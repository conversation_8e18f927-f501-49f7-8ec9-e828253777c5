package com.hnyiti.kuaidi.module.system.service.auth;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 登录失败告警服务
 * 监控登录失败次数，发送告警通知
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LoginFailureAlertService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RestTemplate restTemplate;

    /**
     * 企业微信机器人 Webhook URL
     */
    private static final String WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5c8432d4-d951-42f8-8806-fddfc5171a61";

    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Redis Key 前缀
     */
    private static final String LOGIN_FAIL_IP_PREFIX = "login:fail:ip:";
    private static final String LOGIN_FAIL_USER_PREFIX = "login:fail:user:";
    private static final String LOGIN_ALERT_IP_PREFIX = "login:alert:ip:";
    private static final String LOGIN_ALERT_USER_PREFIX = "login:alert:user:";

    /**
     * 失败次数阈值
     */
    private static final int FAILURE_THRESHOLD = 5;

    /**
     * 监控时间窗口（3分钟）
     */
    private static final int MONITOR_WINDOW_MINUTES = 3;

    /**
     * 告警冷却时间（10分钟）
     */
    private static final int ALERT_COOLDOWN_MINUTES = 10;

    /**
     * 记录登录失败并检查是否需要告警
     *
     * @param username 用户名
     * @param userIp   用户IP
     */
    @Async
    public void recordLoginFailure(String username, String userIp) {
        try {
            // 检查IP失败次数
            checkAndAlertByIp(userIp);
            
            // 检查用户失败次数
            if (StrUtil.isNotBlank(username)) {
                checkAndAlertByUser(username, userIp);
            }
            
        } catch (Exception e) {
            log.error("[recordLoginFailure][记录登录失败异常] username:{} ip:{}", username, userIp, e);
        }
    }

    /**
     * 检查IP失败次数并告警
     *
     * @param userIp 用户IP
     */
    private void checkAndAlertByIp(String userIp) {
        String failKey = LOGIN_FAIL_IP_PREFIX + userIp;
        String alertKey = LOGIN_ALERT_IP_PREFIX + userIp;
        
        // 增加失败次数
        Long failCount = stringRedisTemplate.opsForValue().increment(failKey);
        
        // 设置过期时间（首次设置）
        if (failCount == 1) {
            stringRedisTemplate.expire(failKey, MONITOR_WINDOW_MINUTES, TimeUnit.MINUTES);
        }
        
        // 检查是否达到告警阈值
        if (failCount >= FAILURE_THRESHOLD) {
            // 检查是否在冷却期内
            if (!stringRedisTemplate.hasKey(alertKey)) {
                // 发送告警
                sendIpFailureAlert(userIp, failCount.intValue());
                
                // 设置告警冷却期
                stringRedisTemplate.opsForValue().set(alertKey, "1", ALERT_COOLDOWN_MINUTES, TimeUnit.MINUTES);
            }
        }
    }

    /**
     * 检查用户失败次数并告警
     *
     * @param username 用户名
     * @param userIp   用户IP
     */
    private void checkAndAlertByUser(String username, String userIp) {
        String failKey = LOGIN_FAIL_USER_PREFIX + username;
        String alertKey = LOGIN_ALERT_USER_PREFIX + username;
        
        // 增加失败次数
        Long failCount = stringRedisTemplate.opsForValue().increment(failKey);
        
        // 设置过期时间（首次设置）
        if (failCount == 1) {
            stringRedisTemplate.expire(failKey, MONITOR_WINDOW_MINUTES, TimeUnit.MINUTES);
        }
        
        // 检查是否达到告警阈值
        if (failCount >= FAILURE_THRESHOLD) {
            // 检查是否在冷却期内
            if (!stringRedisTemplate.hasKey(alertKey)) {
                // 发送告警
                sendUserFailureAlert(username, userIp, failCount.intValue());
                
                // 设置告警冷却期
                stringRedisTemplate.opsForValue().set(alertKey, "1", ALERT_COOLDOWN_MINUTES, TimeUnit.MINUTES);
            }
        }
    }

    /**
     * 发送IP失败告警
     *
     * @param userIp    用户IP
     * @param failCount 失败次数
     */
    private void sendIpFailureAlert(String userIp, int failCount) {
        try {
            String message = buildIpFailureMessage(userIp, failCount);
            sendToWeChat(message);
            
            log.warn("[sendIpFailureAlert][IP登录失败告警] ip:{} failCount:{}", userIp, failCount);
            
        } catch (Exception e) {
            log.error("[sendIpFailureAlert][发送IP失败告警异常] ip:{} failCount:{}", userIp, failCount, e);
        }
    }

    /**
     * 发送用户失败告警
     *
     * @param username  用户名
     * @param userIp    用户IP
     * @param failCount 失败次数
     */
    private void sendUserFailureAlert(String username, String userIp, int failCount) {
        try {
            String message = buildUserFailureMessage(username, userIp, failCount);
            sendToWeChat(message);
            
            log.warn("[sendUserFailureAlert][用户登录失败告警] username:{} ip:{} failCount:{}", 
                    username, userIp, failCount);
            
        } catch (Exception e) {
            log.error("[sendUserFailureAlert][发送用户失败告警异常] username:{} ip:{} failCount:{}", 
                    username, userIp, failCount, e);
        }
    }

    /**
     * 构建IP失败告警消息
     *
     * @param userIp    用户IP
     * @param failCount 失败次数
     * @return 告警消息
     */
    private String buildIpFailureMessage(String userIp, int failCount) {
        String alertTime = LocalDateTime.now().format(FORMATTER);
        
        StringBuilder message = new StringBuilder();
        message.append("# 🚨 登录安全告警\n\n");
        message.append("---\n\n");
        message.append("## ⚠️ **IP异常登录检测**\n\n");
        message.append("### 🌐 异常信息\n");
        message.append("- **IP地址：** `").append(userIp).append("`\n");
        message.append("- **失败次数：** ").append(failCount).append(" 次\n");
        message.append("- **时间窗口：** ").append(MONITOR_WINDOW_MINUTES).append(" 分钟内\n");
        message.append("- **告警时间：** ⏰ ").append(alertTime).append("\n\n");
        
        message.append("### 🔍 风险分析\n");
        message.append("> **检测到同一IP地址短时间内多次登录失败**\n");
        message.append("> - 可能存在暴力破解攻击\n");
        message.append("> - 建议立即检查并考虑封禁该IP\n");
        message.append("> - 加强密码策略和验证码机制\n\n");
        
        message.append("---\n");
        message.append("### 📋 建议措施\n");
        message.append("1. 🚫 临时封禁该IP地址\n");
        message.append("2. 🔍 检查相关用户账户安全\n");
        message.append("3. 📊 分析攻击模式和来源\n");
        message.append("4. 🛡️ 加强防护策略\n\n");
        
        message.append("---\n");
        message.append("*快递云系统安全中心 - 自动告警*");
        
        return message.toString();
    }

    /**
     * 构建用户失败告警消息
     *
     * @param username  用户名
     * @param userIp    用户IP
     * @param failCount 失败次数
     * @return 告警消息
     */
    private String buildUserFailureMessage(String username, String userIp, int failCount) {
        String alertTime = LocalDateTime.now().format(FORMATTER);
        
        StringBuilder message = new StringBuilder();
        message.append("# 🚨 登录安全告警\n\n");
        message.append("---\n\n");
        message.append("## ⚠️ **账户异常登录检测**\n\n");
        message.append("### 👤 异常信息\n");
        message.append("- **用户账号：** `").append(username).append("`\n");
        message.append("- **来源IP：** `").append(userIp).append("`\n");
        message.append("- **失败次数：** ").append(failCount).append(" 次\n");
        message.append("- **时间窗口：** ").append(MONITOR_WINDOW_MINUTES).append(" 分钟内\n");
        message.append("- **告警时间：** ⏰ ").append(alertTime).append("\n\n");
        
        message.append("### 🔍 风险分析\n");
        message.append("> **检测到同一账户短时间内多次登录失败**\n");
        message.append("> - 可能存在密码暴力破解\n");
        message.append("> - 账户可能已被恶意攻击\n");
        message.append("> - 建议立即锁定账户并通知用户\n\n");
        
        message.append("---\n");
        message.append("### 📋 建议措施\n");
        message.append("1. 🔒 临时锁定该用户账户\n");
        message.append("2. 📞 联系用户确认登录行为\n");
        message.append("3. 🔑 要求用户重置密码\n");
        message.append("4. 📱 建议启用双因子认证\n\n");
        
        message.append("---\n");
        message.append("*快递云系统安全中心 - 自动告警*");
        
        return message.toString();
    }

    /**
     * 发送消息到企业微信
     *
     * @param message 消息内容
     */
    private void sendToWeChat(String message) {
        try {
            // 构建请求体 - 使用 Markdown 格式
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("msgtype", "markdown");
            
            Map<String, String> markdownContent = new HashMap<>();
            markdownContent.put("content", message);
            requestBody.put("markdown", markdownContent);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            // 发送请求
            String response = restTemplate.postForObject(WEBHOOK_URL, requestEntity, String.class);
            
            log.debug("[sendToWeChat][企业微信告警响应] response:{}", response);
            
        } catch (Exception e) {
            log.error("[sendToWeChat][发送企业微信告警失败] message:{}", message, e);
            throw e;
        }
    }

    /**
     * 清除用户的失败记录（登录成功时调用）
     *
     * @param username 用户名
     * @param userIp   用户IP
     */
    public void clearFailureRecord(String username, String userIp) {
        try {
            // 清除IP失败记录
            if (StrUtil.isNotBlank(userIp)) {
                stringRedisTemplate.delete(LOGIN_FAIL_IP_PREFIX + userIp);
            }
            
            // 清除用户失败记录
            if (StrUtil.isNotBlank(username)) {
                stringRedisTemplate.delete(LOGIN_FAIL_USER_PREFIX + username);
            }
            
        } catch (Exception e) {
            log.error("[clearFailureRecord][清除失败记录异常] username:{} ip:{}", username, userIp, e);
        }
    }
}
