<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnyiti.kuaidi.module.pgsql.dal.mapper.notification.NotificationRecordMapper">

    <!-- 根据订单ID和通知类型查询最新记录 -->
    <select id="selectLatestByOrderIdAndType" resultType="com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationRecord">
        SELECT *
        FROM notification_record
        WHERE order_id = #{orderId}
          AND tenant_id = #{tenantId}
          AND notification_type = #{notificationType}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据订单ID查询所有通知记录 -->
    <select id="selectByOrderId" resultType="com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationRecord">
        SELECT *
        FROM notification_record
        WHERE order_id = #{orderId}
          AND tenant_id = #{tenantId}
        ORDER BY create_time ASC
    </select>

    <!-- 统计订单的成功通知数量 -->
    <select id="countSuccessNotifications" resultType="int">
        SELECT COUNT(*)
        FROM notification_record
        WHERE order_id = #{orderId}
          AND tenant_id = #{tenantId}
          AND send_status = 'SUCCESS'
          AND notification_type IN
        <foreach collection="notificationTypes" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
    </select>

</mapper>
