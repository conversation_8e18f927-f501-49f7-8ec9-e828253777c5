package com.hnyiti.kuaidi.module.pgsql.service.notification.voice;

import com.alibaba.fastjson.JSON;
import com.hnyiti.kuaidi.module.pgsql.service.notification.voice.config.DahanVoiceConfig;
import com.hnyiti.kuaidi.module.pgsql.service.notification.voice.model.DahanVoiceRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 大汉三通语音服务
 * 基于大汉三通语音接口手册实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DahanVoiceService {

    @Autowired
    private DahanVoiceConfig dahanVoiceConfig;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 发送文本语音通话
     *
     * @param mobile 手机号码
     * @param text   语音内容
     * @param msgId  消息ID
     * @return 发送结果
     */
    public Map<String, Object> sendTextCall(String mobile, String text, String msgId) {
        log.info("[sendTextCall] 发送文本语音通话，手机号: {}, 消息ID: {}", mobile, msgId);

        try {
            // 构造请求参数
            DahanVoiceRequest request = buildTextCallRequest(mobile, text, msgId);

            // 发送HTTP请求
            Map<String, Object> response = sendHttpRequest(request);

            log.info("[sendTextCall] 语音通话发送完成，手机号: {}, 消息ID: {}, 响应: {}",
                    mobile, msgId, JSON.toJSONString(response));

            return response;

        } catch (Exception e) {
            log.error("[sendTextCall] 发送文本语音通话异常，手机号: {}, 消息ID: {}", mobile, msgId, e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("result", "DH:9999");
            errorResponse.put("desc", "系统异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 发送验证码语音通话
     *
     * @param mobile     手机号码
     * @param verifyCode 验证码
     * @param msgId      消息ID
     * @return 发送结果
     */
    public Map<String, Object> sendVerifyCodeCall(String mobile, String verifyCode, String msgId) {
        log.info("[sendVerifyCodeCall] 发送验证码语音通话，手机号: {}, 消息ID: {}", mobile, msgId);

        try {
            // 构造请求参数
            DahanVoiceRequest request = buildVerifyCodeRequest(mobile, verifyCode, msgId);

            // 发送HTTP请求
            Map<String, Object> response = sendHttpRequest(request);

            log.info("[sendVerifyCodeCall] 验证码语音发送完成，手机号: {}, 消息ID: {}, 响应: {}",
                    mobile, msgId, JSON.toJSONString(response));

            return response;

        } catch (Exception e) {
            log.error("[sendVerifyCodeCall] 发送验证码语音通话异常，手机号: {}, 消息ID: {}", mobile, msgId, e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("result", "DH:9999");
            errorResponse.put("desc", "系统异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取语音通话状态报告
     *
     * @return 状态报告
     */
    public Map<String, Object> getVoiceReport() {
        log.info("[getVoiceReport] 获取语音通话状态报告");

        try {
            // 构造获取报告的请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("account", dahanVoiceConfig.getAccount());
            requestBody.put("password", dahanVoiceConfig.getPassword());

            // 发送HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("User-Agent", "Kuaidi-Voice-Client/1.0");

            HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(requestBody), headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    dahanVoiceConfig.getReportUrl(),
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            Map<String, Object> result = JSON.parseObject(response.getBody(), Map.class);

            log.info("[getVoiceReport] 获取状态报告完成，响应: {}", JSON.toJSONString(result));

            return result;

        } catch (Exception e) {
            log.error("[getVoiceReport] 获取语音通话状态报告异常", e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("result", "DH:9999");
            errorResponse.put("desc", "获取状态报告异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 检查服务是否可用
     *
     * @return 是否可用
     */
    public boolean isServiceAvailable() {
        try {
            // 通过获取状态报告来检查服务可用性
            Map<String, Object> response = getVoiceReport();
            String result = (String) response.get("result");

            // 如果返回成功或者账号相关错误（说明服务可用），则认为服务可用
            return "DH:0000".equals(result) ||
                    "DH:1001".equals(result) ||
                    "DH:1002".equals(result);

        } catch (Exception e) {
            log.error("[isServiceAvailable] 检查服务可用性异常", e);
            return false;
        }
    }

    /**
     * 构造文本语音通话请求
     */
    private DahanVoiceRequest buildTextCallRequest(String mobile, String text, String msgId) {
        DahanVoiceRequest request = new DahanVoiceRequest();
        request.setAccount(dahanVoiceConfig.getAccount());
        request.setPassword(dahanVoiceConfig.getPassword());

        // 构造消息数据
        Map<String, Object> messageData = new HashMap<>();
        messageData.put("msgid", msgId);
        messageData.put("callee", mobile);
        messageData.put("text", text);
        messageData.put("calltype", 0); // 文本呼叫
        messageData.put("playmode", 0); // 只播放文本
        messageData.put("playtimes", 1); // 播放1次
        messageData.put("speakper", "B0"); // 使用汉小美发音

        request.setData(Arrays.asList(messageData));

        return request;
    }

    /**
     * 构造验证码语音通话请求
     */
    private DahanVoiceRequest buildVerifyCodeRequest(String mobile, String verifyCode, String msgId) {
        DahanVoiceRequest request = new DahanVoiceRequest();
        request.setAccount(dahanVoiceConfig.getAccount());
        request.setPassword(dahanVoiceConfig.getPassword());

        // 构造消息数据
        Map<String, Object> messageData = new HashMap<>();
        messageData.put("msgid", msgId);
        messageData.put("callee", mobile);
        messageData.put("text", verifyCode);
        messageData.put("calltype", 1); // 验证码呼叫
        messageData.put("playmode", 0); // 只播放文本

        request.setData(Arrays.asList(messageData));

        return request;
    }

    /**
     * 发送HTTP请求
     */
    private Map<String, Object> sendHttpRequest(DahanVoiceRequest request) {
        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("User-Agent", "Kuaidi-Voice-Client/1.0");

            // 构造请求体
            String requestBody = JSON.toJSONString(request);
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);

            log.debug("[sendHttpRequest] 发送请求到大汉三通，URL: {}, Body: {}",
                    dahanVoiceConfig.getSubmitUrl(), requestBody);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    dahanVoiceConfig.getSubmitUrl(),
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            // 解析响应
            String responseBody = response.getBody();
            Map<String, Object> result = JSON.parseObject(responseBody, Map.class);

            log.debug("[sendHttpRequest] 收到大汉三通响应: {}", responseBody);

            return result;

        } catch (Exception e) {
            log.error("[sendHttpRequest] 发送HTTP请求异常", e);
            throw new RuntimeException("发送HTTP请求失败", e);
        }
    }
}
