package com.hnyiti.kuaidi.module.system.dal.mysql.tenant;

import com.hnyiti.kuaidi.framework.common.pojo.PageResult;
import com.hnyiti.kuaidi.framework.mybatis.core.mapper.BaseMapperX;
import com.hnyiti.kuaidi.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hnyiti.kuaidi.module.system.api.tenant.dto.TenantPageVO;
import com.hnyiti.kuaidi.module.system.api.tenant.dto.TenantQueryVO;
import com.hnyiti.kuaidi.module.system.dal.dataobject.tenant.TenantDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 租户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantMapper extends BaseMapperX<TenantDO> {

    default PageResult<TenantDO> selectPage(TenantPageVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TenantDO>()
                .likeIfPresent(TenantDO::getName, reqVO.getName())
                .likeIfPresent(TenantDO::getContactName, reqVO.getContactName())
                .likeIfPresent(TenantDO::getContactMobile, reqVO.getContactMobile())
                .eqIfPresent(TenantDO::getStatus, reqVO.getStatus())
                .eqIfPresent(TenantDO::getId, reqVO.getId())
                .orderByDesc(TenantDO::getId));
    }

    default List<TenantDO> selectList(TenantQueryVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TenantDO>()
                .likeIfPresent(TenantDO::getName, reqVO.getName())
                .likeIfPresent(TenantDO::getContactName, reqVO.getContactName())
                .likeIfPresent(TenantDO::getContactMobile, reqVO.getContactMobile())
                .eqIfPresent(TenantDO::getStatus, reqVO.getStatus())
                .orderByDesc(TenantDO::getId));
    }

    default TenantDO selectByName(String name) {
        return selectOne(TenantDO::getName, name);
    }

    default TenantDO selectByDomain(String domain) {
        return selectOne(TenantDO::getDomain, domain);
    }

    default Long selectCountByPackageId(Long packageId) {
        return selectCount(TenantDO::getPackageId, packageId);
    }

    default List<TenantDO> selectListByPackageId(Long packageId) {
        return selectList(TenantDO::getPackageId, packageId);
    }
}
