package com.hnyiti.kuaidi.module.pgsql.controller.notification;

import com.hnyiti.kuaidi.framework.common.pojo.CommonResult;
import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationRecord;
import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.service.notification.CompensationNotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多阶段通知测试控制器
 * 用于测试和验证多阶段通知系统的功能
 *
 * <AUTHOR>
 */
@Tag(name = "多阶段通知测试", description = "多阶段通知系统测试接口")
@RestController
@RequestMapping("/pgsql/notification/test")
@Slf4j
public class NotificationTestController {

    @Autowired
    private CompensationNotificationService compensationNotificationService;

    @Operation(summary = "启动测试通知流程", description = "测试启动多阶段补差通知流程")
    @PostMapping("/start")
    public CommonResult<String> startTestNotification(
            @Parameter(description = "订单ID") @RequestParam String orderId,
            @Parameter(description = "租户ID") @RequestParam Long tenantId,
            @Parameter(description = "补差金额") @RequestParam BigDecimal compensationAmount,
            @Parameter(description = "用户openid") @RequestParam(required = false) String openid,
            @Parameter(description = "用户手机号") @RequestParam(required = false) String mobile) {

        try {
            log.info("[startTestNotification] 启动测试通知流程，订单ID: {}, 租户ID: {}, 补差金额: {}", 
                    orderId, tenantId, compensationAmount);

            // 构造用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("openid", openid != null ? openid : "test_openid_" + System.currentTimeMillis());
            userInfo.put("mobile", mobile != null ? mobile : "13800138000");
            userInfo.put("orderId", orderId);
            userInfo.put("tenantId", tenantId);
            userInfo.put("templateId", "test_template");

            // 启动通知流程
            compensationNotificationService.startCompensationNotification(
                    orderId, tenantId, compensationAmount, userInfo);

            return CommonResult.success("测试通知流程启动成功");

        } catch (Exception e) {
            log.error("[startTestNotification] 启动测试通知流程失败", e);
            return CommonResult.error(500, "启动测试通知流程失败: " + e.getMessage());
        }
    }

    @Operation(summary = "取消测试通知流程", description = "测试取消多阶段补差通知流程")
    @PostMapping("/cancel")
    public CommonResult<String> cancelTestNotification(
            @Parameter(description = "订单ID") @RequestParam String orderId,
            @Parameter(description = "租户ID") @RequestParam Long tenantId) {

        try {
            log.info("[cancelTestNotification] 取消测试通知流程，订单ID: {}, 租户ID: {}", orderId, tenantId);

            // 取消通知流程
            compensationNotificationService.cancelCompensationNotification(orderId, tenantId);

            return CommonResult.success("测试通知流程取消成功");

        } catch (Exception e) {
            log.error("[cancelTestNotification] 取消测试通知流程失败", e);
            return CommonResult.error(500, "取消测试通知流程失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询通知记录", description = "查询订单的通知记录")
    @GetMapping("/records")
    public CommonResult<List<NotificationRecord>> getNotificationRecords(
            @Parameter(description = "订单ID") @RequestParam String orderId,
            @Parameter(description = "租户ID") @RequestParam Long tenantId) {

        try {
            List<NotificationRecord> records = compensationNotificationService.getNotificationRecords(orderId, tenantId);
            return CommonResult.success(records);

        } catch (Exception e) {
            log.error("[getNotificationRecords] 查询通知记录失败", e);
            return CommonResult.error(500, "查询通知记录失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询通知任务", description = "查询订单的通知任务")
    @GetMapping("/tasks")
    public CommonResult<List<NotificationTask>> getNotificationTasks(
            @Parameter(description = "订单ID") @RequestParam String orderId,
            @Parameter(description = "租户ID") @RequestParam Long tenantId) {

        try {
            List<NotificationTask> tasks = compensationNotificationService.getNotificationTasks(orderId, tenantId);
            return CommonResult.success(tasks);

        } catch (Exception e) {
            log.error("[getNotificationTasks] 查询通知任务失败", e);
            return CommonResult.error(500, "查询通知任务失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询通知统计", description = "查询订单的通知统计信息")
    @GetMapping("/statistics")
    public CommonResult<Map<String, Object>> getNotificationStatistics(
            @Parameter(description = "订单ID") @RequestParam String orderId,
            @Parameter(description = "租户ID") @RequestParam Long tenantId) {

        try {
            Map<String, Object> statistics = compensationNotificationService.getNotificationStatistics(orderId, tenantId);
            return CommonResult.success(statistics);

        } catch (Exception e) {
            log.error("[getNotificationStatistics] 查询通知统计失败", e);
            return CommonResult.error(500, "查询通知统计失败: " + e.getMessage());
        }
    }

    @Operation(summary = "健康检查", description = "检查多阶段通知服务是否正常")
    @GetMapping("/health")
    public CommonResult<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("service", "CompensationNotificationService");
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        health.put("version", "1.0.0");
        
        return CommonResult.success(health);
    }
}
