# 多阶段消息推送系统集成指南

## 📋 集成概述

多阶段消息推送系统已经在 `pgsql-biz` 模块中实现，现在需要将其集成到现有的订单处理流程中。

## 🎯 主要入口点

### 1. 启动多阶段通知流程

**入口位置**: `OrderCallbackServiceImpl.makeUpReminder()`
**文件**: `hnyiti-module-drp/hnyiti-module-drp-kuaidi/hnyiti-module-drp-kuaidi-biz/src/main/java/com/hnyiti/kuaidi/module/delivery/service/order/callback/OrderCallbackServiceImpl.java`
**行数**: 1734-1760

```java
/**
 * 超重补差提醒
 */
private void makeUpReminder(UpdateFeesRepVO updateOrder, OrderDO orderDO, BigDecimal payAmount, BigDecimal calcFeeWeight) {
    // ==================== 新增：启动多阶段通知流程 ====================
    startMultiStageNotification(orderDO, updateOrder);

    // ==================== 保留原有逻辑（兼容性） ====================
    // 原有的小程序和公众号通知逻辑...
}
```

### 2. 取消多阶段通知流程

**入口位置**: `OrderSubmitServiceImpl.compensationFinish()`
**文件**: `hnyiti-module-drp/hnyiti-module-drp-kuaidi/hnyiti-module-drp-kuaidi-biz/src/main/java/com/hnyiti/kuaidi/module/delivery/service/order/OrderSubmitServiceImpl.java`
**行数**: 2633-2634

```java
// ==================== 新增：取消多阶段通知流程 ====================
cancelMultiStageNotification(orderBigVo.getOrderInfo());
```

## 🔧 完整集成步骤

### 步骤 1: 添加依赖

在 `hnyiti-module-drp-kuaidi-biz` 模块的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>com.hnyiti.kuaidi</groupId>
    <artifactId>hnyiti-module-pgsql-biz</artifactId>
    <version>${revision}</version>
</dependency>
```

### 步骤 2: ✅ 已完成 - 启用多阶段通知服务

在 `OrderCallbackServiceImpl` 和 `OrderSubmitServiceImpl` 中已添加依赖注入：

```java
@Lazy
@Resource
private CompensationNotificationService compensationNotificationService;
```

### 步骤 3: ✅ 已完成 - 集成代码已启用

#### 在 `OrderCallbackServiceImpl.startMultiStageNotification()` 中：

```java
// ✅ 已启用
compensationNotificationService.startCompensationNotification(
    orderDO.getId(),
    orderDO.getTenantId(),
    updateOrder.getCompensationAmount(),
    userInfo
);
```

#### 在 `OrderSubmitServiceImpl.cancelMultiStageNotification()` 中：

```java
// ✅ 已启用
compensationNotificationService.cancelCompensationNotification(
    orderDO.getId(),
    orderDO.getTenantId()
);
```

### 步骤 4: 数据库初始化

执行数据库建表脚本：

```sql
-- 执行文件：hnyiti-module-pgsql/hnyiti-module-pgsql-biz/src/main/resources/sql/notification_tables.sql
```

### 步骤 5: 配置文件设置

在应用配置文件中添加：

```yaml
# 引入通知配置
spring:
  profiles:
    include:
      - notification

# 多阶段通知配置
notification:
  compensation:
    delays:
      sms: 2h        # 短信延时2小时
      phone: 6h      # 电话在短信后6小时
    work-time:
      start: "09:00"
      end: "23:59"
    retry:
      max-attempts: 3
      interval: 30m
    channels:
      mini-program: true
      wechat-mp: true
      sms: true
      phone-call: true
```

## 🔄 业务流程说明

### 触发时机

1. **启动通知**: 当快递公司回调订单超重需要补差时
   - 触发位置: `OrderCallbackServiceImpl.makeUpReminder()`
   - 触发条件: 订单状态变为超重补差

2. **取消通知**: 当用户完成补差支付时
   - 触发位置: `OrderSubmitServiceImpl.compensationFinish()`
   - 触发条件: 补差支付成功

### 通知阶段

1. **立即通知** (0分钟): 小程序 + 公众号
2. **延时短信** (2小时后): 短信通知 (工作时间限制)
3. **延时电话** (8小时后): 电话通知 (需前序通知完成)

## 🛡️ 容错机制

### 异常处理
- 多阶段通知的异常不会影响主业务流程
- 所有通知操作都有 try-catch 包装
- 失败的通知会自动重试（最多3次）

### 兼容性保证
- 保留原有的通知逻辑，确保向后兼容
- 新旧通知系统可以并行运行
- 可以通过配置开关控制各个通知渠道

## 📊 监控和统计

### 查询接口

```java
// 查询订单的通知记录
List<NotificationRecord> records = compensationNotificationService.getNotificationRecords(orderId, tenantId);

// 查询订单的通知任务
List<NotificationTask> tasks = compensationNotificationService.getNotificationTasks(orderId, tenantId);

// 获取通知统计信息
Map<String, Object> statistics = compensationNotificationService.getNotificationStatistics(orderId, tenantId);
```

### 统计指标

- 各类型通知的发送成功率
- 通知费用统计（短信、电话）
- 流程完整性统计
- 延时准确性监控

## 🚀 部署建议

### 分阶段部署

1. **第一阶段**: 部署代码但不启用，观察系统稳定性
2. **第二阶段**: 启用立即通知（小程序+公众号）
3. **第三阶段**: 启用延时短信通知
4. **第四阶段**: 启用电话通知

### 配置建议

```yaml
# 生产环境建议配置
notification:
  compensation:
    delays:
      sms: 2h        # 可根据业务需求调整
      phone: 6h      # 可根据业务需求调整
    work-time:
      start: "09:00" # 根据客服工作时间调整
      end: "22:00"   # 避免深夜打扰用户
    retry:
      max-attempts: 3
      interval: 30m
    channels:
      mini-program: true
      wechat-mp: true
      sms: true      # 可根据成本考虑
      phone-call: false # 初期可关闭，后续开启
```

## ⚠️ 注意事项

1. **成本控制**: 短信和电话通知会产生费用，需要监控成本
2. **频率限制**: 避免对同一用户发送过多通知
3. **工作时间**: 短信和电话通知受工作时间限制
4. **数据清理**: 定期清理过期的通知记录和任务
5. **监控告警**: 建议配置通知失败率告警

## 🔍 故障排查

### 常见问题

1. **通知未发送**: 检查渠道配置和服务可用性
2. **延时不准确**: 检查消息队列配置和延时级别
3. **重复通知**: 检查订单状态和前序通知检查逻辑
4. **费用异常**: 检查第三方服务计费逻辑

### 日志关键字

- `[startMultiStageNotification]`: 启动通知流程
- `[cancelMultiStageNotification]`: 取消通知流程
- `[executeNotification]`: 执行通知任务
- `[sendNotification]`: 发送通知

通过这些关键字可以快速定位问题和追踪通知流程。
