package com.hnyiti.kuaidi.module.pgsql.service.notification.channel;

import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.service.notification.model.NotificationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信公众号通知渠道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WechatMpNotificationChannel implements NotificationChannel {

    @Override
    public NotificationResult send(NotificationTask task) {
        log.info("[send] 发送公众号通知，任务ID: {}, 订单ID: {}", task.getId(), task.getOrderId());

        try {
            // 从任务数据中获取必要信息
            Map<String, Object> taskData = task.getTaskData();
            String openid = (String) taskData.get("openid");
            String templateId = (String) taskData.get("templateId");
            
            if (openid == null || templateId == null) {
                return NotificationResult.failure("缺少必要参数：openid或templateId");
            }

            // TODO: 调用微信公众号模板消息API
            // 这里需要集成现有的微信公众号消息发送服务
            
            // 构造消息内容
            String content = buildMessageContent(taskData);
            
            // 模拟发送成功
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("msgid", "mock_msgid_" + System.currentTimeMillis());
            responseData.put("errcode", 0);
            responseData.put("errmsg", "ok");
            
            log.info("[send] 公众号通知发送成功，任务ID: {}, openid: {}", task.getId(), openid);
            
            return NotificationResult.success(openid, content, responseData);

        } catch (Exception e) {
            log.error("[send] 发送公众号通知异常，任务ID: {}", task.getId(), e);
            return NotificationResult.failure("发送异常: " + e.getMessage());
        }
    }

    @Override
    public boolean isAvailable() {
        // TODO: 检查微信公众号服务是否可用
        return true;
    }

    @Override
    public String getChannelType() {
        return NotificationTask.TaskType.WECHAT_MP.getCode();
    }

    /**
     * 构造消息内容
     */
    private String buildMessageContent(Map<String, Object> taskData) {
        // 从任务数据中提取订单信息构造消息内容
        String orderId = (String) taskData.get("orderId");
        Object compensationAmount = taskData.get("compensationAmount");
        
        return String.format("【补差通知】您的订单 %s 需要补差费用 %s 元，请点击查看详情。", 
                orderId, compensationAmount != null ? compensationAmount.toString() : "0");
    }
}
