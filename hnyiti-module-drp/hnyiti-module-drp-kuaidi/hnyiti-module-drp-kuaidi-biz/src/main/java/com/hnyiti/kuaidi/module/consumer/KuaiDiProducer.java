package com.hnyiti.kuaidi.module.consumer;

import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;

public interface KuaiDiProducer {
    String ORDER_FEE_OUTPUT = "order-fee-output";

    String ORDER_ASSEMBLY_OUTPUT = "order-assembly-output";

    //v3版本异步风控计算
    String ORDER_RISK_OUTPUT = "order-risk-output";

    //v3版本下单异常退款
    String ORDER_REFUND_OUTPUT = "order-refund-output";

    //v3版本订单分佣
    String ORDER_KICKBACK_OUTPUT = "order-kickback-output";

    // 订单支付补偿
    String ORDER_PAYCOMPENSATION_OUTPUT = "order-paycompensation-output";

    // 用户余额支付补偿
    String BALANCE_RECHARGE_COMPENSATION_OUTPUT = "balance-recharge-compensation-output";

    // 订单上报coreData服务
    String ORDER_REPORTED_OUTPUT = "order-reported-output";

    // 查价差额记录日志息队列
    String KD_DIFFERENCE_OUTPUT = "kd-difference-output";

    // 分佣上报队列
    String REBATE_REPORTED_OUTPUT = "rebate-reported-output";

    // 分享分享
    String COMMISSION_REBATE_OUTPUT = "commission-rebate-output";

    String KD_EXPORT_OUTPUT = "kd-export-output";

    String WECOM_NOTIFY_OUTPUT = "wecom-notify-output";

    //有赞
    String YOUZAN_OUTPUT = "youzan-output";

    //有赞
    String ORDER_CALLBACK_REPEAT_OUTPUT = "order-callback-repeat-output";

    /**
     * 总部对账单
     */
    String ORDER_BILLING_OUTPUT = "order-billing-output";

    String USER_MERGE_OUTPUT = "user-merge-output";

    String SEND_MESSAGE_OUTPUT = "send-message-output";

    // 订单回调消息
    String ORDER_CALLBACK_OUTPUT = "order-callback-output";

    String ORDER_SYNC_PGSQL_OUPUT= "order-sync-pgsql-output";

    // 电话催缴补差费用
    String COMPENSATION_IMMEDIATE_OUTPUT = "compensation-immediate-output";

    @Output(ORDER_FEE_OUTPUT)
    MessageChannel orderFeeOutput();

    @Output(ORDER_ASSEMBLY_OUTPUT)
    MessageChannel orderAssemblyOutput();

    @Output(ORDER_RISK_OUTPUT)
    MessageChannel orderRiskOutput();

    @Output(ORDER_REFUND_OUTPUT)
    MessageChannel orderRefundOutput();

    @Output(ORDER_KICKBACK_OUTPUT)
    MessageChannel calculationKickback();

    // 支付补偿
    @Output(ORDER_PAYCOMPENSATION_OUTPUT)
    MessageChannel orderPayCompensation();

    // 用户余额充值补偿
    @Output(BALANCE_RECHARGE_COMPENSATION_OUTPUT)
    MessageChannel balanceRechargeCompensation();

    // 查价差额记录日志息队列
    @Output(KD_DIFFERENCE_OUTPUT)
    MessageChannel orderDifferenceOutput();

    // 订单上报coreData服务
    @Output(ORDER_REPORTED_OUTPUT)
    MessageChannel orderReported();

    // 分佣上报队列
    @Output(REBATE_REPORTED_OUTPUT)
    MessageChannel rebateReported();

    // 分享分佣消息生产者
    @Output(COMMISSION_REBATE_OUTPUT)
    MessageChannel commissionRebateOutput();

    // 导出
    @Output(KD_EXPORT_OUTPUT)
    MessageChannel kdExportOutput();

    @Output(WECOM_NOTIFY_OUTPUT)
    MessageChannel overweightNotifyOutput();

    @Output(ORDER_BILLING_OUTPUT)
    MessageChannel orderBilling();

    @Output(YOUZAN_OUTPUT)
    MessageChannel youZanOutput();


    @Output(ORDER_CALLBACK_REPEAT_OUTPUT)
    MessageChannel orderCallbackRepeatOutput();


    @Output(USER_MERGE_OUTPUT)
    MessageChannel userMergeOutput();


    @Output(SEND_MESSAGE_OUTPUT)
    MessageChannel sendMessageOutPut();

    /**
     * 订单回调消息通道
     */
    @Output(ORDER_CALLBACK_OUTPUT)
    MessageChannel orderCallbackOutput();

    @Output(ORDER_SYNC_PGSQL_OUPUT)
    MessageChannel orderSyncPgsqlOutput();

    @Output(COMPENSATION_IMMEDIATE_OUTPUT)
    MessageChannel compensationImmediateOutput();

}
