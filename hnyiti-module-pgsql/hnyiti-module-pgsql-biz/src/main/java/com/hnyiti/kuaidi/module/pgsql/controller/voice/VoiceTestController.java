package com.hnyiti.kuaidi.module.pgsql.controller.voice;

import com.hnyiti.kuaidi.framework.common.pojo.CommonResult;
import com.hnyiti.kuaidi.module.pgsql.service.notification.voice.DahanVoiceService;
import com.hnyiti.kuaidi.module.pgsql.service.notification.voice.config.DahanVoiceConfig;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 语音通知测试控制器
 *
 * <AUTHOR>
 */
@Tag(name = "语音通知测试", description = "大汉三通语音通知测试接口")
@RestController
@RequestMapping("/pgsql/voice/test")
@Slf4j
public class VoiceTestController {

    @Autowired
    private DahanVoiceService dahanVoiceService;

    @Autowired
    private DahanVoiceConfig dahanVoiceConfig;

    @Operation(summary = "发送测试语音通话", description = "测试大汉三通文本语音通话功能")
    @PostMapping("/text-call")
    public CommonResult<Map<String, Object>> sendTestTextCall(
            @Parameter(description = "手机号码") @RequestParam String mobile,
            @Parameter(description = "语音内容") @RequestParam(required = false) String text) {

        try {
            log.info("[sendTestTextCall] 发送测试语音通话，手机号: {}", mobile);

            // 使用默认测试内容
            if (text == null || text.isEmpty()) {
                text = "您好，您的有一笔快递订单需要补差，请登录小程序快递助手查看，如需帮助请联系小程序客服。";
            }

            String msgId = "TEST_" + System.currentTimeMillis();
            Map<String, Object> result = dahanVoiceService.sendTextCall(mobile, text, msgId);

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("[sendTestTextCall] 发送测试语音通话失败", e);
            return CommonResult.error(500, "发送测试语音通话失败: " + e.getMessage());
        }
    }

    @Operation(summary = "发送测试验证码语音", description = "测试大汉三通验证码语音功能")
    @PostMapping("/verify-code")
    public CommonResult<Map<String, Object>> sendTestVerifyCode(
            @Parameter(description = "手机号码") @RequestParam String mobile,
            @Parameter(description = "验证码") @RequestParam(required = false) String code) {

        try {
            log.info("[sendTestVerifyCode] 发送测试验证码语音，手机号: {}", mobile);

            // 使用默认测试验证码
            if (code == null || code.isEmpty()) {
                code = "123456";
            }

            // 验证码长度检查
            if (code.length() < 4 || code.length() > 8) {
                return CommonResult.error(400, "验证码长度必须在4-8位之间");
            }

            String msgId = "VERIFY_" + System.currentTimeMillis();
            Map<String, Object> result = dahanVoiceService.sendVerifyCodeCall(mobile, code, msgId);

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("[sendTestVerifyCode] 发送测试验证码语音失败", e);
            return CommonResult.error(500, "发送测试验证码语音失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取语音状态报告", description = "获取大汉三通语音通话状态报告")
    @GetMapping("/report")
    public CommonResult<Map<String, Object>> getVoiceReport() {
        try {
            log.info("[getVoiceReport] 获取语音状态报告");

            Map<String, Object> result = dahanVoiceService.getVoiceReport();

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("[getVoiceReport] 获取语音状态报告失败", e);
            return CommonResult.error(500, "获取语音状态报告失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检查语音服务状态", description = "检查大汉三通语音服务是否可用")
    @GetMapping("/health")
    public CommonResult<Map<String, Object>> checkVoiceHealth() {
        Map<String, Object> health = new HashMap<>();

        try {
            // 检查配置
            boolean configValid = dahanVoiceConfig.isConfigValid();
            health.put("configValid", configValid);
            health.put("configSummary", dahanVoiceConfig.getConfigSummary());

            // 检查服务可用性
            boolean serviceAvailable = dahanVoiceService.isServiceAvailable();
            health.put("serviceAvailable", serviceAvailable);

            // 整体状态
            boolean healthy = configValid && serviceAvailable;
            health.put("status", healthy ? "UP" : "DOWN");
            health.put("timestamp", System.currentTimeMillis());

            return CommonResult.success(health);

        } catch (Exception e) {
            log.error("[checkVoiceHealth] 检查语音服务状态失败", e);

            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", System.currentTimeMillis());

            return CommonResult.error(500, "检查语音服务状态失败", health);
        }
    }

    @Operation(summary = "获取语音配置信息", description = "获取当前语音服务配置信息（脱敏）")
    @GetMapping("/config")
    public CommonResult<Map<String, Object>> getVoiceConfig() {
        Map<String, Object> config = new HashMap<>();

        config.put("account", dahanVoiceConfig.getAccount());
        config.put("submitUrl", dahanVoiceConfig.getSubmitUrl());
        config.put("reportUrl", dahanVoiceConfig.getReportUrl());
        config.put("enabled", dahanVoiceConfig.isEnabled());
        config.put("defaultSpeaker", dahanVoiceConfig.getDefaultSpeaker());
        config.put("defaultPlayTimes", dahanVoiceConfig.getDefaultPlayTimes());
        config.put("maxRetryTimes", dahanVoiceConfig.getMaxRetryTimes());
        config.put("connectTimeout", dahanVoiceConfig.getConnectTimeout());
        config.put("readTimeout", dahanVoiceConfig.getReadTimeout());

        return CommonResult.success(config);
    }
}
