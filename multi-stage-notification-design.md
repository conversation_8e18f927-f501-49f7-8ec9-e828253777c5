# 多阶段消息推送系统设计方案

## 📋 需求分析

### 核心业务场景
这是一个**超重补差通知的多阶段推送系统**，当快递公司回调通知订单超重需要补差时，系统需要按照时间顺序和优先级向用户发送不同类型的通知。

### 业务流程图

```mermaid
flowchart TD
    START([快递公司回调]) --> PARSE[解析订单数据]

    subgraph IMMEDIATE["第一阶段：立即通知"]
        PARSE --> MP[发送小程序通知]
        MP --> WC[发送公众号通知]
        WC --> SAVE1[保存通知记录]
        SAVE1 --> SCHEDULE[安排延时任务]
    end

    subgraph SMS_PHASE["第二阶段：短信通知决策"]
        SCHEDULE --> SMS_TRIGGER{短信任务触发<br/>2小时后}
        SMS_TRIGGER --> CHECK1{检查订单状态}
        CHECK1 -->|已补差| CANCEL[取消后续任务]
        CHECK1 -->|未补差| TIME1{当前时间检查<br/>9:00-24:00?}
        TIME1 -->|否| RESCHEDULE1[重新安排到<br/>下个工作时间]
        TIME1 -->|是| SEND_SMS[发送短信]
        SEND_SMS --> SAVE2[保存短信记录]
        RESCHEDULE1 --> SMS_TRIGGER
    end

    subgraph CALL_PHASE["第三阶段：电话通知决策"]
        SAVE2 --> CALL_TRIGGER{电话任务触发<br/>短信后6小时}
        CALL_TRIGGER --> CHECK2{检查订单状态}
        CHECK2 -->|已补差| END1[结束流程]
        CHECK2 -->|未补差| CHECK_PREV{检查前序通知}
        CHECK_PREV --> VERIFY{小程序+公众号+短信<br/>都完成了吗?}
        VERIFY -->|否| WAIT[等待前序通知完成]
        VERIFY -->|是| TIME2{当前时间检查<br/>9:00-24:00?}
        TIME2 -->|否| RESCHEDULE2[重新安排到<br/>下个工作时间]
        TIME2 -->|是| SEND_CALL[发起电话通知]
        SEND_CALL --> SAVE3[保存电话记录]
        SAVE3 --> END2[结束流程]
        RESCHEDULE2 --> CALL_TRIGGER
        WAIT --> CALL_TRIGGER
    end

    CANCEL --> END3[流程结束]
    END1 --> END3
    END2 --> END3
    END3 --> FINAL([通知流程完成])
```

### 业务特点
1. **多阶段推送**: 小程序 → 公众号 → 短信 → 电话，逐步升级通知强度
2. **时间控制**: 短信和电话有工作时间限制（9:00-24:00）
3. **状态检查**: 每个阶段都需要检查订单是否已完成补差
4. **依赖关系**: 电话通知需要前序通知都完成
5. **智能调度**: 超出工作时间自动延期到下个时段

## 🏗️ 技术架构设计

### 1. 整体架构方案

```mermaid
graph TB
    subgraph "消息推送系统架构"
        A[快递回调接口] --> B[消息调度器]
        B --> C[延时任务引擎]
        C --> D[通知执行器]
        D --> E[通知渠道适配器]
        E --> F[通知记录服务]

        G[订单状态检查器] --> D
        H[工作时间检查器] --> D
        I[前序通知检查器] --> D
    end
```

### 2. 核心组件设计

#### A. 消息调度器 (NotificationScheduler)
```java
@Component
public class NotificationScheduler {

    /**
     * 启动补差通知流程
     */
    public void startCompensationNotificationFlow(String orderId, Long tenantId) {
        // 1. 立即发送小程序和公众号通知
        sendImmediateNotifications(orderId, tenantId);

        // 2. 安排延时短信任务（2小时后）
        scheduleDelayedSmsTask(orderId, tenantId, 2, TimeUnit.HOURS);

        // 3. 安排延时电话任务（短信后6小时）
        scheduleDelayedCallTask(orderId, tenantId, 8, TimeUnit.HOURS); // 2+6=8小时
    }
}
```

#### B. 延时任务引擎 (DelayedTaskEngine)
基于现有的 XXL-Job 或 RocketMQ 延时消息实现

```java
@Component
public class DelayedTaskEngine {

    /**
     * 安排延时任务
     */
    public void scheduleTask(NotificationTask task, long delay, TimeUnit timeUnit) {
        // 方案1: 使用 RocketMQ 延时消息
        sendDelayedMessage(task, delay, timeUnit);

        // 方案2: 使用 XXL-Job 动态任务
        // createDynamicJob(task, delay, timeUnit);

        // 方案3: 使用 Redis 延时队列
        // addToDelayQueue(task, delay, timeUnit);
    }
}
```

#### C. 通知执行器 (NotificationExecutor)
```java
@Component
public class NotificationExecutor {

    /**
     * 执行通知任务
     */
    public void executeNotification(NotificationTask task) {
        // 1. 检查订单状态
        if (isOrderCompensated(task.getOrderId())) {
            cancelSubsequentTasks(task.getOrderId());
            return;
        }

        // 2. 检查工作时间（短信和电话）
        if (needWorkTimeCheck(task.getType()) && !isWorkTime()) {
            rescheduleToNextWorkTime(task);
            return;
        }

        // 3. 检查前序通知（电话）
        if (task.getType() == PHONE_CALL && !isPreviousNotificationsCompleted(task.getOrderId())) {
            rescheduleTask(task, 30, TimeUnit.MINUTES); // 30分钟后重试
            return;
        }

        // 4. 执行通知
        NotificationResult result = channelAdapter.sendNotification(task);

        // 5. 保存通知记录
        notificationRecordService.saveRecord(task, result);
    }
}
```

### 3. 数据模型设计

#### A. 通知任务表 (notification_task)
```sql
CREATE TABLE notification_task (
    id BIGINT PRIMARY KEY,
    order_id VARCHAR(64) NOT NULL,
    tenant_id BIGINT NOT NULL,
    task_type VARCHAR(32) NOT NULL,  -- MINI_PROGRAM, WECHAT_MP, SMS, PHONE_CALL
    task_status VARCHAR(32) NOT NULL, -- PENDING, EXECUTING, COMPLETED, CANCELLED, FAILED
    scheduled_time DATETIME NOT NULL, -- 计划执行时间
    actual_execute_time DATETIME,     -- 实际执行时间
    retry_count INT DEFAULT 0,        -- 重试次数
    max_retry_count INT DEFAULT 3,    -- 最大重试次数
    next_retry_time DATETIME,         -- 下次重试时间
    task_data JSON,                   -- 任务数据（收件人、内容等）
    result_data JSON,                 -- 执行结果数据
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_order_tenant (order_id, tenant_id),
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_status_type (task_status, task_type)
);
```

#### B. 通知记录表 (notification_record)
```sql
CREATE TABLE notification_record (
    id BIGINT PRIMARY KEY,
    order_id VARCHAR(64) NOT NULL,
    tenant_id BIGINT NOT NULL,
    notification_type VARCHAR(32) NOT NULL, -- MINI_PROGRAM, WECHAT_MP, SMS, PHONE_CALL
    recipient VARCHAR(128) NOT NULL,         -- 接收人（openid、手机号等）
    content TEXT,                           -- 通知内容
    send_status VARCHAR(32) NOT NULL,       -- SUCCESS, FAILED, PENDING
    send_time DATETIME,                     -- 发送时间
    response_data JSON,                     -- 第三方响应数据
    error_message VARCHAR(512),             -- 错误信息
    cost_amount DECIMAL(10,4),              -- 费用（短信、电话）
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_order_tenant (order_id, tenant_id),
    INDEX idx_type_status (notification_type, send_status),
    INDEX idx_send_time (send_time)
);
```

## 🔄 实现方案详解

### 1. 延时任务实现方案对比

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| **RocketMQ延时消息** | 高可靠性、支持18个延时级别 | 延时级别固定、不够灵活 | 固定延时场景 |
| **XXL-Job动态任务** | 灵活、可视化管理 | 复杂度高、资源消耗大 | 复杂调度场景 |
| **Redis延时队列** | 简单、延时精确 | 可靠性相对较低 | 简单延时场景 |
| **数据库轮询** | 简单可靠 | 性能较低、实时性差 | 对实时性要求不高 |

**推荐方案**: **RocketMQ延时消息 + 数据库轮询补偿**

### 2. 工作时间检查实现

```java
@Component
public class WorkTimeChecker {

    /**
     * 检查是否在工作时间内（9:00-24:00）
     */
    public boolean isWorkTime() {
        LocalTime now = LocalTime.now();
        return now.isAfter(LocalTime.of(9, 0)) && now.isBefore(LocalTime.of(24, 0));
    }

    /**
     * 计算下一个工作时间
     */
    public LocalDateTime getNextWorkTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalTime currentTime = now.toLocalTime();

        if (currentTime.isBefore(LocalTime.of(9, 0))) {
            // 当前时间早于9点，安排到今天9点
            return now.toLocalDate().atTime(9, 0);
        } else {
            // 当前时间晚于24点，安排到明天9点
            return now.toLocalDate().plusDays(1).atTime(9, 0);
        }
    }
}
```

### 3. 订单状态检查实现

```java
@Component
public class OrderStatusChecker {

    /**
     * 检查订单是否已完成补差
     */
    public boolean isOrderCompensated(String orderId) {
        // 查询最新的补差记录
        OrderCompensateDO compensate = orderCompensateService.getLatestByOrderId(orderId);

        if (compensate == null) {
            return false;
        }

        // 检查补差结果是否为已完成
        return OrderConstants.ORDER_COMPENSATING_RESULT_THREE.equals(compensate.getCompensatingResult());
    }
}
```

### 4. 前序通知检查实现

```java
@Component
public class PreviousNotificationChecker {

    /**
     * 检查前序通知是否都已完成
     */
    public boolean isPreviousNotificationsCompleted(String orderId) {
        List<String> requiredTypes = Arrays.asList(
            NotificationType.MINI_PROGRAM.name(),
            NotificationType.WECHAT_MP.name(),
            NotificationType.SMS.name()
        );

        for (String type : requiredTypes) {
            NotificationRecord record = notificationRecordService.getLatestByOrderIdAndType(orderId, type);
            if (record == null || !NotificationStatus.SUCCESS.equals(record.getSendStatus())) {
                return false;
            }
        }

        return true;
    }
}
```

## 📨 消息队列设计

### 1. 消息主题设计

```yaml
# 立即通知消息
IMMEDIATE_NOTIFICATION_TOPIC:
  - compensation-immediate-notify

# 延时通知消息
DELAYED_NOTIFICATION_TOPIC:
  - compensation-delayed-notify

# 任务重新调度消息
RESCHEDULE_NOTIFICATION_TOPIC:
  - compensation-reschedule-notify
```

### 2. 消息体设计

```java
@Data
public class NotificationMessage {
    private String orderId;
    private Long tenantId;
    private NotificationType type;
    private NotificationAction action; // SEND, RESCHEDULE, CANCEL
    private Long delayTime;           // 延时时间（毫秒）
    private Map<String, Object> data; // 扩展数据
    private Integer retryCount;       // 重试次数
}
```

## 🛡️ 容错与监控

### 1. 容错机制

- **重试机制**: 每种通知类型支持最大3次重试
- **降级策略**: 第三方服务不可用时记录失败，不阻塞流程
- **补偿机制**: 定时任务扫描失败的通知进行补偿
- **熔断机制**: 连续失败时暂停该渠道通知

### 2. 监控指标

- **通知成功率**: 按渠道统计成功率
- **延时准确性**: 任务执行时间与计划时间的偏差
- **流程完整性**: 完整走完四个阶段的订单比例
- **成本统计**: 短信和电话的费用统计

## 🔧 配置管理

### 1. 通知配置

```yaml
notification:
  compensation:
    # 延时配置
    delays:
      sms: 2h        # 短信延时2小时
      phone: 6h      # 电话在短信后6小时

    # 工作时间配置
    work-time:
      start: "09:00"
      end: "24:00"

    # 重试配置
    retry:
      max-attempts: 3
      interval: 30m

    # 渠道开关
    channels:
      mini-program: true
      wechat-mp: true
      sms: true
      phone-call: true
```

### 2. 模板配置

```java
@ConfigurationProperties(prefix = "notification.templates")
@Data
public class NotificationTemplateConfig {
    private Map<String, String> miniProgram;
    private Map<String, String> wechatMp;
    private Map<String, String> sms;
    private Map<String, String> phoneCall;
}
```

## 📈 扩展性考虑

### 1. 新增通知渠道

通过策略模式设计，新增渠道只需：
1. 实现 `NotificationChannel` 接口
2. 注册到 `ChannelAdapter`
3. 配置相应的模板和参数

### 2. 自定义延时策略

支持按租户、订单类型等维度自定义延时时间和通知策略。

### 3. 多语言支持

模板系统支持国际化，根据用户语言偏好发送对应语言的通知。

## 🎯 实施建议

### 1. 分阶段实施

**第一阶段**: 实现基础的多阶段通知功能
- 立即通知（小程序+公众号）
- 延时短信通知
- 基础的状态检查

**第二阶段**: 完善高级功能
- 电话通知
- 工作时间控制
- 前序通知检查

**第三阶段**: 优化和扩展
- 监控告警
- 性能优化
- 多语言支持

### 2. 技术选型建议

- **延时任务**: RocketMQ延时消息（主） + XXL-Job（补偿）
- **数据存储**: MySQL（主） + Redis（缓存）
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

### 3. 风险控制

- **限流保护**: 防止消息轰炸
- **成本控制**: 短信和电话费用监控
- **降级开关**: 紧急情况下可关闭特定渠道
- **数据备份**: 重要通知记录的备份策略

这个设计方案具备良好的可扩展性、容错性和监控能力，能够满足复杂的多阶段通知需求，同时保证系统的稳定性和可维护性。
