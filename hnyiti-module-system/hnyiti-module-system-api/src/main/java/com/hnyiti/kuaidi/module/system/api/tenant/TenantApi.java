package com.hnyiti.kuaidi.module.system.api.tenant;

import com.hnyiti.kuaidi.framework.common.pojo.CommonResult;
import com.hnyiti.kuaidi.module.system.api.tenant.dto.TenantDataSourceConfigRespDTO;
import com.hnyiti.kuaidi.module.system.api.tenant.dto.TenantRespVO;
import com.hnyiti.kuaidi.module.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =`
public interface TenantApi {
    String PREFIX = ApiConstants.PREFIX + "/tenant";

    @GetMapping(PREFIX + "/id-list")
    CommonResult<List<Long>> getTenantIdList();

    @GetMapping(PREFIX + "/valid")
    CommonResult<Boolean> validTenant(@RequestParam("id") Long id);

    /**
     * 获得租户的数据源配置
     *
     * @param tenantId 租户编号
     * @return 数据源配置
     */
    @GetMapping(PREFIX + "/getTenantDataSourceConfig")
    TenantDataSourceConfigRespDTO getTenantDataSourceConfig(@RequestParam("id") Long tenantId);

    @GetMapping(PREFIX + "/getTenantDataSourceList")
    CommonResult<List<TenantDataSourceConfigRespDTO>> getTenantDataSourceList();

    @GetMapping(PREFIX + "/getTenantInfo")
    CommonResult<TenantRespVO> getTenantInfo(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/getTenantByDomain")
    CommonResult<TenantRespVO> getTenantByDomain(@RequestParam("domain") String domain);

    /**
     * 代理商扣款
     *
     * @return
     */
    @PostMapping(PREFIX + "/deductTenantAmount")
    CommonResult deductTenantAmount(@RequestParam("orderId") String orderId,
                                    @RequestParam(value = "tenantAmount") BigDecimal tenantAmount,
                                    @RequestParam("scene") String scene,
                                    @RequestParam(value = "mbrId", required = false) Long mbrId,
                                    @RequestParam(value = "remark", required = false) String remark);

    /**
     * 扣费 扣除用户的余额和加盟商的余额
     * @param mbrId    会员id
     * @param mbrAmount    会员金额
     * @param tenantAmount 加盟商金额
     * @param scene        场景
     * @return 返回日志的id
     */
    @PostMapping(PREFIX + "/deduct")
    CommonResult<Long> deduct(@RequestParam("mbrId") Long mbrId, @RequestParam("money") BigDecimal mbrAmount, @RequestParam(value = "tenantAmount",required = false) BigDecimal tenantAmount, @RequestParam("scene") String scene, @RequestParam("remark") String remark);

    /**
     * 费用充值
     *
     * @param type
     * @param amount
     * @param payOrderId
     */
    @PostMapping(PREFIX + "/rechargeTenantAmount")
    CommonResult rechargeTenantAmount(@RequestParam("type") String type, @RequestParam("amount") Integer amount, @RequestParam("payOrderId") Long payOrderId);

    @PostMapping(PREFIX + "/updateSmsLogSendStatusByOrderId")
    CommonResult updateSmsLogSendStatusByOrderId(@RequestParam("orderId") String orderId);

    /**
     * 用户扣款
     *
     * @param userId
     * @param orderId
     * @param payAmount
     * @param type
     * @param remark
     * @return
     */
    @PostMapping(PREFIX + "/deductUserAmount")
    CommonResult deductUserAmount(@RequestParam("userId") Long userId, @RequestParam(value = "orderId",required = false) String orderId
            , @RequestParam(value = "batchOrderId",required = false) Long batchOrderId, @RequestParam(value = "payAmount") BigDecimal payAmount, @RequestParam("type") String type, @RequestParam("remark") String remark);

    /**
     * 用户余额退款
     *
     * @param userId
     * @param orderId
     * @param payAmount
     * @param type
     * @param remark
     * @return
     */
    @PostMapping(PREFIX + "/refundUserAmount")
    CommonResult refundUserAmount(@RequestParam("userId") Long userId, @RequestParam("orderId") String orderId, @RequestParam(value = "payAmount") BigDecimal payAmount, @RequestParam("type") String type, @RequestParam("remark") String remark);


    /**
     * 用户余额退款
     * @return
     */
    @GetMapping(PREFIX + "/getTenantList")
    CommonResult<List<TenantRespVO>> getTenantList();

}
