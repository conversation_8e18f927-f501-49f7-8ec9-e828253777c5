package com.hnyiti.kuaidi.module.pgsql.service.notification.channel;

import com.alibaba.fastjson.JSON;
import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.service.notification.model.NotificationResult;
import com.hnyiti.kuaidi.module.pgsql.service.notification.voice.DahanVoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 电话通知渠道 - 基于大汉三通语音接口
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PhoneCallNotificationChannel implements NotificationChannel {

    @Autowired
    private DahanVoiceService dahanVoiceService;

    @Override
    public NotificationResult send(NotificationTask task) {
        log.info("[send] 发起电话通知，任务ID: {}, 订单ID: {}", task.getId(), task.getOrderId());

        try {
            // 从任务数据中获取必要信息
            Map<String, Object> taskData = task.getTaskData();
            String mobile = (String) taskData.get("mobile");

            if (mobile == null) {
                return NotificationResult.failure("缺少必要参数：mobile");
            }

            // 构造语音通知内容
            String content = buildVoiceContent(taskData);

            // 调用大汉三通语音接口发送语音通知
            Map<String, Object> voiceResult = dahanVoiceService.sendTextCall(mobile, content, task.getId().toString());

            // 解析响应结果
            String resultCode = (String) voiceResult.get("result");
            boolean isSuccess = "DH:0000".equals(resultCode);

            if (isSuccess) {
                // 发送成功
                BigDecimal cost = calculateCallCost(); // 计算通话费用

                Map<String, Object> responseData = new HashMap<>();
                responseData.put("result", resultCode);
                responseData.put("desc", voiceResult.get("desc"));
                responseData.put("msgid", task.getId().toString());
                responseData.put("mobile", mobile);
                responseData.put("provider", "大汉三通");

                log.info("[send] 电话通知发起成功，任务ID: {}, mobile: {}, 费用: {}", task.getId(), mobile, cost);

                return NotificationResult.success(mobile, content, cost).setResponseData(responseData);

            } else {
                // 发送失败
                String errorDesc = (String) voiceResult.get("desc");
                log.warn("[send] 电话通知发起失败，任务ID: {}, mobile: {}, 错误: {}", task.getId(), mobile, errorDesc);

                return NotificationResult.failure("语音通知发送失败: " + errorDesc, voiceResult);
            }

        } catch (Exception e) {
            log.error("[send] 发起电话通知异常，任务ID: {}", task.getId(), e);
            return NotificationResult.failure("发起异常: " + e.getMessage());
        }
    }

    @Override
    public boolean isAvailable() {
        try {
            return dahanVoiceService.isServiceAvailable();
        } catch (Exception e) {
            log.error("[isAvailable] 检查大汉三通语音服务可用性异常", e);
            return false;
        }
    }

    @Override
    public String getChannelType() {
        return NotificationTask.TaskType.PHONE_CALL.getCode();
    }

    /**
     * 构造语音通知内容
     */
    private String buildVoiceContent(Map<String, Object> taskData) {
        String orderId = (String) taskData.get("orderId");
        Object compensationAmount = taskData.get("compensationAmount");

        // 构造适合语音播报的内容
        String orderNo = extractOrderNo(orderId);
        String amount = compensationAmount != null ? compensationAmount.toString() : "0";

        return String.format("您好，您的快递订单%s需要补差%s元，请及时处理。如需帮助请联系客服。",
                orderNo, amount);
    }

    /**
     * 提取订单号（去掉前缀，便于语音播报）
     */
    private String extractOrderNo(String orderId) {
        if (orderId == null) {
            return "未知";
        }

        // 如果订单ID过长，只播报后几位
        if (orderId.length() > 8) {
            return orderId.substring(orderId.length() - 8);
        }

        return orderId;
    }

    /**
     * 计算通话费用
     */
    private BigDecimal calculateCallCost() {
        // 根据实际费用标准计算，这里使用固定费用
        return new BigDecimal("0.15"); // 每次通话0.15元
    }
}
