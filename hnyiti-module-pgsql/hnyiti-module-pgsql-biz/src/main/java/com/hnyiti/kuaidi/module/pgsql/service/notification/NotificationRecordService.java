package com.hnyiti.kuaidi.module.pgsql.service.notification;

import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationRecord;
import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.dal.mapper.notification.NotificationRecordMapper;
import com.hnyiti.kuaidi.module.pgsql.service.notification.model.NotificationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 通知记录服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class NotificationRecordService {

    @Autowired
    private NotificationRecordMapper notificationRecordMapper;

    /**
     * 保存通知记录
     *
     * @param task   通知任务
     * @param result 发送结果
     */
    public void saveRecord(NotificationTask task, NotificationResult result) {
        log.info("[saveRecord] 保存通知记录，任务ID: {}, 订单ID: {}, 类型: {}, 结果: {}",
                task.getId(), task.getOrderId(), task.getTaskType(), result.isSuccess() ? "成功" : "失败");

        try {
            NotificationRecord record = new NotificationRecord();
            record.setOrderId(task.getOrderId());
            record.setTenantId(task.getTenantId());
            record.setNotificationType(task.getTaskType());
            record.setRecipient(result.getRecipient());
            record.setContent(result.getContent());
            record.setSendStatus(result.isSuccess() ?
                    NotificationRecord.SendStatus.SUCCESS.getCode() :
                    NotificationRecord.SendStatus.FAILED.getCode());
            record.setSendTime(result.getSendTime());
            record.setResponseData(result.getResponseData());
            record.setErrorMessage(result.getErrorMessage());
            // 不再记录费用
            record.setCreateTime(LocalDateTime.now());

            notificationRecordMapper.insert(record);

            log.info("[saveRecord] 通知记录保存成功，记录ID: {}", record.getId());

        } catch (Exception e) {
            log.error("[saveRecord] 保存通知记录异常，任务ID: {}", task.getId(), e);
            // 记录保存失败不影响主流程
        }
    }

    /**
     * 根据订单ID和通知类型查询最新记录
     *
     * @param orderId          订单ID
     * @param tenantId         租户ID
     * @param notificationType 通知类型
     * @return 通知记录
     */
    public NotificationRecord getLatestByOrderIdAndType(String orderId, Long tenantId, String notificationType) {
        return notificationRecordMapper.selectLatestByOrderIdAndType(orderId, tenantId, notificationType);
    }

    /**
     * 根据订单ID查询所有通知记录
     *
     * @param orderId  订单ID
     * @param tenantId 租户ID
     * @return 通知记录列表
     */
    public List<NotificationRecord> getByOrderId(String orderId, Long tenantId) {
        return notificationRecordMapper.selectByOrderId(orderId, tenantId);
    }

    /**
     * 统计订单的成功通知数量
     *
     * @param orderId           订单ID
     * @param tenantId          租户ID
     * @param notificationTypes 通知类型列表
     * @return 成功通知数量
     */
    public int countSuccessNotifications(String orderId, Long tenantId, List<String> notificationTypes) {
        return notificationRecordMapper.countSuccessNotifications(orderId, tenantId, notificationTypes);
    }

    /**
     * 检查通知是否成功
     *
     * @param orderId          订单ID
     * @param tenantId         租户ID
     * @param notificationType 通知类型
     * @return 是否成功
     */
    public boolean isNotificationSuccess(String orderId, Long tenantId, String notificationType) {
        NotificationRecord record = getLatestByOrderIdAndType(orderId, tenantId, notificationType);
        return record != null && NotificationRecord.SendStatus.SUCCESS.getCode().equals(record.getSendStatus());
    }
}
