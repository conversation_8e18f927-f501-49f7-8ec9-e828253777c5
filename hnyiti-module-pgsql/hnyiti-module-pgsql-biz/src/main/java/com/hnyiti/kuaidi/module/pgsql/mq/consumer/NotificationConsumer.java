package com.hnyiti.kuaidi.module.pgsql.mq.consumer;

import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.dal.mapper.notification.NotificationTaskMapper;
import com.hnyiti.kuaidi.module.pgsql.mq.message.NotificationMessage;
import com.hnyiti.kuaidi.module.pgsql.service.notification.NotificationExecutor;
import com.hnyiti.kuaidi.module.pgsql.service.notification.NotificationScheduler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

/**
 * 通知消息消费者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NotificationConsumer {

    @Autowired
    private NotificationTaskMapper notificationTaskMapper;

    @Autowired
    private NotificationExecutor notificationExecutor;

    @Autowired
    private NotificationScheduler notificationScheduler;

    /**
     * 消费立即通知消息
     */
    @StreamListener("compensation-immediate-notify")
    public void handleImmediateNotification(Message<NotificationMessage> message) {
        NotificationMessage notificationMessage = message.getPayload();
        
        log.info("[handleImmediateNotification] 接收到立即通知消息，任务ID: {}, 订单ID: {}, 动作: {}", 
                notificationMessage.getTaskId(), notificationMessage.getOrderId(), notificationMessage.getAction());

        try {
            switch (notificationMessage.getAction()) {
                case SEND:
                    handleSendAction(notificationMessage);
                    break;
                case CANCEL:
                    handleCancelAction(notificationMessage);
                    break;
                default:
                    log.warn("[handleImmediateNotification] 不支持的动作类型: {}", notificationMessage.getAction());
            }

        } catch (Exception e) {
            log.error("[handleImmediateNotification] 处理立即通知消息异常，任务ID: {}", 
                    notificationMessage.getTaskId(), e);
        }
    }

    /**
     * 消费延时通知消息
     */
    @StreamListener("compensation-delayed-notify")
    public void handleDelayedNotification(Message<NotificationMessage> message) {
        NotificationMessage notificationMessage = message.getPayload();
        
        log.info("[handleDelayedNotification] 接收到延时通知消息，任务ID: {}, 订单ID: {}, 动作: {}", 
                notificationMessage.getTaskId(), notificationMessage.getOrderId(), notificationMessage.getAction());

        try {
            switch (notificationMessage.getAction()) {
                case SEND:
                    handleSendAction(notificationMessage);
                    break;
                case RESCHEDULE:
                    handleRescheduleAction(notificationMessage);
                    break;
                default:
                    log.warn("[handleDelayedNotification] 不支持的动作类型: {}", notificationMessage.getAction());
            }

        } catch (Exception e) {
            log.error("[handleDelayedNotification] 处理延时通知消息异常，任务ID: {}", 
                    notificationMessage.getTaskId(), e);
        }
    }

    /**
     * 消费重新调度消息
     */
    @StreamListener("compensation-reschedule-notify")
    public void handleRescheduleNotification(Message<NotificationMessage> message) {
        NotificationMessage notificationMessage = message.getPayload();
        
        log.info("[handleRescheduleNotification] 接收到重新调度消息，任务ID: {}, 订单ID: {}", 
                notificationMessage.getTaskId(), notificationMessage.getOrderId());

        try {
            handleSendAction(notificationMessage);

        } catch (Exception e) {
            log.error("[handleRescheduleNotification] 处理重新调度消息异常，任务ID: {}", 
                    notificationMessage.getTaskId(), e);
        }
    }

    /**
     * 处理发送动作
     */
    private void handleSendAction(NotificationMessage message) {
        if (message.getTaskId() == null) {
            log.warn("[handleSendAction] 任务ID为空，跳过处理");
            return;
        }

        // 查询任务详情
        NotificationTask task = notificationTaskMapper.selectById(message.getTaskId());
        if (task == null) {
            log.warn("[handleSendAction] 任务不存在，任务ID: {}", message.getTaskId());
            return;
        }

        // 检查任务状态
        if (!NotificationTask.TaskStatus.PENDING.getCode().equals(task.getTaskStatus())) {
            log.info("[handleSendAction] 任务状态不是待执行，跳过处理，任务ID: {}, 状态: {}", 
                    task.getId(), task.getTaskStatus());
            return;
        }

        // 执行通知
        notificationExecutor.executeNotification(task);
    }

    /**
     * 处理取消动作
     */
    private void handleCancelAction(NotificationMessage message) {
        log.info("[handleCancelAction] 处理取消动作，订单ID: {}", message.getOrderId());
        
        // 取消订单的后续任务
        notificationScheduler.cancelSubsequentTasks(message.getOrderId(), message.getTenantId());
    }

    /**
     * 处理重新调度动作
     */
    private void handleRescheduleAction(NotificationMessage message) {
        log.info("[handleRescheduleAction] 处理重新调度动作，任务ID: {}", message.getTaskId());
        
        // 重新调度逻辑与发送动作相同
        handleSendAction(message);
    }
}
