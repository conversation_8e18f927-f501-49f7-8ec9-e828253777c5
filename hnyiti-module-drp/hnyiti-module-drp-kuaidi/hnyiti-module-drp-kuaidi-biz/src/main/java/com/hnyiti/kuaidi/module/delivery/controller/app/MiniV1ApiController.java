package com.hnyiti.kuaidi.module.delivery.controller.app;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hnyiti.kuaidi.framework.common.pojo.CommonResult;
import com.hnyiti.kuaidi.framework.security.core.util.SecurityFrameworkUtils;
import com.hnyiti.kuaidi.framework.tenant.core.context.TenantContextHolder;
import com.hnyiti.kuaidi.module.consumer.KuaiDiProducer;
import com.hnyiti.kuaidi.module.delivery.controller.app.vo.*;
import com.hnyiti.kuaidi.module.delivery.enums.order.OrderDeliverySwitchEnum;
import com.hnyiti.kuaidi.module.member.api.userparam.UserParamConfigApi;
import com.hnyiti.kuaidi.module.member.api.userparam.vo.UserParamConfigVO;
import com.hnyiti.kuaidi.module.message.api.MessageSendApi;
import com.hnyiti.kuaidi.module.message.api.group.vo.NoticeGroupMiniRespVO;
import com.hnyiti.kuaidi.module.system.api.param.ParamApi;
import com.hnyiti.kuaidi.module.system.api.tenant.TenantApi;
import com.hnyiti.kuaidi.module.system.api.tenant.dto.TenantRespVO;
import com.hnyiti.kuaidi.module.system.api.window.SystemWindowVO;
import com.hnyiti.kuaidi.module.system.enums.ConfigConstants;
import com.hnyiti.kuaidi.module.wxmini.dal.dataobject.banner.BannerDO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Tag(name = "小程序 - 公共API接口")
@RestController
@RequestMapping("/mini/v2/api")
@Slf4j
public class MiniV1ApiController {


    @Autowired
    private MiniV2SystemController systemController;

    @Autowired
    private MiniV2BrokerageController brokerageController;

    @Autowired
    private MiniV2MktController mktController;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private MessageSendApi messageSendApi;

    @Resource
    private ParamApi paramApi;

    @Resource
    private UserParamConfigApi userParamConfigApi;

    @Resource
    private KuaiDiProducer mqProducer;

    @PermitAll
    @GetMapping("/messageTest")
    public CommonResult messageTest(@RequestParam("tenantId") String tenantId, @RequestParam("orderId") String orderId, @RequestParam("type") String type) {
        Map<String, String> params = new HashMap<>();
        params.put("tenantId", tenantId);
        params.put("orderId", orderId);
        params.put("type", type);
        mqProducer.orderSyncPgsqlOutput().send(MessageBuilder.withPayload(params)
                .build());

        return CommonResult.success("test");
    }

    @Operation(summary = "获取后台数据", description = "获取小程序初始化所需的公共数据，包括菜单配置、Banner、公众号配置等")
    @Parameters({
            @Parameter(name = "params", description = "请求参数Map，包含token等信息", schema = @Schema(type = "object")),
            @Parameter(name = "tenantId", description = "租户ID", required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    @ApiResponse(responseCode = "200", description = "成功返回后台数据")
    @GetMapping("getBackgroundFetchData/{tenantId}")
    @PermitAll
    public CommonResult<BackgroundFetchDataVo> getBackgroundFetchData(
            @RequestParam(required = false) @Schema(description = "请求参数Map，包含token等信息") Map<String, String> params,
            @Schema(description = "HTTP请求对象") HttpServletRequest request,
            @PathVariable("tenantId") @Schema(description = "租户ID", example = "1") Long tenantId) {
        // log.error("getBackgroundFetchData {}", params);
        BackgroundFetchDataVo result = new BackgroundFetchDataVo();

        // token 格式 ${openId}_${tenantId}
        String token = params.get("token");
//        String[] split = token.split("_");

//        String openId = split[0];
//        Long tenantId = Long.valueOf(split[1]);

        TenantContextHolder.setTenantId(tenantId);

        // 查询菜单信息
        CommonResult<List<MiniMenuConfigVO>> menuConfigListResult = systemController.getMenuConfigList();
        result.setMenuConfigList(menuConfigListResult.getData());

        // 查询广告栏
        CommonResult<List<BannerDO>> bannerResult = systemController.getBanner();
        result.setBanners(bannerResult.getData());

        // 查询公众号配置
        CommonResult<MiniPublicConfigVO> publicConfigResult = systemController.getPublicAccountConfigList();
        result.setPublicConfig(publicConfigResult.getData());

        // 获取通知公告(系统通知)
        CommonResult<SystemWindowVO> noticeByTypeResult = systemController.getSystemWindow(ConfigConstants.MINI_KUAIDI_MININOTICE);
        result.setSystemBulletin(noticeByTypeResult.getData());

        // 小程序中会员相关模块是否展示
        result.setShowVip(systemController.memberModuleShow().getData());

        // 获取小程序客服配置信息
        result.setMemberConfigVo(systemController.miniMemberConfig().getData());

        // 获取客服信息
        CommonResult<MiniCustomServiceVo> customerInfo = systemController.getCustomServiceUrl();
        result.setCustomerInfo(customerInfo.getData());

        // 获取横幅配置数据
        List<BannerDO> list = mktController.getActivitiesShare().getData();
        result.setShareRespVO(list);

        // 个人中心菜单按钮开关
        MiniPersonalCenterConfigVo centerConfigVo = systemController.personalCenterConfig().getData();
        result.setCenterConfig(centerConfigVo);

        // 获取所有用户标识下的用户参数配置列表
        List<UserParamConfigVO> configList = userParamConfigApi.getUserParamConfigListByTenant(tenantId);
        List<MiniPersonalCenterConfigVo> centerConfigVoList = new ArrayList<>();

        // 转换为MiniPersonalCenterConfigVo
        if (configList != null && !configList.isEmpty()) {
            centerConfigVoList = BeanUtil.copyToList(configList, MiniPersonalCenterConfigVo.class);

        }

        result.setCenterConfigVoList(centerConfigVoList);

        // 获取小程序对应公众号是否开启
        result.setMiniProgramsOpen(systemController.miniProgramsOpen().getData());

        // 查询会员升级要求
//        CommonResult<MiniMemAskResVO> miniMemAskResResult = brokerageController.memberAsk();
//        result.setMiniMemAskRes(miniMemAskResResult.getData());

        // 查询用户信息
//        if (openId != null) {
//            // 获取需要用户信息
//
//
//        }

        // 获取登录类型
        result.setMiniFunctionConfigVo(systemController.functionConfig().getData());

        CommonResult<TenantRespVO> tenantInfo = tenantApi.getTenantInfo(tenantId);
        if (ObjectUtils.isNotEmpty(tenantInfo.getData())) {
            TenantRespVO data = tenantInfo.getData();
            // 获取代理商分销开关（是否展示分享按钮）
            result.setMktSwitch(data.getMktSwitch());

            // 获取代理分销模式（1:推新返佣、2:推三反一）  默认1
            result.setMktType(StringUtils.isNotEmpty(data.getMktType()) ? data.getMktType() : "1");
        }

        // 获取优惠卷开关
        String couponStatus = paramApi.getCouponStatus(tenantId);
        result.setCouponSwitch(StringUtils.isNotEmpty(couponStatus) ? couponStatus : "1");

        // 获取新人优惠卷弹窗开关
        String couponNewcomerSwitch = paramApi.getCouponNewcomerSwitch(tenantId);
        result.setCouponNewcomerSwitch(StringUtils.isNotEmpty(couponNewcomerSwitch) ? couponStatus : "1");

        // 获取余额支付开关
        Boolean isOpenBalancePay = paramApi.isOpenBalancePay(tenantId);
        result.setIsOpenBalancePay(isOpenBalancePay);

        // 获取取消原因开关
        String cancelReasonSwitch = paramApi.getCancelReasonSwitch(tenantId);
        result.setCancelReasonSwitch(StringUtils.isNotEmpty(cancelReasonSwitch) ? cancelReasonSwitch : "1");

        // 获取余额充值开关
        String balanceRechargeSwitch = paramApi.getBalanceRechargeSwitch(tenantId);
        result.setBalanceRechargeSwitch(StringUtils.isNotEmpty(balanceRechargeSwitch) ? balanceRechargeSwitch : "1");

        // 获取余额提现开关
        String balanceWithdrawSwitch = paramApi.getBalanceWithdrawSwitch(tenantId);
        result.setBalanceWithdrawSwitch(StringUtils.isNotEmpty(balanceWithdrawSwitch) ? balanceWithdrawSwitch : "1");

        // 获取收益提现开关
        String profitWithdrawSwitch = paramApi.getProfitWithdrawSwitch(tenantId);
        result.setProfitWithdrawSwitch(StringUtils.isNotEmpty(profitWithdrawSwitch) ? profitWithdrawSwitch : "1");

        // 获取是否开启优惠券列表调优开关（自动选择最优的优惠券）
        String couponOptimal = StringUtils.isNotEmpty(paramApi.getCouponOptimal(tenantId)) ?
                paramApi.getCouponOptimal(tenantId) : OrderDeliverySwitchEnum.INACTIVE.getStatus();
        result.setCouponOptimalSwitch(couponOptimal);

        // 会员升级类型
        Boolean memberVipUpType = paramApi.getMiniMemberVipUpType(tenantId);
        result.setMemberVipUpType(memberVipUpType);

        TenantContextHolder.setTenantId(null);
        return CommonResult.success(result);
    }


    @Operation(summary = "获取h5后台数据", description = "获取小程序初始化所需的公共数据，包括菜单配置、Banner、公众号配置等")
    @Parameters({
            @Parameter(name = "params", description = "请求参数Map，包含token等信息", schema = @Schema(type = "object")),
            @Parameter(name = "tenantId", description = "租户ID", required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    @ApiResponse(responseCode = "200", description = "成功返回后台数据")
    @GetMapping("getH5BackgroundFetchData")
    @PermitAll
    public CommonResult<BackgroundFetchDataVo> getH5BackgroundFetchData(
            @RequestHeader(value = "forwardedHost", required = false) String forwardedHost,
            @RequestParam(required = false) @Schema(description = "请求参数Map，包含token等信息") Map<String, String> params,
            @Schema(description = "HTTP请求对象") HttpServletRequest request) {
        // 获取真实的原始域名（处理网关/代理转发的情况）
        log.info("原始请求域名: {}, 当前Host头: {}", forwardedHost, request.getHeader("host"));

        if (ObjectUtil.isEmpty(forwardedHost)) {
            return CommonResult.error(100006, "缺少参数forwardedHost");
        }
        // log.error("getBackgroundFetchData {}", params);
        BackgroundFetchDataVo result = new BackgroundFetchDataVo();

        // 根据域名查询租户信息
        Long tenantId = 196L; // 默认租户ID

        // 尝试通过多种方式获取租户ID
        tenantId = getTenantIdByDomain(forwardedHost);
        if (ObjectUtil.isEmpty(tenantId)) {
            return CommonResult.error(100006, "当前域名未绑定分销商");
        }

        TenantContextHolder.setTenantId(tenantId);


        result.setTenantId(tenantId);

        // 查询菜单信息
        CommonResult<List<MiniMenuConfigVO>> menuConfigListResult = systemController.getMenuConfigList();
        result.setMenuConfigList(menuConfigListResult.getData());

        // 查询广告栏
        CommonResult<List<BannerDO>> bannerResult = systemController.getBanner();
        result.setBanners(bannerResult.getData());

        // 查询公众号配置
        CommonResult<MiniPublicConfigVO> publicConfigResult = systemController.getPublicAccountConfigList();
        result.setPublicConfig(publicConfigResult.getData());

        // 获取通知公告(系统通知)
        CommonResult<SystemWindowVO> noticeByTypeResult = systemController.getSystemWindow(ConfigConstants.MINI_KUAIDI_MININOTICE);
        result.setSystemBulletin(noticeByTypeResult.getData());

        // 小程序中会员相关模块是否展示
        result.setShowVip(systemController.memberModuleShow().getData());

        // 获取小程序客服配置信息
        result.setMemberConfigVo(systemController.miniMemberConfig().getData());

        // 获取客服信息
        CommonResult<MiniCustomServiceVo> customerInfo = systemController.getCustomServiceUrl();
        result.setCustomerInfo(customerInfo.getData());

        // 获取横幅配置数据
        List<BannerDO> list = mktController.getActivitiesShare().getData();
        result.setShareRespVO(list);

        // 个人中心菜单按钮开关
        MiniPersonalCenterConfigVo centerConfigVo = systemController.personalCenterConfig().getData();
        result.setCenterConfig(centerConfigVo);

        // 获取所有用户标识下的用户参数配置列表
        List<UserParamConfigVO> configList = userParamConfigApi.getUserParamConfigListByTenant(tenantId);
        List<MiniPersonalCenterConfigVo> centerConfigVoList = new ArrayList<>();

        // 转换为MiniPersonalCenterConfigVo
        if (configList != null && !configList.isEmpty()) {
            centerConfigVoList = BeanUtil.copyToList(configList, MiniPersonalCenterConfigVo.class);

        }

        result.setCenterConfigVoList(centerConfigVoList);

        // 获取小程序对应公众号是否开启
        result.setMiniProgramsOpen(systemController.miniProgramsOpen().getData());

        // 查询会员升级要求
//        CommonResult<MiniMemAskResVO> miniMemAskResResult = brokerageController.memberAsk();
//        result.setMiniMemAskRes(miniMemAskResResult.getData());

        // 查询用户信息
//        if (openId != null) {
//            // 获取需要用户信息
//
//
//        }

        // 获取登录类型
        result.setMiniFunctionConfigVo(systemController.functionConfig().getData());

        CommonResult<TenantRespVO> tenantInfo = tenantApi.getTenantInfo(tenantId);
        if (ObjectUtils.isNotEmpty(tenantInfo.getData())) {
            TenantRespVO data = tenantInfo.getData();
            // 获取代理商分销开关（是否展示分享按钮）
            result.setMktSwitch(data.getMktSwitch());

            // 获取代理分销模式（1:推新返佣、2:推三反一）  默认1
            result.setMktType(StringUtils.isNotEmpty(data.getMktType()) ? data.getMktType() : "1");
        }

        // 获取优惠卷开关
        String couponStatus = paramApi.getCouponStatus(tenantId);
        result.setCouponSwitch(StringUtils.isNotEmpty(couponStatus) ? couponStatus : "1");

        // 获取新人优惠卷弹窗开关
        String couponNewcomerSwitch = paramApi.getCouponNewcomerSwitch(tenantId);
        result.setCouponNewcomerSwitch(StringUtils.isNotEmpty(couponNewcomerSwitch) ? couponStatus : "1");

        // 获取余额支付开关
        Boolean isOpenBalancePay = paramApi.isOpenBalancePay(tenantId);
        result.setIsOpenBalancePay(isOpenBalancePay);

        // 获取取消原因开关
        String cancelReasonSwitch = paramApi.getCancelReasonSwitch(tenantId);
        result.setCancelReasonSwitch(StringUtils.isNotEmpty(cancelReasonSwitch) ? cancelReasonSwitch : "1");

        // 获取余额充值开关
        String balanceRechargeSwitch = paramApi.getBalanceRechargeSwitch(tenantId);
        result.setBalanceRechargeSwitch(StringUtils.isNotEmpty(balanceRechargeSwitch) ? balanceRechargeSwitch : "1");

        // 获取余额提现开关
        String balanceWithdrawSwitch = paramApi.getBalanceWithdrawSwitch(tenantId);
        result.setBalanceWithdrawSwitch(StringUtils.isNotEmpty(balanceWithdrawSwitch) ? balanceWithdrawSwitch : "1");

        // 获取收益提现开关
        String profitWithdrawSwitch = paramApi.getProfitWithdrawSwitch(tenantId);
        result.setProfitWithdrawSwitch(StringUtils.isNotEmpty(profitWithdrawSwitch) ? profitWithdrawSwitch : "1");

        // 获取是否开启优惠券列表调优开关（自动选择最优的优惠券）
        String couponOptimal = StringUtils.isNotEmpty(paramApi.getCouponOptimal(tenantId)) ?
                paramApi.getCouponOptimal(tenantId) : OrderDeliverySwitchEnum.INACTIVE.getStatus();
        result.setCouponOptimalSwitch(couponOptimal);

        // 会员升级类型
        Boolean memberVipUpType = paramApi.getMiniMemberVipUpType(tenantId);
        result.setMemberVipUpType(memberVipUpType);

        TenantContextHolder.setTenantId(null);
        return CommonResult.success(result);
    }

    /**
     * 获取首页需要鉴权的公共数据
     * @param params
     * @return
     */
    @Operation(summary = "获取公共数据", description = "获取首页需要鉴权的公共数据")
    @Parameter(name = "params", description = "请求参数Map，用于传递额外查询条件", schema = @Schema(type = "object"))
    @ApiResponse(responseCode = "200", description = "成功返回公共数据")
    @GetMapping("getCommonData")
    public CommonResult<MiniCommonDataVo> getBackgroundFetchData(
            @RequestParam(required = false) @Schema(description = "请求参数Map，用于传递额外查询条件") Map<String, String> params) {

        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Long tenantId = TenantContextHolder.getTenantId();

        MiniCommonDataVo result = new MiniCommonDataVo();

        // 是否关注公众号
        result.setIsAttentionMp(systemController.isAttentionMp().getData());

        // 查询会员升级要求
        CommonResult<MiniMemAskResVO> miniMemAskResResult = brokerageController.memberAsk();
        result.setMiniMemAskRes(miniMemAskResResult.getData());


        TenantContextHolder.setTenantId(null);
        return CommonResult.success(result);
    }

    /**
     * 获取当前代理下的所有消息通知模板组以及模板组下的小程序通知code
     * @return
     */
    @Operation(summary = "获取消息通知模板组", description = "获取当前代理下的所有消息通知模板组以及模板组下的小程序通知code")
    @ApiResponse(responseCode = "200", description = "成功返回消息通知模板组")
    @GetMapping("/mini-code")
    public CommonResult<List<NoticeGroupMiniRespVO>> getNoticeGroupAndMiniCode(){
        return messageSendApi.getNoticeGroupAndMiniCode(TenantContextHolder.getTenantId());
    }

    /**
     * 通过多种方式获取租户ID
     *
     * @param originalHost 原始域名
     * @return 租户ID
     */
    private Long getTenantIdByDomain(String originalHost) {
        // 1. 首先尝试通过域名查询
        if (StringUtils.isNotEmpty(originalHost)) {
            try {
                CommonResult<TenantRespVO> tenantResult = tenantApi.getTenantByDomain(originalHost);
                if (tenantResult != null && tenantResult.getData() != null) {
                    log.info("根据域名 {} 找到租户ID: {}", originalHost, tenantResult.getData().getId());
                    return tenantResult.getData().getId();
                } else {
                    log.warn("未找到域名 {} 对应的租户", originalHost);
                }
            } catch (Exception e) {
                log.error("根据域名查询租户失败: {}, 错误: {}", originalHost, e.getMessage());
            }
        }
        return null;
    }

}
