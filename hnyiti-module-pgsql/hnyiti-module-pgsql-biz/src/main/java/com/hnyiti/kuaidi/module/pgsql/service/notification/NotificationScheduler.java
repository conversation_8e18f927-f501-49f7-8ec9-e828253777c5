package com.hnyiti.kuaidi.module.pgsql.service.notification;

import com.hnyiti.kuaidi.module.pgsql.config.NotificationConfig;
import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.dal.mapper.notification.NotificationTaskMapper;
import com.hnyiti.kuaidi.module.pgsql.mq.producer.NotificationProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 消息调度器
 * 负责启动和管理多阶段通知流程
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NotificationScheduler {

    @Autowired
    private NotificationTaskMapper notificationTaskMapper;

    @Autowired
    private NotificationProducer notificationProducer;

    @Autowired
    private NotificationConfig notificationConfig;

    /**
     * 启动补差通知流程
     *
     * @param orderId 订单ID
     * @param tenantId 租户ID
     * @param orderData 订单数据
     */
    public void startCompensationNotificationFlow(String orderId, Long tenantId, Map<String, Object> orderData) {
        log.info("[startCompensationNotificationFlow] 启动补差通知流程，订单ID: {}, 租户ID: {}", orderId, tenantId);

        try {
            // 1. 立即发送小程序和公众号通知
            sendImmediateNotifications(orderId, tenantId, orderData);

            // 2. 安排延时短信任务（2小时后）
            scheduleDelayedSmsTask(orderId, tenantId, orderData);

            // 3. 安排延时电话任务（短信后6小时，总共8小时后）
            scheduleDelayedCallTask(orderId, tenantId, orderData);

            log.info("[startCompensationNotificationFlow] 补差通知流程启动成功，订单ID: {}", orderId);

        } catch (Exception e) {
            log.error("[startCompensationNotificationFlow] 启动补差通知流程失败，订单ID: {}", orderId, e);
            throw new RuntimeException("启动补差通知流程失败", e);
        }
    }

    /**
     * 发送立即通知（小程序 + 公众号）
     */
    private void sendImmediateNotifications(String orderId, Long tenantId, Map<String, Object> orderData) {
        LocalDateTime now = LocalDateTime.now();

        // 创建小程序通知任务
        if (notificationConfig.getChannels().getMiniProgram()) {
            NotificationTask miniProgramTask = createNotificationTask(
                    orderId, tenantId, NotificationTask.TaskType.MINI_PROGRAM.getCode(),
                    now, orderData
            );
            notificationTaskMapper.insert(miniProgramTask);

            // 立即发送小程序通知消息
            notificationProducer.sendImmediateNotification(miniProgramTask);
        }

        // 创建公众号通知任务
        if (notificationConfig.getChannels().getWechatMp()) {
            NotificationTask wechatMpTask = createNotificationTask(
                    orderId, tenantId, NotificationTask.TaskType.WECHAT_MP.getCode(),
                    now, orderData
            );
            notificationTaskMapper.insert(wechatMpTask);

            // 立即发送公众号通知消息
            notificationProducer.sendImmediateNotification(wechatMpTask);
        }
    }

    /**
     * 安排延时短信任务
     */
    private void scheduleDelayedSmsTask(String orderId, Long tenantId, Map<String, Object> orderData) {
        if (!notificationConfig.getChannels().getSms()) {
            return;
        }

        LocalDateTime scheduledTime = LocalDateTime.now().plus(notificationConfig.getDelays().getSms());

        NotificationTask smsTask = createNotificationTask(
                orderId, tenantId, NotificationTask.TaskType.SMS.getCode(),
                scheduledTime, orderData
        );
        notificationTaskMapper.insert(smsTask);

        // 发送延时消息
        long delayMillis = notificationConfig.getDelays().getSms().toMillis();
        notificationProducer.sendDelayedNotification(smsTask, delayMillis);

        log.info("[scheduleDelayedSmsTask] 安排短信延时任务，订单ID: {}, 计划执行时间: {}", orderId, scheduledTime);
    }

    /**
     * 安排延时电话任务
     */
    private void scheduleDelayedCallTask(String orderId, Long tenantId, Map<String, Object> orderData) {
        if (!notificationConfig.getChannels().getPhoneCall()) {
            return;
        }

        // 电话任务在短信后6小时，总共8小时后执行
        long totalDelayMillis = notificationConfig.getDelays().getSms().toMillis() + 
                               notificationConfig.getDelays().getPhone().toMillis();
        LocalDateTime scheduledTime = LocalDateTime.now().plusNanos(totalDelayMillis * 1_000_000);

        NotificationTask phoneTask = createNotificationTask(
                orderId, tenantId, NotificationTask.TaskType.PHONE_CALL.getCode(),
                scheduledTime, orderData
        );
        notificationTaskMapper.insert(phoneTask);

        // 发送延时消息
        notificationProducer.sendDelayedNotification(phoneTask, totalDelayMillis);

        log.info("[scheduleDelayedCallTask] 安排电话延时任务，订单ID: {}, 计划执行时间: {}", orderId, scheduledTime);
    }

    /**
     * 创建通知任务
     */
    private NotificationTask createNotificationTask(String orderId, Long tenantId, String taskType,
                                                    LocalDateTime scheduledTime, Map<String, Object> orderData) {
        NotificationTask task = new NotificationTask();
        task.setOrderId(orderId);
        task.setTenantId(tenantId);
        task.setTaskType(taskType);
        task.setTaskStatus(NotificationTask.TaskStatus.PENDING.getCode());
        task.setScheduledTime(scheduledTime);
        task.setRetryCount(0);
        task.setMaxRetryCount(notificationConfig.getRetry().getMaxAttempts());
        task.setTaskData(new HashMap<>(orderData));
        task.setCreateTime(LocalDateTime.now());
        task.setUpdateTime(LocalDateTime.now());

        return task;
    }

    /**
     * 取消订单的后续任务
     *
     * @param orderId 订单ID
     * @param tenantId 租户ID
     */
    public void cancelSubsequentTasks(String orderId, Long tenantId) {
        log.info("[cancelSubsequentTasks] 取消订单后续任务，订单ID: {}, 租户ID: {}", orderId, tenantId);

        int cancelledCount = notificationTaskMapper.cancelSubsequentTasks(orderId, tenantId, LocalDateTime.now());

        log.info("[cancelSubsequentTasks] 已取消 {} 个后续任务，订单ID: {}", cancelledCount, orderId);
    }
}
