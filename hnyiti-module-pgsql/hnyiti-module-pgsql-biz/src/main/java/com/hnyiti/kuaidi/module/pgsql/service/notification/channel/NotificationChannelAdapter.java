package com.hnyiti.kuaidi.module.pgsql.service.notification.channel;

import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.service.notification.model.NotificationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 通知渠道适配器
 * 根据通知类型选择对应的发送渠道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NotificationChannelAdapter {

    @Autowired
    private MiniProgramNotificationChannel miniProgramChannel;

    @Autowired
    private WechatMpNotificationChannel wechatMpChannel;

    @Autowired
    private SmsNotificationChannel smsChannel;

    @Autowired
    private PhoneCallNotificationChannel phoneCallChannel;

    private final Map<String, NotificationChannel> channelMap = new HashMap<>();

    /**
     * 初始化渠道映射
     */
    public void initChannelMap() {
        channelMap.put(NotificationTask.TaskType.MINI_PROGRAM.getCode(), miniProgramChannel);
        channelMap.put(NotificationTask.TaskType.WECHAT_MP.getCode(), wechatMpChannel);
        channelMap.put(NotificationTask.TaskType.SMS.getCode(), smsChannel);
        channelMap.put(NotificationTask.TaskType.PHONE_CALL.getCode(), phoneCallChannel);
    }

    /**
     * 发送通知
     *
     * @param task 通知任务
     * @return 发送结果
     */
    public NotificationResult sendNotification(NotificationTask task) {
        log.info("[sendNotification] 开始发送通知，任务ID: {}, 类型: {}, 订单ID: {}", 
                task.getId(), task.getTaskType(), task.getOrderId());

        try {
            // 确保渠道映射已初始化
            if (channelMap.isEmpty()) {
                initChannelMap();
            }

            // 获取对应的通知渠道
            NotificationChannel channel = channelMap.get(task.getTaskType());
            if (channel == null) {
                String errorMsg = "不支持的通知类型: " + task.getTaskType();
                log.error("[sendNotification] {}", errorMsg);
                return NotificationResult.failure(errorMsg);
            }

            // 执行发送
            NotificationResult result = channel.send(task);

            log.info("[sendNotification] 通知发送完成，任务ID: {}, 类型: {}, 结果: {}", 
                    task.getId(), task.getTaskType(), result.isSuccess() ? "成功" : "失败");

            return result;

        } catch (Exception e) {
            log.error("[sendNotification] 发送通知异常，任务ID: {}, 类型: {}", task.getId(), task.getTaskType(), e);
            return NotificationResult.failure("发送异常: " + e.getMessage());
        }
    }

    /**
     * 检查渠道是否可用
     *
     * @param taskType 任务类型
     * @return 是否可用
     */
    public boolean isChannelAvailable(String taskType) {
        try {
            if (channelMap.isEmpty()) {
                initChannelMap();
            }

            NotificationChannel channel = channelMap.get(taskType);
            return channel != null && channel.isAvailable();

        } catch (Exception e) {
            log.error("[isChannelAvailable] 检查渠道可用性异常，类型: {}", taskType, e);
            return false;
        }
    }
}
