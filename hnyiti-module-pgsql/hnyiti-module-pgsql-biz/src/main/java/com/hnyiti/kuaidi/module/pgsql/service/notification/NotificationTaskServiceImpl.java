package com.hnyiti.kuaidi.module.pgsql.service.notification;

import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.dal.mapper.notification.NotificationTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 通知任务服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class NotificationTaskServiceImpl implements NotificationTaskService {

    @Autowired
    private NotificationTaskMapper notificationTaskMapper;

    @Override
    public Long createNotificationTask(String orderId, Long tenantId, String taskType,
                                       LocalDateTime scheduledTime, Map<String, Object> taskData) {
        log.info("[createNotificationTask] 创建通知任务，订单ID: {}, 租户ID: {}, 任务类型: {}",
                orderId, tenantId, taskType);

        try {
            // 验证任务类型
            validateTaskType(taskType);

            // 创建通知任务对象
            NotificationTask task = new NotificationTask();
            task.setOrderId(orderId);
            task.setTenantId(tenantId);
            task.setTaskType(taskType);
            task.setTaskStatus(NotificationTask.TaskStatus.PENDING.getCode());
            task.setScheduledTime(scheduledTime != null ? scheduledTime : LocalDateTime.now());
            task.setRetryCount(0);
            task.setMaxRetryCount(3); // 默认最大重试3次
            task.setTaskData(taskData != null ? new HashMap<>(taskData) : new HashMap<>());
            task.setCreateTime(LocalDateTime.now());
            task.setUpdateTime(LocalDateTime.now());

            // 插入数据库
            notificationTaskMapper.insert(task);

            log.info("[createNotificationTask] 通知任务创建成功，任务ID: {}, 订单ID: {}", task.getId(), orderId);
            return task.getId();

        } catch (Exception e) {
            log.error("[createNotificationTask] 创建通知任务失败，订单ID: {}", orderId, e);
            throw new RuntimeException("创建通知任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public NotificationTask getNotificationTaskById(Long id) {
        log.info("[getNotificationTaskById] 查询通知任务，任务ID: {}", id);

        try {
            NotificationTask task = notificationTaskMapper.selectById(id);
            if (task == null) {
                log.warn("[getNotificationTaskById] 通知任务不存在，任务ID: {}", id);
                throw new RuntimeException("通知任务不存在，任务ID: " + id);
            }

            log.info("[getNotificationTaskById] 查询通知任务成功，任务ID: {}, 订单ID: {}", id, task.getOrderId());
            return task;

        } catch (Exception e) {
            log.error("[getNotificationTaskById] 查询通知任务失败，任务ID: {}", id, e);
            throw new RuntimeException("查询通知任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void updateTaskStatus(Long id, String taskStatus) {
        log.info("[updateTaskStatus] 更新任务状态，任务ID: {}, 状态: {}", id, taskStatus);

        try {
            // 验证任务状态
            validateTaskStatus(taskStatus);

            NotificationTask task = new NotificationTask();
            task.setId(id);
            task.setTaskStatus(taskStatus);
            task.setUpdateTime(LocalDateTime.now());

            int updateCount = notificationTaskMapper.updateById(task);
            if (updateCount == 0) {
                log.warn("[updateTaskStatus] 任务不存在或更新失败，任务ID: {}", id);
                throw new RuntimeException("任务不存在或更新失败，任务ID: " + id);
            }

            log.info("[updateTaskStatus] 任务状态更新成功，任务ID: {}, 状态: {}", id, taskStatus);

        } catch (Exception e) {
            log.error("[updateTaskStatus] 更新任务状态失败，任务ID: {}", id, e);
            throw new RuntimeException("更新任务状态失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证任务类型
     */
    private void validateTaskType(String taskType) {
        boolean isValid = false;
        for (NotificationTask.TaskType type : NotificationTask.TaskType.values()) {
            if (type.getCode().equals(taskType)) {
                isValid = true;
                break;
            }
        }
        if (!isValid) {
            throw new IllegalArgumentException("无效的任务类型: " + taskType);
        }
    }

    /**
     * 验证任务状态
     */
    private void validateTaskStatus(String taskStatus) {
        boolean isValid = false;
        for (NotificationTask.TaskStatus status : NotificationTask.TaskStatus.values()) {
            if (status.getCode().equals(taskStatus)) {
                isValid = true;
                break;
            }
        }
        if (!isValid) {
            throw new IllegalArgumentException("无效的任务状态: " + taskStatus);
        }
    }
}
