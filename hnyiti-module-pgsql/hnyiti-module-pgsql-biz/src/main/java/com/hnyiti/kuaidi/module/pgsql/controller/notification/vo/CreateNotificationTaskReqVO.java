package com.hnyiti.kuaidi.module.pgsql.controller.notification.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 创建通知任务请求 VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "创建通知任务请求")
public class CreateNotificationTaskReqVO {

    @Schema(description = "订单ID", required = true, example = "ORDER_20231201_001")
    @NotBlank(message = "订单ID不能为空")
    private String orderId;

    @Schema(description = "租户ID", required = true, example = "1")
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    @Schema(description = "任务类型", required = true, example = "SMS",
            allowableValues = {"MINI_PROGRAM", "WECHAT_MP", "SMS", "PHONE_CALL"})
    @NotBlank(message = "任务类型不能为空")
    private String taskType;

    @Schema(description = "计划执行时间", example = "2023-12-01T10:00:00")
    private LocalDateTime scheduledTime;

    @Schema(description = "任务数据", example = "{\"mobile\":\"13800138000\",\"content\":\"您的订单需要补差\"}")
    private Map<String, Object> taskData;
}
