package com.hnyiti.kuaidi.module.pgsql.service.notification;

import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationRecord;
import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.dal.mapper.notification.NotificationTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 补差通知服务
 * 对外提供的主要业务接口
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CompensationNotificationService {

    @Autowired
    private NotificationScheduler notificationScheduler;

    @Autowired
    private NotificationRecordService notificationRecordService;

    @Autowired
    private NotificationTaskMapper notificationTaskMapper;

    /**
     * 启动补差通知流程
     * 当订单需要补差时调用此方法启动多阶段通知
     *
     * @param orderId            订单ID
     * @param tenantId           租户ID
     * @param compensationAmount 补差金额
     * @param userInfo           用户信息（openid、手机号等）
     */
    public void startCompensationNotification(String orderId, Long tenantId,
                                              Object compensationAmount, Map<String, Object> userInfo) {
        log.info("[startCompensationNotification] 启动补差通知流程，订单ID: {}, 租户ID: {}, 补差金额: {}",
                orderId, tenantId, compensationAmount);

        try {
            // 构造订单数据
            Map<String, Object> orderData = buildOrderData(orderId, tenantId, compensationAmount, userInfo);

            // 启动通知流程
            notificationScheduler.startCompensationNotificationFlow(orderId, tenantId, orderData);

            log.info("[startCompensationNotification] 补差通知流程启动成功，订单ID: {}", orderId);

        } catch (Exception e) {
            log.error("[startCompensationNotification] 启动补差通知流程失败，订单ID: {}", orderId, e);
            throw new RuntimeException("启动补差通知流程失败: " + e.getMessage(), e);
        }
    }

    /**
     * 取消补差通知流程
     * 当订单已完成补差时调用此方法取消后续通知
     *
     * @param orderId  订单ID
     * @param tenantId 租户ID
     */
    public void cancelCompensationNotification(String orderId, Long tenantId) {
        log.info("[cancelCompensationNotification] 取消补差通知流程，订单ID: {}, 租户ID: {}", orderId, tenantId);

        try {
            // 取消后续任务
            notificationScheduler.cancelSubsequentTasks(orderId, tenantId);

            log.info("[cancelCompensationNotification] 补差通知流程取消成功，订单ID: {}", orderId);

        } catch (Exception e) {
            log.error("[cancelCompensationNotification] 取消补差通知流程失败，订单ID: {}", orderId, e);
        }
    }

    /**
     * 查询订单的通知记录
     *
     * @param orderId  订单ID
     * @param tenantId 租户ID
     * @return 通知记录列表
     */
    public List<NotificationRecord> getNotificationRecords(String orderId, Long tenantId) {
        return notificationRecordService.getByOrderId(orderId, tenantId);
    }

    /**
     * 查询订单的通知任务
     *
     * @param orderId  订单ID
     * @param tenantId 租户ID
     * @return 通知任务列表
     */
    public List<NotificationTask> getNotificationTasks(String orderId, Long tenantId) {
        return notificationTaskMapper.selectByOrderId(orderId, tenantId);
    }

    /**
     * 获取通知统计信息
     *
     * @param orderId  订单ID
     * @param tenantId 租户ID
     * @return 统计信息
     */
    public Map<String, Object> getNotificationStatistics(String orderId, Long tenantId) {
        Map<String, Object> statistics = new HashMap<>();

        // 查询所有通知记录
        List<NotificationRecord> records = notificationRecordService.getByOrderId(orderId, tenantId);

        // 统计各类型通知的发送情况
        long miniProgramCount = records.stream()
                .filter(r -> NotificationRecord.NotificationType.MINI_PROGRAM.getCode().equals(r.getNotificationType()))
                .filter(r -> NotificationRecord.SendStatus.SUCCESS.getCode().equals(r.getSendStatus()))
                .count();

        long wechatMpCount = records.stream()
                .filter(r -> NotificationRecord.NotificationType.WECHAT_MP.getCode().equals(r.getNotificationType()))
                .filter(r -> NotificationRecord.SendStatus.SUCCESS.getCode().equals(r.getSendStatus()))
                .count();

        long smsCount = records.stream()
                .filter(r -> NotificationRecord.NotificationType.SMS.getCode().equals(r.getNotificationType()))
                .filter(r -> NotificationRecord.SendStatus.SUCCESS.getCode().equals(r.getSendStatus()))
                .count();

        long phoneCallCount = records.stream()
                .filter(r -> NotificationRecord.NotificationType.PHONE_CALL.getCode().equals(r.getNotificationType()))
                .filter(r -> NotificationRecord.SendStatus.SUCCESS.getCode().equals(r.getSendStatus()))
                .count();

        // 不再计算费用

        statistics.put("miniProgramCount", miniProgramCount);
        statistics.put("wechatMpCount", wechatMpCount);
        statistics.put("smsCount", smsCount);
        statistics.put("phoneCallCount", phoneCallCount);
        // 不再统计费用
        statistics.put("totalRecords", records.size());

        return statistics;
    }

    /**
     * 构造订单数据
     */
    private Map<String, Object> buildOrderData(String orderId, Long tenantId,
                                               Object compensationAmount, Map<String, Object> userInfo) {
        Map<String, Object> orderData = new HashMap<>();
        orderData.put("orderId", orderId);
        orderData.put("tenantId", tenantId);
        orderData.put("compensationAmount", compensationAmount);

        // 添加用户信息
        if (userInfo != null) {
            orderData.putAll(userInfo);
        }

        // 添加模板信息（可以从配置中获取）
        orderData.put("templateId", "compensation_template");

        // 添加小程序名称（用于语音通知）
        orderData.put("miniProgramName", orderData.getOrDefault("miniProgramName", "快递助手"));

        return orderData;
    }
}
