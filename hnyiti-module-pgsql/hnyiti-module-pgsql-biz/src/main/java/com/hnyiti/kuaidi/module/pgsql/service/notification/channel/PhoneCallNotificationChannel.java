package com.hnyiti.kuaidi.module.pgsql.service.notification.channel;

import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.service.notification.model.NotificationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 电话通知渠道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PhoneCallNotificationChannel implements NotificationChannel {

    @Override
    public NotificationResult send(NotificationTask task) {
        log.info("[send] 发起电话通知，任务ID: {}, 订单ID: {}", task.getId(), task.getOrderId());

        try {
            // 从任务数据中获取必要信息
            Map<String, Object> taskData = task.getTaskData();
            String mobile = (String) taskData.get("mobile");
            String templateCode = (String) taskData.get("templateCode");
            
            if (mobile == null || templateCode == null) {
                return NotificationResult.failure("缺少必要参数：mobile或templateCode");
            }

            // TODO: 调用语音通话服务API
            // 这里需要集成现有的语音通话服务
            
            // 构造消息内容
            String content = buildMessageContent(taskData);
            
            // 模拟发送成功，计算费用
            BigDecimal cost = new BigDecimal("0.15"); // 假设每次通话0.15元
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("callId", "mock_callId_" + System.currentTimeMillis());
            responseData.put("code", "OK");
            responseData.put("message", "通话发起成功");
            responseData.put("duration", 30); // 通话时长（秒）
            
            log.info("[send] 电话通知发起成功，任务ID: {}, mobile: {}, 费用: {}", task.getId(), mobile, cost);
            
            return NotificationResult.success(mobile, content, cost);

        } catch (Exception e) {
            log.error("[send] 发起电话通知异常，任务ID: {}", task.getId(), e);
            return NotificationResult.failure("发起异常: " + e.getMessage());
        }
    }

    @Override
    public boolean isAvailable() {
        // TODO: 检查语音通话服务是否可用
        return true;
    }

    @Override
    public String getChannelType() {
        return NotificationTask.TaskType.PHONE_CALL.getCode();
    }

    /**
     * 构造消息内容
     */
    private String buildMessageContent(Map<String, Object> taskData) {
        // 从任务数据中提取订单信息构造消息内容
        String orderId = (String) taskData.get("orderId");
        Object compensationAmount = taskData.get("compensationAmount");
        
        return String.format("您好，您的快递订单%s需要补差%s元，请及时处理。", 
                orderId, compensationAmount != null ? compensationAmount.toString() : "0");
    }
}
