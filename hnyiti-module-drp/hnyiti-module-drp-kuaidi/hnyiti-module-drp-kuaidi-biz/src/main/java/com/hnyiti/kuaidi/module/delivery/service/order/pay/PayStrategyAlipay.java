package com.hnyiti.kuaidi.module.delivery.service.order.pay;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hnyiti.kuaidi.framework.pay.core.enums.channel.PayChannelEnum;
import com.hnyiti.kuaidi.framework.pay.core.enums.order.PayOrderDisplayModeEnum;
import com.hnyiti.kuaidi.framework.security.core.util.SecurityFrameworkUtils;
import com.hnyiti.kuaidi.framework.tenant.core.context.TenantContextHolder;
import com.hnyiti.kuaidi.module.PayLogConstants;
import com.hnyiti.kuaidi.module.consumer.wxmsg.WxMiniMsgVo;
import com.hnyiti.kuaidi.module.consumer.wxmsg.WxMqProducer;
import com.hnyiti.kuaidi.module.consumer.wxmsg.WxMsgTypeConstants;
import com.hnyiti.kuaidi.module.delivery.service.order.OrderService;
import com.hnyiti.kuaidi.module.delivery.service.order.pay.vo.PayRequest;
import com.hnyiti.kuaidi.module.member.controller.admin.wechatpaylog.vo.WechatPayLogCreateReqVO;
import com.hnyiti.kuaidi.module.member.enums.FeeConstants;
import com.hnyiti.kuaidi.module.member.service.wechatpaylog.WechatPayLogService;
import com.hnyiti.kuaidi.module.pay.api.order.dto.PayOrderCreateReqDTO;
import com.hnyiti.kuaidi.module.pay.controller.admin.order.vo.PayOrderSubmitReqVO;
import com.hnyiti.kuaidi.module.pay.controller.admin.order.vo.PayOrderSubmitRespVO;
import com.hnyiti.kuaidi.module.pay.dal.dataobject.app.PayAppDO;
import com.hnyiti.kuaidi.module.pay.service.app.PayAppService;
import com.hnyiti.kuaidi.module.pay.service.order.PayOrderService;
import com.hnyiti.kuaidi.module.system.api.param.ParamApi;
import com.hnyiti.kuaidi.module.system.api.social.SocialUserApi;
import com.hnyiti.kuaidi.module.system.enums.social.SocialTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.hnyiti.kuaidi.framework.common.util.date.LocalDateTimeUtils.addTime;
import static com.hnyiti.kuaidi.framework.common.util.servlet.ServletUtils.getClientIP;

/**
 * 微信支付相关
 */
@Slf4j
public class PayStrategyAlipay implements PayStrategy {

    @Resource
    private RedisTemplate redisTemplate;
    @Lazy
    @Resource
    private OrderService orderService;
    @Lazy
    @Resource
    private ParamApi paramApi;
    @Lazy
    @Resource
    private PayAppService payAppService;
    @Lazy
    @Resource
    private SocialUserApi socialUserApi;
    @Lazy
    @Resource
    private PayOrderService payOrderService;
    @Lazy
    @Resource
    private WechatPayLogService wechatPayLogService;
    @Lazy
    @Resource
    private WxMqProducer wxMqProducer;


    /**
     * 创建支付单
     * @param payRequest
     * @return
     */
    @Override
    public Map createPayOrder(PayRequest payRequest) {
        //         获取到用户订阅消息模板
        Map coreMap = new HashMap();
        try {
            //        1. 获取消息订阅模板
            coreMap.put("tempIds", orderService.getOrderTmplIds());
//        2. 校验微信渠道支付
            Assert.isTrue(paramApi.isOpenWechatPay(TenantContextHolder.getTenantId()), "微信支付渠道已关闭");
//        3. 校验系统支付参数
            List<PayAppDO> appList = payAppService.getAppList();
            Assert.isTrue(CollUtil.isNotEmpty(appList), "系统支付参数不正确");
//        4. 组装信息调用微信支付
            Long payOrderId = buildWeChatPayment(payRequest, coreMap, appList);
//        5. 创建微信支付日志
            createWechatPayLog(payOrderId,payRequest, PayLogConstants.PAY_LOG_PAY_TYPE_ALIPAY);
        }catch (Exception e) {
            // 支付失败，发送企微短信
            weChatPayError(payRequest, e);
        }
        return coreMap;
    }


    /**
     * 调用微信支付接口
     *
     * @param params
     * @param coreMap
     * @param appList
     * @return
     */
    private Long buildWeChatPayment(PayRequest params, Map<String, Object> coreMap, List<PayAppDO> appList){
        if(ObjectUtil.isEmpty(params.getPaymentChannel())){
            params.setPaymentChannel(PayChannelEnum.ALIPAY_PUB.getCode());
        }
        if(ObjectUtil.isEmpty(params.getDisplayMode())){
            params.setDisplayMode(PayOrderDisplayModeEnum.APP.getMode());
        }

        Long tenantId = TenantContextHolder.getTenantId();

        String subject = params.getSubject();
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Map<String, String> channelExtras = params.getChannelExtras();
        String openId = socialUserApi.getUserOpenId(userId, params.getCode(), SocialTypeEnum.ALI_MINI_APP.getType()).getData();
        channelExtras.put("openid", openId);
        // TODO 记得改成查redis
//        channelExtras.put("appId", "2021004190629799");
        // 获取支付宝小程序appId
        channelExtras.put("appId", paramApi.getAlipayAppId(tenantId));

//        channelExtras.put("orderId", order.getId());
        Integer price = params.getAmount().multiply(BigDecimal.valueOf(100)).intValue();
//        channelExtras.put("type", "deliveryV2");
        channelExtras.put("payType", PayLogConstants.PAY_LOG_PAY_TYPE_ALIPAY);
        Long payOrderId = payOrderService.createOrder(new PayOrderCreateReqDTO().setAppId(appList.get(0).getId()).setUserIp(getClientIP()) // 支付应用
                .setMerchantOrderId(IdWorker.getIdStr()) // 业务的订单编号
                .setSubject(subject).setBody(params.getBody()).setPrice(price) // 价格信息
                .setExpireTime(addTime(Duration.ofHours(2L))).setUserId(userId));

        PayOrderSubmitReqVO reqVO = new PayOrderSubmitReqVO();
        reqVO.setChannelCode(params.getPaymentChannel());
        reqVO.setId(payOrderId);
        reqVO.setChannelExtras(channelExtras);
        reqVO.setDisplayMode(params.getDisplayMode());

        PayOrderSubmitRespVO payOrderSubmitRespVO = payOrderService.submitOrder(reqVO, getClientIP());
        String coreJson = payOrderSubmitRespVO.getDisplayContent();
//        coreMap = JSONUtil.toBean(coreJson, Map.class);
//        coreMap.put("package", coreMap.get("packageValue"));

        Map<String, Object> tempMap = JSONUtil.toBean(coreJson, Map.class);
//        if(params.getPaymentChannel().equals(PayChannelEnum.WX_LITE.getCode())){
//            tempMap.put("package", tempMap.get("packageValue"));
//            tempMap.put("payOrderId", payOrderId);
//        } else if (params.getPaymentChannel().equals(PayChannelEnum.ALIPAY_PUB.getCode())) {
        tempMap.put("tradeNo", tempMap.get("tradeNo"));
        tempMap.put("payOrderId", payOrderId);

//        coreMap.putAll(tempMap);


        coreMap.put("tradeNo", tempMap.get("tradeNo"));
        coreMap.put("payOrderId", payOrderId);


        return payOrderId;
    }


    private void createWechatPayLog(Long payOrderId, PayRequest payParams, String payType) {

        WechatPayLogCreateReqVO createReqVO = new WechatPayLogCreateReqVO();
        createReqVO.setPayOrderId(payOrderId);
        createReqVO.setBizType(payParams.getBizType());
        if(ObjectUtil.isNotEmpty(payParams.getOrderId())){
            createReqVO.setOrderId(Long.valueOf(payParams.getOrderId()));
        }
        if(ObjectUtil.isNotEmpty(payParams.getBatchOrderId())){
            createReqVO.setBatchId(Long.valueOf(payParams.getBatchOrderId()));
        }
        createReqVO.setMbrId(SecurityFrameworkUtils.getLoginUserId());
        createReqVO.setPayStatus(FeeConstants.PAY_STATUS_UNPAY);
        createReqVO.setPayAmount(payParams.getAmount());
        createReqVO.setPayType(payType);
        // 新增添加运单号
        createReqVO.setOrderNo(payParams.getOrderNo());
        wechatPayLogService.createWechatPayLog(createReqVO);
    }


    private void weChatPayError(PayRequest payParams, Exception e) {
        WxMiniMsgVo miniMsgVo = new WxMiniMsgVo();
        miniMsgVo.setTenantId(TenantContextHolder.getTenantId());
        miniMsgVo.setBizId(payParams.getBizId());
        miniMsgVo.setOrderNo(payParams.getOrderNo());
        miniMsgVo.setAmount(payParams.getAmount());
        miniMsgVo.setExceptionMessage(e.getMessage());
        miniMsgVo.setMsgType(WxMsgTypeConstants.ERROR);
        miniMsgVo.setBizType(WxMsgTypeConstants.PAY_BIZ_TYPE);
        miniMsgVo.setUserId(SecurityFrameworkUtils.getLoginUserId());
        wxMqProducer.sendMiniMessage(miniMsgVo);
    }

}
