package com.hnyiti.kuaidi.module.delivery.controller.app;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.hnyiti.kuaidi.framework.common.exception.ErrorCode;
import com.hnyiti.kuaidi.framework.common.exception.ServiceException;
import com.hnyiti.kuaidi.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.hnyiti.kuaidi.framework.common.pojo.CommonResult;
import com.hnyiti.kuaidi.framework.common.pojo.PageResult;
import com.hnyiti.kuaidi.framework.common.util.date.DateUtils;
import com.hnyiti.kuaidi.framework.dict.core.util.DictFrameworkUtils;
import com.hnyiti.kuaidi.framework.security.core.util.SecurityFrameworkUtils;
import com.hnyiti.kuaidi.framework.tenant.core.context.TenantContextHolder;
import com.hnyiti.kuaidi.module.ai.vo.AiAddressVo;
import com.hnyiti.kuaidi.module.ai.vo.YiDaPreOrderVo;
import com.hnyiti.kuaidi.module.consumer.MQOrderPayRemedyVo;
import com.hnyiti.kuaidi.module.consumer.OrderMqProducer;
import com.hnyiti.kuaidi.module.delivery.annotation.RateLimit;
import com.hnyiti.kuaidi.module.delivery.controller.admin.address.vo.*;
import com.hnyiti.kuaidi.module.delivery.controller.admin.couponuser.vo.CouponUserPageReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.couponuser.vo.CouponUserRespVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.kd.channel.vo.KdChannelExportReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.order.vo.OrderMiniRespPageVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.order.vo.OrderNumRespVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.order.vo.OrderPageReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.order.vo.OrderStatusNumRespVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.orderparcel.vo.OrderParcelUpdateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.app.third.vo.ThirdQueryPriceReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.app.vo.*;
import com.hnyiti.kuaidi.module.delivery.convert.address.AddressConvert;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.address.AddressDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.cancelbutton.CancelButtonStatisticsDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.couponcode.CouponCodeOldDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.feeuser.FeeUserDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.order.OrderDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.ordercontacts.OrderContactsDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.orderfee.OrderFeeDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.orderparcel.OrderParcelDO;
import com.hnyiti.kuaidi.module.delivery.enums.CouponConstants;
import com.hnyiti.kuaidi.module.delivery.enums.OrderConstants;
import com.hnyiti.kuaidi.module.delivery.service.address.AddressService;
import com.hnyiti.kuaidi.module.delivery.service.cache.CacheServiceImpl;
import com.hnyiti.kuaidi.module.delivery.service.cancelbutton.CancelButtonStatisticsService;
import com.hnyiti.kuaidi.module.delivery.service.channelexpress.ChannelExpressService;
import com.hnyiti.kuaidi.module.delivery.service.couponcode.CouponCodeOldService;
import com.hnyiti.kuaidi.module.delivery.service.couponuser.CouponUserService;
import com.hnyiti.kuaidi.module.delivery.service.feeuser.FeeUserService;
import com.hnyiti.kuaidi.module.delivery.service.kd.channel.KdChannelService;
import com.hnyiti.kuaidi.module.delivery.service.order.OrderEsService;
import com.hnyiti.kuaidi.module.delivery.service.order.OrderPriceServiceImpl;
import com.hnyiti.kuaidi.module.delivery.service.order.OrderService;
import com.hnyiti.kuaidi.module.delivery.service.order.OrderSubmitServiceImpl;
import com.hnyiti.kuaidi.module.delivery.service.orderbatchshipments.OrderBatchShipmentsService;
import com.hnyiti.kuaidi.module.delivery.service.ordercontacts.OrderContactsService;
import com.hnyiti.kuaidi.module.delivery.service.orderfee.OrderFeeService;
import com.hnyiti.kuaidi.module.delivery.service.orderparcel.OrderParcelService;
import com.hnyiti.kuaidi.module.delivery.service.thirdapi.*;
import com.hnyiti.kuaidi.module.delivery.util.RateLimitUtil;
import com.hnyiti.kuaidi.module.member.api.balance.vo.api.BalanceApi;
import com.hnyiti.kuaidi.module.member.api.user.MemberUserApi;
import com.hnyiti.kuaidi.module.member.api.user.dto.MemberUserRespDTO;
import com.hnyiti.kuaidi.module.member.service.wechatpaylog.WechatPayLogService;
import com.hnyiti.kuaidi.module.message.api.MessageSendApi;
import com.hnyiti.kuaidi.module.message.api.group.vo.NoticeGroupMiniRespVO;
import com.hnyiti.kuaidi.module.pay.service.app.PayAppService;
import com.hnyiti.kuaidi.module.pay.service.order.PayOrderService;
import com.hnyiti.kuaidi.module.system.api.param.ParamApi;
import com.hnyiti.kuaidi.module.system.api.param.ReasonParamApi;
import com.hnyiti.kuaidi.module.system.api.param.vo.ReasonParamVO;
import com.hnyiti.kuaidi.module.system.api.social.SocialUserApi;
import com.hnyiti.kuaidi.module.system.api.tenant.TenantApi;
import com.hnyiti.kuaidi.module.system.api.user.dto.CacheUserDTO;
import com.hnyiti.kuaidi.module.system.enums.DictTypeConstants;
import com.hnyiti.kuaidi.module.system.enums.MbrConstants;
import com.hnyiti.kuaidi.vo.CreateOrderVO;
import com.hnyiti.kuaidi.vo.OrderTrackReturnVo;
import com.hnyiti.kuaidi.vo.QueryPriceResVO;
import com.hnyiti.kuaidi.vo.QueryPriceVO;
import com.hnyiti.kuaidi.yidai.vo.ChannelExpressDetailVO;
import com.hnyiti.kuaidi.yuntong.vo.ordercancel.YunTongCancelOrderReq;
import com.hnyiti.kuaidi.yuntong.vo.ordercancel.YunTongCancelOrderResp;
import com.hnyiti.kuaidi.yuntong.vo.resp.YunTongSubmitOrderResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.hnyiti.kuaidi.framework.common.pojo.CommonResult.success;

/**
 * 快递订单接口
 *
 * <AUTHOR>
 */
@Tag(name = "小程序 - 快递订单接口")
@RestController
@RequestMapping("/mini/v2/order")
@Slf4j
public class MiniV2DeliveryOrderController {

    @Resource
    private AddressService addressService;
    @Resource
    private OrderService orderService;
    @Resource
    private OrderParcelService parcelService;
    @Resource
    private OrderContactsService contactsService;
    @Resource
    private ChannelExpressService channelExpressService;
    @Resource
    private OrderParcelService orderParcelService;
    @Resource
    private OrderFeeService orderFeeService;
    @Resource
    private BaiDuApiServic baiDuApiServic;
    @Resource
    private AliyunApiService aliyunApiService;
    @Resource
    private JdApiService jdApiService;
    @Resource
    private MemberUserApi memberUserApi;
    @Resource
    private PayOrderService payOrderService;
    @Resource
    private ParamApi paramApi;
    @Resource
    private BalanceApi balanceApi;
    @Resource
    private PayAppService payAppService;
    @Resource
    private SocialUserApi socialUserApi;
    @Resource
    private TenantApi tenantApi;
    @Resource
    private YiDaApiService yiDaApiService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private WechatPayLogService wechatPayLogService;
    @Resource
    private CouponUserService couponUserService;
    @Resource
    private CouponCodeOldService couponCodeOldService;
    @Lazy
    @Resource
    private OrderEsService orderEsService;
    @Lazy
    @Resource
    private CacheServiceImpl cacheService;
    @Resource
    private FeeUserService feeUserService;

    @Lazy
    @Resource
    private OrderBatchShipmentsService orderBatchShipmentsService;

    @Resource
    private OrderMqProducer producer;

    @Resource
    private OrderSubmitServiceImpl orderSubmitServiceImpl;
    @Lazy
    @Resource
    private OrderPriceServiceImpl orderPriceService;
    @Resource
    private KdChannelService kdChannelService;
    @Resource
    private ThreadPoolExecutor threadPoolExecutor; // 注入线程池

    @Resource
    private RateLimitUtil rateLimitUtil;
    @Resource
    private CancelButtonStatisticsService cancelButtonStatisticsService;
    @Resource
    private ReasonParamApi reasonParamApi;

    @Resource
    private YunTongApiService yunTongApiService;

    @Resource
    private AddressRecognitionTest addressRecognitionTest;

    @Resource
    private MessageSendApi messageSendApi;

    /**
     * 查询价格
     *
     */
    @Operation(summary = "查询价格", description = "根据寄件和收件信息查询可用快递及价格")
    @ApiResponse(responseCode = "200", description = "成功返回快递价格列表")
    @PostMapping("/open/choosecom3")
    public CommonResult<List<MiniQueryPriceResVO>> choosecom(@RequestBody ThirdQueryPriceReqVO params) {
        if(ObjectUtil.isNotEmpty(params) && ObjectUtil.isNotEmpty(params.getTenantId())){
            TenantContextHolder.setTenantId(params.getTenantId());
        } else {
            TenantContextHolder.setTenantId(155L);
        }
        if(ObjectUtil.isEmpty(params.getWeight())){
            return CommonResult.error(310002, "【weight】重量为空");
        }
        if(ObjectUtil.isEmpty(params.getSenderProvince())){
            return CommonResult.error(310002, "【senderProvince】发件省不能为空");
        }
        if(ObjectUtil.isEmpty(params.getSenderCity())){
            return CommonResult.error(310002, "【senderCity】发件市不能为空");
        }
        if(ObjectUtil.isEmpty(params.getSenderDistrict())){
            return CommonResult.error(310002, "【senderDistrict】发件区/县不能为空");
        }
//        if(ObjectUtil.isEmpty(params.getSenderAddress())){
//            return CommonResult.error(310002, "【senderAddress】发件详细地址不能为空（不包含省市区）");
//        }

        if(ObjectUtil.isEmpty(params.getReceiveProvince())){
            return CommonResult.error(310002, "【receiveProvince】发件省不能为空");
        }
        if(ObjectUtil.isEmpty(params.getReceiveCity())){
            return CommonResult.error(310002, "【receiveCity】发件市不能为空");
        }
        if(ObjectUtil.isEmpty(params.getReceiveDistrict())){
            return CommonResult.error(310002, "【receiveDistrict】发件区/县不能为空");
        }
//        if(ObjectUtil.isEmpty(params.getReceiveAddress())){
//            return CommonResult.error(310002, "【receiveAddress】发件详细地址不能为空（不包含省市区）");
//        }

        QueryPriceVO queryPriceVO = new QueryPriceVO();
        BeanUtil.copyProperties(params, queryPriceVO);

        queryPriceVO.setReceiveAddressDetail(params.getReceiveProvince() + params.getReceiveCity() + params.getReceiveDistrict());
        queryPriceVO.setSenderAddressDetail(params.getSenderProvince() + params.getSenderCity() + params.getSenderDistrict());

        if(ObjectUtil.isEmpty(queryPriceVO.getGoods())){
            queryPriceVO.setGoods("日用品");
        }

        if (StrUtil.isEmpty(queryPriceVO.getSenderAddress())) {
            queryPriceVO.setSenderAddress("前面左转");
        }
        if (StrUtil.isEmpty(queryPriceVO.getReceiveAddress())) {
            queryPriceVO.setReceiveAddress("前面左转");
        }

        if (StrUtil.isEmpty(queryPriceVO.getSenderMobile()) && StrUtil.isEmpty(queryPriceVO.getSenderTel()) || ("undefined".equals(queryPriceVO.getSenderMobile()) || "undefined".equals(queryPriceVO.getSenderTel()))) {
            queryPriceVO.setSenderMobile("18012341234");
        }
        if (ObjectUtil.isEmpty(queryPriceVO.getGoods())) {
            queryPriceVO.setGoods("日用品");
        }
        if (ObjectUtil.isEmpty(queryPriceVO.getPackageCount())) {
            queryPriceVO.setPackageCount(1);
        }

        if (StrUtil.isEmpty(queryPriceVO.getReceiveMobile()) && StrUtil.isEmpty(queryPriceVO.getReceiveTel()) || ("undefined".equals(queryPriceVO.getReceiveMobile()) || "undefined".equals(queryPriceVO.getReceiveTel()))) {
            queryPriceVO.setReceiveMobile("18012341234");
        }

//        yidaPreOrder(queryPriceVO);
        queryPriceVO.setUserId(SecurityFrameworkUtils.getLoginUserId());
//        List<QueryPriceResVO> queryPriceResVOS = orderService.queryPrice(queryPriceVO);
        List<QueryPriceResVO> queryPriceResVOS = orderPriceService.queryPrice(queryPriceVO);

        List<MiniQueryPriceResVO> returnList = new ArrayList<>();
        if (CollUtil.isNotEmpty(queryPriceResVOS)) {
            for (QueryPriceResVO queryPriceResVO : queryPriceResVOS) {
                MiniQueryPriceResVO miniQueryPriceReqVO = new MiniQueryPriceResVO();
                BeanUtil.copyProperties(queryPriceResVO, miniQueryPriceReqVO);
                returnList.add(miniQueryPriceReqVO);
            }
        }
        return CommonResult.success(returnList);
    }


    /**
     * 查询价格
     *
     */
    @Operation(summary = "查询物流价格", description = "根据寄件和收件信息查询可用快递及价格")
    @ApiResponse(responseCode = "200", description = "成功返回快递价格列表")
    @PostMapping("/choosecom")
    @RateLimit(key = "choose_com")
    public CommonResult<List<MiniQueryPriceResVO>> choosecom(@RequestBody MiniQueryPriceReqVO params) {
        /* // 检查访问频率限制
        rateLimitUtil.checkRateLimit(ip, userId);*/

        QueryPriceVO queryPriceVO = new QueryPriceVO();
        BeanUtil.copyProperties(params, queryPriceVO);

        queryPriceVO.setReceiveAddressDetail(params.getReceiveProvince() + params.getReceiveCity() + params.getReceiveDistrict());
        queryPriceVO.setSenderAddressDetail(params.getSenderProvince() + params.getSenderCity() + params.getSenderDistrict());

        if (StrUtil.isEmpty(queryPriceVO.getSenderAddress())) {
            queryPriceVO.setSenderAddress("前面左转");
        }
        if (StrUtil.isEmpty(queryPriceVO.getSenderAddress())) {
            queryPriceVO.setSenderAddress("前面左转");
        }

        if (StrUtil.isEmpty(queryPriceVO.getSenderMobile()) && StrUtil.isEmpty(queryPriceVO.getSenderTel()) || ("undefined".equals(queryPriceVO.getSenderMobile()) || "undefined".equals(queryPriceVO.getSenderTel()))) {
            queryPriceVO.setSenderMobile("18012341234");
        }
        if (ObjectUtil.isEmpty(queryPriceVO.getGoods())) {
            queryPriceVO.setGoods("日用品");
        }
        if (ObjectUtil.isEmpty(queryPriceVO.getPackageCount())) {
            queryPriceVO.setPackageCount(1);
        }

        if (StrUtil.isEmpty(queryPriceVO.getReceiveMobile()) && StrUtil.isEmpty(queryPriceVO.getReceiveTel()) || ("undefined".equals(queryPriceVO.getReceiveMobile()) || "undefined".equals(queryPriceVO.getReceiveTel()))) {
            queryPriceVO.setReceiveMobile("18012341234");
        }

        queryPriceVO.setWeight(roundUpIfDecimal(params.getWeight()));

//        yidaPreOrder(queryPriceVO);
        queryPriceVO.setUserId(SecurityFrameworkUtils.getLoginUserId());



//        List<QueryPriceResVO> queryPriceResVOS = orderService.queryPrice(queryPriceVO);

        List<QueryPriceResVO> queryPriceResVOS =  orderPriceService.queryPrice(queryPriceVO);
//        queryPriceResVOS =  orderPriceService.queryPriceV2(queryPriceVO);

        List<MiniQueryPriceResVO> returnList = new ArrayList<>();
        if (CollUtil.isNotEmpty(queryPriceResVOS)) {
            for (QueryPriceResVO queryPriceResVO : queryPriceResVOS) {
                MiniQueryPriceResVO miniQueryPriceReqVO = new MiniQueryPriceResVO();
                BeanUtil.copyProperties(queryPriceResVO, miniQueryPriceReqVO);
                returnList.add(miniQueryPriceReqVO);
            }
        }
        return CommonResult.success(returnList);
    }

    /**
     * 查询价格
     */
    @Operation(summary = "查询价格V2", description = "根据收发件信息查询可用快递及价格(分组版本)")
    @ApiResponse(responseCode = "200", description = "成功返回分组后的价格信息")
    @PostMapping("/choosecom2")
    public CommonResult choosecom2(@RequestBody MiniQueryPriceReqVO params) {
        CommonResult<List<MiniQueryPriceResVO>> choosecom = this.choosecom(params);

        List<MiniQueryPriceResVO> result = new ArrayList<>();
        if (choosecom.isSuccess()) {
            List<MiniQueryPriceResVO> dataList = choosecom.getData();

            // 快递分组
            if (CollUtil.isNotEmpty(dataList)) {
                Map<Long, List<MiniQueryPriceResVO>> channelExpressMap = dataList.stream().collect(Collectors.groupingBy(channelExpressDO -> channelExpressDO.getExpressId()));

                channelExpressMap.forEach((key, items) -> {
                    MiniQueryPriceResVO item0 = items.get(0);

                    MiniQueryPriceResVO miniQueryPriceResVO = new MiniQueryPriceResVO();
                    miniQueryPriceResVO.setDeliveryLogo(item0.getDeliveryLogo());
                    miniQueryPriceResVO.setDeliveryName(item0.getDeliveryName());
                    miniQueryPriceResVO.setPickUpFast(item0.getPickUpFast());
                    miniQueryPriceResVO.setPickUpSlow(item0.getPickUpSlow());

                    for (int i = 0; i < items.size(); i++) {
                        items.get(i).setDeliveryName(items.get(i).getDeliveryName() + '-' + (i + 1));
                    }

                    miniQueryPriceResVO.setItems(items);
                    result.add(miniQueryPriceResVO);
                });
            }
        }

        return CommonResult.success(result);
    }

    /**
     * 预下单接口-实现黑名单查询
     * @param miniCreateOrderReqVO
     * @return
     */
    @Operation(summary = "预下单接口-查询是否黑黑名单", description = "查询是否黑黑名单")
    @ApiResponse(responseCode = "200", description = "成功返回预下单接口-查询是否黑黑名单信息")
    @PostMapping("/preOrder")
//    @AutoLog(value = "用户黑名单校验",businessType= LogBusinessType.KUAIDI)
    public CommonResult preOrder(@RequestBody MiniCreateOrderReqVO miniCreateOrderReqVO) {
        // 黑名单稳定后开启此代码
        miniCreateOrderReqVO.setOrderType(OrderConstants.ORDER_TYPE_USER);
        orderSubmitServiceImpl.validateUserStatus(miniCreateOrderReqVO);
        return CommonResult.success("");
    }

    /**
     * 查询价格
     */
    @Operation(summary = "查询价格-老接口", description = "查询物流价格-老接口")
    @ApiResponse(responseCode = "200", description = "成功返回查询价格-老接口信息")
    @PostMapping("/preOrderOld")
    public CommonResult yidaPreOrder(@RequestBody MiniCreateOrderReqVO miniCreateOrderReqVO) {
        // 下单时调第三方接口查询是否是黑名p
        YiDaPreOrderVo preOrderVo = new YiDaPreOrderVo();
        preOrderVo.setThirdNo(IdWorker.getIdStr());
        // 查询寄件人信息
        AddressDO sendAddress = addressService.getAddress(miniCreateOrderReqVO.getSenderAddrId());

        if (null == sendAddress) {
            return CommonResult.error(310002, "寄件人信息缺失");
        }

        // 查询收件人信息
        AddressDO receiveAddress = addressService.getAddress(miniCreateOrderReqVO.getReceiveAddrId());
        if (null == receiveAddress) {
            return CommonResult.error(310003, "收件人信息缺失");
        }
        CreateOrderVO createOrderVO = new CreateOrderVO();
//        createOrderVO.setChannelExpressId(expressChannel);

        preOrderVo.setSenderAddress(sendAddress.getAddress());
        preOrderVo.setSenderProvince(sendAddress.getProvince());
        preOrderVo.setSenderCity(sendAddress.getCity());
        preOrderVo.setSenderDistrict(sendAddress.getDistrict());
        preOrderVo.setSenderAddress(sendAddress.getAddress());
        preOrderVo.setSenderMobile(sendAddress.getMobileNumber());
        if (ObjectUtil.isEmpty(sendAddress.getMobileNumber()) && ObjectUtil.isNotEmpty(sendAddress.getLandlineNumber())) {
            preOrderVo.setSenderTel(sendAddress.getLandlineNumber());
        }
        preOrderVo.setSenderName(sendAddress.getContactPerson());

        preOrderVo.setReceiveAddress(receiveAddress.getAddress());
        preOrderVo.setReceiveProvince(receiveAddress.getProvince());
        preOrderVo.setReceiveCity(receiveAddress.getCity());
        preOrderVo.setReceiveDistrict(receiveAddress.getDistrict());
        preOrderVo.setReceiveAddress(receiveAddress.getAddress());
        preOrderVo.setReceiveMobile(receiveAddress.getMobileNumber());
        if (ObjectUtil.isEmpty(receiveAddress.getMobileNumber()) && ObjectUtil.isNotEmpty(receiveAddress.getLandlineNumber())) {
            preOrderVo.setReceiveTel(receiveAddress.getLandlineNumber());
        }
        preOrderVo.setReceiveName(receiveAddress.getContactPerson());

        preOrderVo.setWeight(miniCreateOrderReqVO.getWeight());
        preOrderVo.setGoods(miniCreateOrderReqVO.getGoods());
        preOrderVo.setPackageCount(miniCreateOrderReqVO.getPackageCount());
//        preOrderVo.setRemark(miniCreateOrderReqVO.getRemark());
//        preOrderVo.setGuaranteeValueAmount(miniCreateOrderReqVO.getGuaranteeValueAmount());
//        preOrderVo.setDeliveryType(miniCreateOrderReqVO.getDeliveryType());
//        preOrderVo.setDeliveryBusiness(miniCreateOrderReqVO.getDeliveryBusiness());
//        preOrderVo.setUserId(userId);
        preOrderVo.setGoods(miniCreateOrderReqVO.getGoods());
        preOrderVo.setPackageCount(miniCreateOrderReqVO.getPackageCount());
        preOrderVo.setWeight(miniCreateOrderReqVO.getWeight());
        try {
            yiDaApiService.preOrder(preOrderVo);
        } catch (Exception e) {
            return CommonResult.error(310002, e.getMessage());
        }

        return CommonResult.success("");
    }

    /**
     * 获取用户通讯录信息
     *
     * @param type 地址类型(1:寄件地址,3:同城地址)
     * @return
     */
    @Operation(summary = "获取用户地址列表", description = "获取当前用户的通讯录地址信息")
    @ApiResponse(responseCode = "200", description = "成功返回地址列表")
    @Parameter(name = "type", description = "地址类型(1:寄件地址,3:同城地址)")
    @GetMapping("/address/getUserAreaList")
    public CommonResult<List<MiniAddressVO>> getUserAreaList(@RequestParam(value = "type", required = false) String type) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        AddressExportReqVO exportReqVO = new AddressExportReqVO();
        exportReqVO.setMbrId(userId);
        exportReqVO.setType(type);
        List<AddressDO> addressDOList = addressService.getAddressList(exportReqVO);
        List<MiniAddressVO> miniAddressVOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(addressDOList)) {
            for (AddressDO addressDO : addressDOList) {
                MiniAddressVO miniAddressVO = new MiniAddressVO();
                BeanUtil.copyProperties(addressDO, miniAddressVO);

                StringBuilder builder = new StringBuilder();
                builder.append(addressDO.getProvince()).append(addressDO.getCity());
                if (StrUtil.isNotEmpty(addressDO.getDistrict())) {
                    builder.append(addressDO.getDistrict());
                }
                if (StrUtil.isNotEmpty(addressDO.getAddress())) {
                    builder.append(addressDO.getAddress());
                }
                miniAddressVO.setDetailedAddress(builder.toString());
                miniAddressVOS.add(miniAddressVO);
            }
        }
        return CommonResult.success(miniAddressVOS);
    }

    /**
     * 分页获取地址信息
     *
     * @param pageReqVO
     * @return
     */
    @Operation(summary = "分页获取地址信息", description = "分页获取当前用户的地址信息")
    @ApiResponse(responseCode = "200", description = "成功返回地址分页数据")
    @GetMapping("/address/getUserAreaPage")
    public CommonResult<PageResult<AddressRespVO>> getUserAreaList(@Valid AddressPageReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        pageReqVO.setMbrId(userId);
        PageResult<AddressDO> pageResult = addressService.getAddressPage(pageReqVO);
        PageResult<AddressRespVO> result = AddressConvert.INSTANCE.convertPage(pageResult);
        if (CollUtil.isNotEmpty(result.getList())) {
            for (AddressRespVO address : result.getList()) {
                StringBuilder builder = new StringBuilder();
                builder.append(address.getProvince()).append(address.getCity());
                if (StrUtil.isNotEmpty(address.getDistrict())) {
                    builder.append(address.getDistrict());
                }
                if (StrUtil.isNotEmpty(address.getAddress())) {
                    builder.append(address.getAddress());
                }
                address.setDetailedAddress(builder.toString());
            }
        }
        return CommonResult.success(result);
    }

    /**
     * 获取用户默认地址
     *
     * @param type 地址类型(1:寄件地址,3:同城地址)
     * @return
     */
    @Operation(summary = "获取用户默认地址", description = "获取当前用户的默认地址信息")
    @ApiResponse(responseCode = "200", description = "成功返回默认地址")
    @Parameter(name = "type", description = "地址类型(1:寄件地址,3:同城地址)")
    @GetMapping("/address/getDefaultAddress")
    public CommonResult<MiniAddressVO> getDefaultAddress(@RequestParam(value = "type", required = false) String type) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        AddressDO address = addressService.getDefaultAddress(type, userId);
        MiniAddressVO miniAddressVO = new MiniAddressVO();
        BeanUtil.copyProperties(address, miniAddressVO);
        return CommonResult.success(miniAddressVO);
    }

    /**
     * 根据id获取通讯录信息
     *
     * @param addressId
     * @return
     */
    @Operation(summary = "获取指定地址详情", description = "根据地址ID获取地址详情")
    @ApiResponse(responseCode = "200", description = "成功返回地址详情")
    @Parameter(name = "addressId", description = "地址ID", required = true)
    @GetMapping("/address/getUserArea")
    public CommonResult<MiniAddressVO> getUserArea(@RequestParam Long addressId) {
        AddressDO addressDO = addressService.getAddress(addressId);

        MiniAddressVO miniAddressVO = new MiniAddressVO();
        if (null != addressDO) {
            BeanUtil.copyProperties(addressDO, miniAddressVO);

            StringBuilder sb = new StringBuilder();
            sb.append(addressDO.getProvince());
            sb.append(addressDO.getCity());
            if (StrUtil.isNotEmpty(addressDO.getDistrict())) {
                sb.append(addressDO.getDistrict());
            }
            if (StrUtil.isNotEmpty(addressDO.getAddress())) {
                sb.append(addressDO.getAddress());
            }
            miniAddressVO.setDetailedAddress(sb.toString());
        }

        return CommonResult.success(miniAddressVO);
    }

    /**
     * 地址分析拆解
     *
     * @param content
     * @return
     */
    @Operation(summary = "地址智能解析", description = "通过百度API智能解析地址信息")
    @ApiResponse(responseCode = "200", description = "成功返回解析后的地址")
    @Parameter(name = "content", description = "需要解析的地址文本", required = true)
    @PostMapping("/address/aiarea")
    public CommonResult<MiniAiAddressVo> aiarea(@RequestParam("content") String content) {
        AiAddressVo address = baiDuApiServic.getAddress(content);
        MiniAiAddressVo miniAddressVO = new MiniAiAddressVo();
        if (null != address) {
            BeanUtil.copyProperties(address, miniAddressVO);
            return CommonResult.success(miniAddressVO);
        }

        AiAddressVo aliyunAddress = aliyunApiService.getAddress(content);
        if (null != aliyunAddress) {
            BeanUtil.copyProperties(aliyunAddress, miniAddressVO);
            return CommonResult.success(miniAddressVO);
        }

        return CommonResult.success(miniAddressVO);
    }


    /**
     * 地址分析拆解
     *
     * @param
     * @return
     */
    @Operation(summary = "地址智能解析V2", description = "通过百度API智能解析地址信息(请求体方式)")
    @ApiResponse(responseCode = "200", description = "成功返回解析后的地址")
    @PostMapping("/address/aiarea2")
    public CommonResult<MiniAiAddressVo> aiarea2(@RequestBody Map<String, String> params) {
        String content = params.get("content");
        if(ObjectUtil.isEmpty(content)){
            return CommonResult.error(500,"请填写地址详情");
        }

        MiniAiAddressVo miniAddressVO = new MiniAiAddressVo();

        String jdStatus = paramApi.getAddressRecognitionJdStatus(TenantContextHolder.getTenantId());
        if (ObjectUtil.isNotEmpty(jdStatus) && "0".equals(jdStatus)) {
            // 京东免费地址解析
            AiAddressVo jdAddress = jdApiService.getAddress(content);
            if (null != jdAddress) {
                BeanUtil.copyProperties(jdAddress, miniAddressVO);
                return CommonResult.success(miniAddressVO);
            }
        }

        AiAddressVo address = baiDuApiServic.getAddress(content);
        if (null != address) {
            BeanUtil.copyProperties(address, miniAddressVO);
            return CommonResult.success(miniAddressVO);
        }

        AiAddressVo aliyunAddress = aliyunApiService.getAddress(content);
        if (null != aliyunAddress) {
            BeanUtil.copyProperties(aliyunAddress, miniAddressVO);
            return CommonResult.success(miniAddressVO);
        }

        return CommonResult.success(miniAddressVO);
    }


    @Operation(summary = "地址京东智能解析V3", description = "通过京东API智能解析地址信息(请求体方式)")
    @ApiResponse(responseCode = "200", description = "成功返回解析后的地址")
    @PostMapping("/address/aiarea3")
    public CommonResult<MiniAiAddressVo> aiarea3(@RequestBody Map<String, String> params) {
        String content = params.get("content");
        if (ObjectUtil.isEmpty(content)) {
            return CommonResult.error(500, "请填写地址详情");
        }

        MiniAiAddressVo miniAddressVO = new MiniAiAddressVo();

        // 京东免费地址解析
        AiAddressVo jdAddress = jdApiService.getAddress(content);
        if (null != jdAddress) {
            BeanUtil.copyProperties(jdAddress, miniAddressVO);
            return CommonResult.success(miniAddressVO);
        }
//
//        AiAddressVo address = baiDuApiServic.getAddress(content);
//        if (null != address) {
//            BeanUtil.copyProperties(address, miniAddressVO);
//            return CommonResult.success(miniAddressVO);
//        }
//
//        AiAddressVo aliyunAddress = aliyunApiService.getAddress(content);
//        if (null != aliyunAddress) {
//            BeanUtil.copyProperties(aliyunAddress, miniAddressVO);
//            return CommonResult.success(miniAddressVO);
//        }

        return CommonResult.success(miniAddressVO);
    }


    /**
     * 地址分析拆解
     *
     * @param
     * @return
     */
    @Operation(summary = "批量地址智能解析", description = "批量解析多个地址信息")
    @ApiResponse(responseCode = "200", description = "成功返回解析后的地址列表")
    @PostMapping("/address/batch/aiarea")
    public CommonResult<List<MiniAiAddressVo>> batchAiarea(@RequestBody Map<String, String> params) {
        String content = params.get("content");
        if(ObjectUtil.isEmpty(content)){
            return CommonResult.error(500,"请填写地址详情");
        }


        String jdStatus = paramApi.getAddressRecognitionJdStatus(TenantContextHolder.getTenantId());
        // 使用 \n 作为分隔符
//        String[] lines = content.split("\n");
        String[] lines = content.split("。");
        List<MiniAiAddressVo> miniAiAddressList = new ArrayList<>();
        for (String line : lines) {
            MiniAiAddressVo miniAddressVO = new MiniAiAddressVo();
            if (ObjectUtil.isNotEmpty(jdStatus) && "0".equals(jdStatus)) {
                // 京东免费地址解析
                AiAddressVo jdAddress = jdApiService.getAddress(line);
                if (null != jdAddress) {
                    BeanUtil.copyProperties(jdAddress, miniAddressVO);
                    miniAiAddressList.add(miniAddressVO);

                    continue;
                }
            }
            AiAddressVo address = baiDuApiServic.getAddress(line);

            if (null != address) {
                BeanUtil.copyProperties(address, miniAddressVO);
                miniAiAddressList.add(miniAddressVO);
                continue;
            }

            AiAddressVo aliyunAddress = aliyunApiService.getAddress(content);
            if (null != aliyunAddress) {
                BeanUtil.copyProperties(aliyunAddress, miniAddressVO);
                miniAiAddressList.add(miniAddressVO);
                continue;
            }
        }

        return CommonResult.success(miniAiAddressList);
    }

    /**
     * 新增用户通讯录
     *
     * @param params
     * @return
     */
    @Operation(summary = "新增地址", description = "添加新的收/寄件地址信息")
    @ApiResponse(responseCode = "200", description = "成功返回新增的地址信息")
    @PostMapping("/address/add")
    public CommonResult<MiniAddressVO> addUserArea(@RequestBody MiniAddressVO params) {

        if (ObjectUtil.isEmpty(params.getContactPerson()) || params.getContactPerson().length() < 2) {
            return CommonResult.error(310004,"姓名不能为空且不能少于两个字");
        }

        if (ObjectUtil.isEmpty(params.getAddress()) || params.getAddress().length() > 50 || params.getAddress().length() < 4 ) {
            return CommonResult.error(310004,"详细地址不能为空且大于四个字,少于五十个字");
        }

        Long userId = SecurityFrameworkUtils.getLoginUserId();

        AddressCreateReqVO createReqVO = new AddressCreateReqVO();

        BeanUtil.copyProperties(params, createReqVO);
        createReqVO.setMbrId(userId);
        if (ObjectUtil.isEmpty(createReqVO.getIsDefault())) {
            createReqVO.setIsDefault(true);
        }

        Long id = addressService.createAddress(createReqVO);

        AddressDO addressDO = addressService.getAddress(id);
        MiniAddressVO miniAddressVO = new MiniAddressVO();
        BeanUtil.copyProperties(addressDO, miniAddressVO);
        StringBuilder builder = new StringBuilder();
        builder.append(addressDO.getProvince()).append(addressDO.getCity());
        if (StrUtil.isNotEmpty(addressDO.getDistrict())) {
            builder.append(addressDO.getDistrict());
        }
        if (StrUtil.isNotEmpty(addressDO.getAddress())) {
            builder.append(addressDO.getAddress());
        }
        miniAddressVO.setDetailedAddress(builder.toString());

        return CommonResult.success(miniAddressVO);
    }

    /**
     * 新增用户通讯录
     *
     * @param params
     * @return
     */
    @Operation(summary = "编辑地址", description = "修改已有的收/寄件地址信息")
    @ApiResponse(responseCode = "200", description = "地址更新成功")
    @PostMapping("/address/edit")
    public CommonResult editUserArea(@RequestBody MiniAddressVO params) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();

        if (ObjectUtil.isEmpty(params.getContactPerson()) || params.getContactPerson().length() < 2) {
            return CommonResult.error(310004,"姓名不能为空且不能少于两个字");
        }

        if (ObjectUtil.isEmpty(params.getAddress()) || params.getAddress().length() > 50 || params.getAddress().length() < 4 ) {
            return CommonResult.error(310004,"详细地址不能为空且大于四个字,少于五十个字");
        }

        AddressUpdateReqVO updateVo = new AddressUpdateReqVO();

        BeanUtil.copyProperties(params, updateVo);
        if (ObjectUtil.isEmpty(updateVo.getIsDefault())) {
            updateVo.setIsDefault(true);
        }
        updateVo.setMbrId(userId);

        addressService.updateAddress(updateVo);

        return CommonResult.success("");
    }

    /**
     * 删除用户通讯录
     *
     * @param addressId
     * @return
     */
    @Operation(summary = "删除地址", description = "删除指定ID的地址信息")
    @ApiResponse(responseCode = "200", description = "地址删除成功")
    @Parameter(name = "addressId", description = "要删除的地址ID", required = true)
    @PostMapping("/address/del")
    public CommonResult delArea(@RequestParam Long addressId) {
        addressService.deleteAddress(addressId);
        return CommonResult.success("");
    }

    @Operation(summary = "批量删除地址", description = "批量删除多个地址信息")
    @ApiResponse(responseCode = "200", description = "地址批量删除成功")
    @Parameter(name = "addressIds", description = "要删除的地址ID列表，以逗号分隔", required = true)
    @PostMapping("/address/delAddresses")
    public CommonResult delChooseArea(String addressIds) {
        if (StrUtil.isEmpty(addressIds)){
            throw new ServiceException(new ErrorCode(400,"请选择需要删除的地址"));
        }
        String[] splitAdd = addressIds.split(",");
        Assert.notEmpty(splitAdd,"请选择需要删除的地址");
        if (ObjectUtil.isEmpty(splitAdd)){
            throw new ServiceException(new ErrorCode(400,"请选择需要删除的地址"));
        }
        // 查询地址
        List<AddressDO> deleteAddress = addressService.getAddressList(Arrays.stream(splitAdd).map(Long::valueOf).collect(Collectors.toList()));

        for(AddressDO bean: deleteAddress){
            if(MbrConstants.MBR_ADDRESS_CLASSIFIED_MERCHANTS.equals(bean.getClassified())){
                throw new ServiceException(new ErrorCode(400,"商家地址不能删除"));
            }
        }

        for (String address : splitAdd) {
            addressService.deleteAddress(Long.valueOf(address));
        }
        return CommonResult.success("");
    }


    /**
     * 获取默认下单地址
     *
     * @param type
     * @return
     */
    @Operation(summary = "获取默认下单地址", description = "获取当前用户的默认下单地址信息")
    @ApiResponse(responseCode = "200", description = "成功返回默认地址信息")
    @Parameter(name = "type", description = "地址类型(1:寄件地址,3:同城地址)")
    @GetMapping("/address/getdefaultarea")
    public CommonResult<MiniAddressVO> getdefaultarea(@RequestParam(value = "type", required = false) String type) {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
//        List<AddressDO> addressDOS = addressService.getListByUserId(loginUserId);
        AddressExportReqVO exportReqVO = new AddressExportReqVO();
        exportReqVO.setMbrId(loginUserId);
        exportReqVO.setType(type);
        List<AddressDO> addressDOS = addressService.getAddressList(exportReqVO);
        MiniAddressVO miniAddressVO = new MiniAddressVO();

        if (CollUtil.isNotEmpty(addressDOS)) {
            AddressDO addressDO = addressDOS.get(0);

            BeanUtil.copyProperties(addressDO, miniAddressVO);


            StringBuilder sb = new StringBuilder();
            sb.append(addressDO.getProvince());
            sb.append(addressDO.getCity());
            if (StrUtil.isNotEmpty(addressDO.getDistrict())) {
                sb.append(addressDO.getDistrict());
            }
            if (StrUtil.isNotEmpty(addressDO.getAddress())) {
                sb.append(addressDO.getAddress());
            }
            miniAddressVO.setDetailedAddress(sb.toString());
        }

        return CommonResult.success(miniAddressVO);
    }

    /**
     * 获取用户各个状态下的订单数量
     * @param params
     * @return
     */
    @Operation(summary = "获取订单数量统计", description = "获取用户各个状态下的订单数量")
    @ApiResponse(responseCode = "200", description = "成功返回订单数量统计信息")
    @PostMapping("/number")
    public CommonResult<OrderNumRespVO> getOrderNumber(@RequestBody MiniOrderPageReqVO params) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        OrderNumRespVO orderNumRespVO = new OrderNumRespVO();

        // 加一天，默认查询把当天也算进去
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(params.getEndDate(), formatter);
        LocalDate nextDay = date.plusDays(1);
        params.setEndDate(formatter.format(nextDay));

        // 统计用户下各种订单状态下的数据
        List<OrderStatusNumRespVO> orderStatusNumRespVOS = orderService.getOrderNumber(params, userId);
        if (!CollectionUtils.isEmpty(orderStatusNumRespVOS)) {
            orderStatusNumRespVOS.stream().forEach(statusResp -> {
                switch (statusResp.getStatus()) {
                    case OrderConstants.ORDER_STATUS_WAITING_PARTS: orderNumRespVO.setWaitingPartsNum(statusResp.getNum()); break;
                    case OrderConstants.ORDER_STATUS_RECEIVED_ORDERS: orderNumRespVO.setWaitingPartsNum(orderNumRespVO.getWaitingPartsNum() + statusResp.getNum()); break;
                    case OrderConstants.ORDER_STATUS_IN_TRANSIT: orderNumRespVO.setInTransitNum(statusResp.getNum()); break;
                    case OrderConstants.ORDER_STATUS_SIGN_FOR: orderNumRespVO.setSignForNum(statusResp.getNum()); break;
                    case OrderConstants.ORDER_STATUS_CANCELLED: orderNumRespVO.setCancelledNum(statusResp.getNum()); break;
                    case OrderConstants.ORDER_STATUS_OVERWEIGHT_COMPENSATION: orderNumRespVO.setOverweightNum(statusResp.getNum()); break;
                }
            });
        }

        return CommonResult.success(orderNumRespVO);
    }


    public static String convertInvalidDateToNextMonthFirstDay(String invalidDateString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        try {
            // 尝试解析日期
            LocalDate parsedDate = LocalDate.parse(invalidDateString, formatter);
            return parsedDate.format(formatter); // 如果成功解析，直接返回
        } catch (Exception e) {
            // 日期格式无效或内容错误时的处理
            LocalDate now = LocalDate.now();
            LocalDate previousMonthFirstDay = now.minusMonths(1).withDayOfMonth(1);
            return previousMonthFirstDay.format(formatter); // 返回前一个月的第一天
        }
    }

    /**
     * 查询用户的订单列表
     *
     * @return
     */
    @Operation(summary = "查询订单列表", description = "查询用户的订单列表，支持多种过滤条件")
    @ApiResponse(responseCode = "200", description = "成功返回订单列表数据")
    @PostMapping("/lists")
//    @AutoLog(value = "用户查看订单列表",businessType= LogBusinessType.KUAIDI)
    public CommonResult<PageResult<MiniOrderListResVO>> getOrderList(@RequestBody MiniOrderPageReqVO params) {
        // log.error("用户查看订单列表: {}", params);

        try {

            // 时间合理化
            String startDate = params.getStartDate();
            if (StringUtils.isNotEmpty(startDate)) {
                startDate = this.convertInvalidDateToNextMonthFirstDay(startDate);
                params.setStartDate(startDate);
            }

            String endDate = params.getEndDate();
            if (StringUtils.isNotEmpty(endDate)) {
                endDate = this.convertInvalidDateToNextMonthFirstDay(endDate);
                params.setEndDate(endDate);
            }

            Long userId = SecurityFrameworkUtils.getLoginUserId();
            Long tenantId = TenantContextHolder.getTenantId();

            OrderPageReqVO pageReqVO = new OrderPageReqVO();

            // 进行中的包括（已付款,运输中,待支付,已接单,已取件,超重补差,超轻退款）
//            status = OrderConstants.ORDER_STATUS_WAITING_PARTS;

            PageResult<MiniOrderListResVO> returnPage = new PageResult();
            List<Long> userIds = new ArrayList<>();

            List<String> notStatus = new ArrayList<>();

            // 查询是否查询团队订单
            if ("2".equals(params.getType())) {
                // 查询当前客户信息。
                MemberUserRespDTO memberUserRespDTO = memberUserApi.getUser(userId);
                if (ObjectUtil.isEmpty(memberUserRespDTO)) {
                    return CommonResult.error(500, "当前用户不存在");
                }
                if ("1".equals(memberUserRespDTO.getUserLevel())) {
                    return CommonResult.success(returnPage);
                }
                // 查询当前用户的团队
                userIds = memberUserApi.getTeamIdsById(userId).getData();
                // 更改：上面方法是查询当前用户的直推和间推，查询用户上下级表
//            userIds = userParentApi.getTeamIdsById(userId).getData();
                if (ObjectUtil.isEmpty(userIds)) {
                    return CommonResult.success(returnPage);
                }
                // 团队列表只差有效订单
                notStatus.add(OrderConstants.ORDER_STATUS_PICKED_SIX);
                notStatus.add(OrderConstants.ORDER_STATUS_CANCELLED);
                notStatus.add(OrderConstants.ORDER_STATUS_WAITING_ABNORMAL);
            }
            String status = null;
            List<String> statusList = new ArrayList<>();
            pageReqVO.setCompensatingResult(params.getCompensatingResult());
            pageReqVO.setCompensatingState(params.getCompensatingState());
            if ("2".equals(params.getStatus())) {
                statusList.add(OrderConstants.ORDER_STATUS_WAITING_PARTS);
                statusList.add(OrderConstants.ORDER_STATUS_RECEIVED_ORDERS);
            } else if ("3".equals(params.getStatus())) {
                status = OrderConstants.ORDER_STATUS_IN_TRANSIT;
            } else if ("4".equals(params.getStatus())) {
                status = OrderConstants.ORDER_STATUS_SIGN_FOR;
            } else if ("5".equals(params.getStatus())) {
                status = OrderConstants.ORDER_STATUS_CANCELLED;
            } else if ("6".equals(params.getStatus())) {
                pageReqVO.setCompensatingState(OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO);
                pageReqVO.setCompensatingResult(OrderConstants.ORDER_COMPENSATING_RESULT_TWO);
            }
            if (!"2".equals(params.getType())) {
                pageReqVO.setUserId(userId);
            }
            boolean weightFal = paramApi.isLessWeightRefundAuto(tenantId);

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(params.getStartDate()) && org.apache.commons.lang3.StringUtils.isNotEmpty(params.getEndDate())) {
                LocalDateTime endTime = DateUtils.toLocalDateTime(params.getEndDate());
                if (null != endTime) {
                    endTime = endTime.with(LocalTime.MAX);
                }
                LocalDateTime[] createTime = new LocalDateTime[]{DateUtils.toLocalDateTime(params.getStartDate()), endTime};
                // log.error("设置createTime: {}", createTime);
                pageReqVO.setCreateTime(createTime);
            }
            pageReqVO.setPageNo(params.getPageNo());
            pageReqVO.setPageSize(params.getPageSize());
            pageReqVO.setStatus(status);
            pageReqVO.setUserIdList(userIds);
            pageReqVO.setStatusList(statusList);
            pageReqVO.setSearchText(params.getSearchText());
            pageReqVO.setNotStatusList(notStatus);
            pageReqVO.setPayType(params.getPayType());
//        if (params.getPageNo() > 1) {
//            pageReqVO.setLastId(params.getLastId());
//        }else {
//            params.setLastId(null);
//        }
//        if (params.getLastId() != null) {
//            pageReqVO.setPageNo(1);
//        }

            // 工单选择订单：自己账号下，没有订单号的订单不返回
            if ("1".equals(params.getType()) && ObjectUtil.isNotEmpty(params.getWorkOrderFlag()) && 1 == params.getWorkOrderFlag()) {
                pageReqVO.setOrderNoNotNull(true);
            }
            pageReqVO.setTenantId(tenantId);

            // TODO 2024-12-10 取消订单相关ES操作
//            PageResult<OrderMiniRespPageVO> orderPage = orderEsService.orderMiniPage(pageReqVO);
            PageResult<OrderMiniRespPageVO> orderPage = orderService.selectMiniPage(pageReqVO);

//        if (!CollectionUtils.isEmpty(orderPage.getList()) && ObjectUtil.isNotEmpty(params.getLastId()) && ObjectUtil.isEmpty(params.getLastId())) {
////        if (!CollectionUtils.isEmpty(orderPage.getList()) && ObjectUtil.isNotEmpty(params.getLastId())) {
//            List<OrderMiniRespPageVO> collect = orderPage.getList().stream().filter(vo -> vo.getId() < params.getLastId()).collect(Collectors.toList());
//            orderPage.setList(collect);
//        }

            Map<Long, ChannelExpressDetailVO> channelMap = new HashMap<>();

            // 转map
            if (ObjectUtil.isNotEmpty(orderPage)) {
                List<Long> ids = Optional.ofNullable(orderPage.getList()).map(Collection::stream).orElseGet(Stream::empty).map(OrderMiniRespPageVO::getChannelExpressId).filter(Objects::nonNull).filter(s -> !s.equalsIgnoreCase("null")).map(Long::valueOf).distinct().collect(Collectors.toList());
                List<ChannelExpressDetailVO> channelExpressList = channelExpressService.getChannelExpressDetailList(null, ids, tenantId);
                channelMap = ObjectUtil.isNotEmpty(channelExpressList) ? channelExpressList.stream().collect(Collectors.toMap(ChannelExpressDetailVO::getChannelExpressId, channelList -> channelList)) : null;
            }


            returnPage.setTotal(orderPage.getTotal());
            // 返回给小程序的对象
            List<MiniOrderListResVO> miniOrderListResVOS = new ArrayList<>();

            if (CollUtil.isNotEmpty(orderPage.getList())) {
                for (OrderMiniRespPageVO orderDO : orderPage.getList()) {
                    MiniOrderListResVO miniOrderListResVO = new MiniOrderListResVO();
                    BeanUtil.copyProperties(orderDO, miniOrderListResVO);
                    miniOrderListResVO.setIsShow(false);
                    String statusLabel = DictFrameworkUtils.getDictDataLabel(DictTypeConstants.KUAIDI_STATUS, orderDO.getStatus());
                    miniOrderListResVO.setStatusStr(statusLabel);
                    miniOrderListResVO.setThirdExpressName(orderDO.getThirdExpressName());
                    miniOrderListResVO.setThirdExpressType(orderDO.getThirdExpressType());
                    miniOrderListResVO.setMessage(orderDO.getMessage());
                    miniOrderListResVO.setCreateTime(LocalDateTimeUtil.formatNormal(orderDO.getCreateTime()));
                    if (ObjectUtil.isNotEmpty(orderDO.getChannelExpressId()) && ObjectUtil.isNotEmpty(channelMap)) {
                        Long channelExpressIdLog = Long.parseLong(orderDO.getChannelExpressId());
                        miniOrderListResVO.setLogo(ObjectUtil.isNotEmpty(channelMap.get(channelExpressIdLog)) ? channelMap.get(channelExpressIdLog).getLogo() : "");
                    }
                    if (StrUtil.isNotEmpty(orderDO.getChannelExpressId()) && ObjectUtil.isEmpty(orderDO.getTenantChannelId())) {
                        try {
                            ChannelExpressDetailVO channelExpressDetail = channelExpressService.getChannelExpressDetail(Long.valueOf(orderDO.getChannelExpressId()));
                            if (ObjectUtil.isNotEmpty(channelExpressDetail)) {
                                miniOrderListResVO.setChannelExpressName(channelExpressDetail.getName());
                            }
                        } catch (Exception e) {
                            log.error("1", e);
                        }
                    } else if (StrUtil.isNotEmpty(orderDO.getChannelExpressId()) && ObjectUtil.isNotEmpty(orderDO.getTenantChannelId())) {
                        try {
                            KdChannelExportReqVO channelAndExpressName = kdChannelService.getChannelAndExpressName(Long.valueOf(orderDO.getChannelExpressId()));
                            if (ObjectUtil.isNotEmpty(channelAndExpressName)) {
                                miniOrderListResVO.setChannelExpressName(channelAndExpressName.getExpressName());
                                miniOrderListResVO.setLogo(channelAndExpressName.getExpressLogo());
                            }
                        } catch (Exception e) {
                            log.error("2", e);
                        }
                    }
                    // 发件人名称脱敏
                    if ("2".equals(params.getType()) && ObjectUtil.isNotEmpty(orderDO.getSenderName())) {
                        String name = nameDesensitization(orderDO.getSenderName());
                        miniOrderListResVO.setSenderName(name);
                    } else if (!"2".equals(params.getType())) {
                        miniOrderListResVO.setSenderName(orderDO.getSenderName());
                    }
                    // 发件人名称脱敏
                    if ("2".equals(params.getType()) && ObjectUtil.isNotEmpty(orderDO.getReceiveName())) {
                        String name = nameDesensitization(orderDO.getReceiveName());
                        miniOrderListResVO.setRecipientsName(name);
                    } else if (!"2".equals(params.getType())) {
                        miniOrderListResVO.setRecipientsName(orderDO.getReceiveName());
                    }
                    if ("2".equals(params.getType()) && ObjectUtil.isNotEmpty(orderDO.getUserId())) {
                        if (ObjectUtil.isNotEmpty(orderDO.getUserId())) {
                            CacheUserDTO cacheUserDTO = cacheService.getUserInfo(orderDO.getUserId());
                            orderDO.setOrderMobile(ObjectUtil.isNotEmpty(cacheUserDTO) ? cacheUserDTO.getMobile() : "");
                        }
                        String phone = orderDO.getOrderMobile().replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
                        miniOrderListResVO.setOrderPhone(phone);
                    }
                    // 运单号脱敏
                    if ("2".equals(params.getType()) && ObjectUtil.isNotEmpty(orderDO.getOrderNo())) {
                        String orderNo = maskBillNumber(orderDO.getOrderNo(), 6);
                        miniOrderListResVO.setOrderNo(orderNo);
                    }

                    if (ObjectUtil.isNotEmpty(orderDO.getOrderName())) {
                        String name = nameDesensitization(orderDO.getOrderName());
                        miniOrderListResVO.setOrderName(name);
                    }
                    miniOrderListResVO.setRecipientsProvince(orderDO.getReceiveProvince());
//                miniOrderListResVO.setRecipientsName(orderDO.getReceiveName());

                    // 如果是超重补差 状态变更 为 "运输中"
                    if (!weightFal && OrderConstants.ORDER_STATUS_REFUND.equals(miniOrderListResVO.getStatus())) {
                        miniOrderListResVO.setStatus(OrderConstants.ORDER_STATUS_IN_TRANSIT);
                        miniOrderListResVO.setStatusStr("运输中");
                    }
                    if (ObjectUtil.isNotEmpty(orderDO.getRefundFlag())) {
                        String dictDataLabel = DictFrameworkUtils.getDictDataLabel(DictTypeConstants.ORDER_REFUND_FLAG, orderDO.getRefundFlag());
                        if (ObjectUtil.isNotEmpty(dictDataLabel)) {
                            miniOrderListResVO.setRefundFlagStr(dictDataLabel);
                        }
                    }

                    FeeUserDO feeUserDO = feeUserService.getFeeUser(orderDO.getId().toString());
                    // 查询用户费用明细
                    if (ObjectUtil.isNotEmpty(feeUserDO)) {
                        miniOrderListResVO.setPayAmount(feeUserDO.getPayAmount());
                    }

                    // 收件人信息
                    OrderContactsDO recevierContacts = contactsService.getOrderContactsByOrderId(orderDO.getId().toString(), "receive");
                    if (ObjectUtil.isNotEmpty(recevierContacts)) {
                        miniOrderListResVO.setReceiveAddressDetail(recevierContacts.getProvince() + recevierContacts.getCity() + recevierContacts.getDistrict() + recevierContacts.getAddress());
                    }
                    if(OrderConstants.ORDER_CHANNEL_KDNIAO.equals(orderDO.getChannel()) && ObjectUtil.isEmpty(orderDO.getOrderNo())
                            && OrderConstants.ORDER_STATUS_WAITING_PARTS.equals(orderDO.getStatus())){
                        miniOrderListResVO.setOrderNo("预计5秒内更新单号,请刷新页面");
                    }

                    miniOrderListResVOS.add(miniOrderListResVO);
                }
            }
            returnPage.setList(miniOrderListResVOS);
            return CommonResult.success(returnPage);
        } catch (Exception ex) {
            log.error("查询订单列表异常", ex);
            return CommonResult.success(null);
        }
//        dataMap.put("handle", handle);
//        dataMap.put("list", listMap);
//        dataMap.put("Shcount", 0);
//        dataMap.put("Jicount", size);
//        result.put("data", dataMap);

    }

    /**
     * 脱敏运单号，将中间部分数字替换为星号
     *
     * @param billNumber 运单号
     * @param maskLength 要脱敏的长度（通常是中间几位数字）
     * @return 脱敏后的运单号
     */
    public static String maskBillNumber(String billNumber, int maskLength) {
        if (billNumber == null || billNumber.length() < maskLength) {
            return billNumber; // 如果运单号长度小于需要脱敏的长度，则直接返回原运单号
        }

        int start = (billNumber.length() - maskLength) / 2; // 计算开始替换的位置
        int end = start + maskLength; // 计算结束替换的位置

        StringBuilder maskedBillNumber = new StringBuilder(billNumber);
        for (int i = start; i < end; i++) {
            maskedBillNumber.setCharAt(i, '*'); // 将指定位置的字符替换为星号
        }

        return maskedBillNumber.toString();
    }


    public String nameDesensitization(String name) {
        if (ObjectUtil.isEmpty(name)) {
            return name;
        }
        char[] sArr = name.toCharArray();
        if (sArr.length == 2) {
            return sArr[0] + "**";
        } else if (sArr.length > 2) {
            for (int i = 1; i < sArr.length; i++) {
                // if ('·' != sArr[i]) {
                sArr[i] = '*';
                // }
            }
            return new String(sArr);
        }
        return name;
    }



    /**
     * 构建查询价格参数
     */
    private QueryPriceVO buildQueryPriceVO(MiniQueryBatchPriceReqVO params, MiniBatchReceiveReqVO bean, Long userId) {
        QueryPriceVO queryPriceVO = new QueryPriceVO();
        // 复制发件人信息
        BeanUtil.copyProperties(params, queryPriceVO);

        // 设置收件人信息
        queryPriceVO.setReceiveAddressDetail(bean.getReceiveProvince() + bean.getReceiveCity() + bean.getReceiveDistrict());
        queryPriceVO.setSenderAddressDetail(params.getSenderProvince() + params.getSenderCity() + params.getSenderDistrict());
        queryPriceVO.setGoods(bean.getGoods());
        queryPriceVO.setWeight(bean.getWeight());
        queryPriceVO.setPackageCount(bean.getPackageCount());
        queryPriceVO.setReceiveProvince(bean.getReceiveProvince());
        queryPriceVO.setReceiveCity(bean.getReceiveCity());
        queryPriceVO.setReceiveDistrict(bean.getReceiveDistrict());
        queryPriceVO.setReceiveName(bean.getReceiveName());
        queryPriceVO.setReceiveTel(bean.getReceiveTel());
        queryPriceVO.setReceiveAddress(bean.getReceiveAddress());
        queryPriceVO.setVloumHeight(bean.getVloumHeight());
        queryPriceVO.setVloumLong(bean.getVloumLong());
        queryPriceVO.setVloumWidth(bean.getVloumWidth());

        // 设置默认值
        if (StrUtil.isEmpty(queryPriceVO.getSenderAddress())) {
            queryPriceVO.setSenderAddress("前面左转");
        }
        if (StrUtil.isEmpty(queryPriceVO.getSenderMobile()) && StrUtil.isEmpty(queryPriceVO.getSenderTel()) ||
                ("undefined".equals(queryPriceVO.getSenderMobile()) || "undefined".equals(queryPriceVO.getSenderTel()))) {
            queryPriceVO.setSenderMobile("18012341234");
        }
        if (ObjectUtil.isEmpty(queryPriceVO.getGoods())) {
            queryPriceVO.setGoods("日用品");
        }
        if (ObjectUtil.isEmpty(queryPriceVO.getPackageCount())) {
            queryPriceVO.setPackageCount(1);
        }
        if (StrUtil.isEmpty(queryPriceVO.getReceiveMobile()) && StrUtil.isEmpty(queryPriceVO.getReceiveTel()) ||
                ("undefined".equals(queryPriceVO.getReceiveMobile()) || "undefined".equals(queryPriceVO.getReceiveTel()))) {
            queryPriceVO.setReceiveMobile("18012341234");
        }

        // 设置用户ID
        queryPriceVO.setUserId(userId);

        return queryPriceVO;
    }


    /**
     * 批量查询价格
     */
    @Operation(summary = "批量查询物流价格", description = "根据批量收件人信息查询可用快递及价格")
    @ApiResponse(responseCode = "200", description = "成功返回批量查询价格结果")
    @PostMapping("/batch/choosecom")
    public CommonResult<List<MiniBatchQueryPriceResVO>> batchChoosecom(@RequestBody MiniQueryBatchPriceReqVO params) {
        if(ObjectUtil.isEmpty(params.getBatchReceive())){
            return CommonResult.error(500, "收件人信息不能为空");
        }

        // 使用LinkedHashMap保持插入顺序
        Map<String,MiniBatchQueryPriceResVO> expressMap = new LinkedHashMap<>();
        AtomicBoolean isFirstTime = new AtomicBoolean(true);
        int errorCount = 1;

        // 创建CompletableFuture列表，并保持顺序
        List<CompletableFuture<List<QueryPriceResVO>>> futures = new ArrayList<>();

        // 创建锁对象
        Object lockObj = new Object();


        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Long tenantId = TenantContextHolder.getTenantId();
        // 先收集所有的Future
        for(MiniBatchReceiveReqVO bean: params.getBatchReceive()) {
            final int currentErrorCount = errorCount++;
            CompletableFuture<List<QueryPriceResVO>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    TenantContextHolder.setTenantId(tenantId);
                    QueryPriceVO queryPriceVO = buildQueryPriceVO(params, bean, userId);
                    List<QueryPriceResVO> queryPriceResVOS = orderPriceService.batchQueryPrice(queryPriceVO);

                    if (CollUtil.isEmpty(queryPriceResVOS)) {
                        String errorMsg = "您当前批量寄件中第"+currentErrorCount+"个收件人未查询到价格信息";
                        log.error(errorMsg);
                        throw new RuntimeException(errorMsg); // 使用RuntimeException替代ServiceException
                    }

                    // 对每个快递公司只保留最低价格的报价
                    return queryPriceResVOS.stream()
                            .collect(Collectors.groupingBy(QueryPriceResVO::getExpressId))
                            .values()
                            .stream()
                            .map(group -> group.stream()
                                    .min(Comparator.comparing(QueryPriceResVO::getLastPrice))
                                    .orElse(null))
                            .collect(Collectors.toList());
                } catch (Exception e) {
                    log.error("查询价格异常", e);
                    throw new RuntimeException(e.getMessage()); // 确保只传递错误消息
                }
            }, threadPoolExecutor);

            futures.add(future);
        }

        try {
            // 等待所有查询完成，设置超时时间为15秒
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(15, TimeUnit.SECONDS);

            // 按顺序处理结果
            for(CompletableFuture<List<QueryPriceResVO>> future : futures) {
                List<QueryPriceResVO> result = future.get();
                synchronized (lockObj) {
                    Map<String,MiniBatchQueryPriceResVO> newMap = batchChooseMapChange(
                            buildQueryPriceVO(params, params.getBatchReceive().get(futures.indexOf(future)), userId),
                            new LinkedHashMap<>(expressMap),
                            result,
                            isFirstTime.get()
                    );
                    expressMap.clear();
                    expressMap.putAll(newMap);
                    isFirstTime.set(false);
                }
            }
        } catch (Exception e) {
            // 获取最原始的错误消息
            String errorMessage = e.getMessage();
            if (e.getCause() != null) {
                errorMessage = e.getCause().getMessage();
            }
            return CommonResult.error(500, errorMessage);
        }

        // 将 Map 转换为 List，保持顺序
        List<MiniBatchQueryPriceResVO> list = new ArrayList<>(expressMap.values());

        // 计算总金额
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(t -> {
                if (t != null && t.getItems() != null) {
                    BigDecimal totalAmount = t.getItems().stream()
                            .map(MiniBatchPriceResVO::getTenantPayAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    t.setTenantPayAmount(totalAmount);
                }
            });
        }

        return CommonResult.success(list);
    }

    public Map<String,MiniBatchQueryPriceResVO> batchChooseMapChange(QueryPriceVO queryPriceVO,Map<String,MiniBatchQueryPriceResVO> expressMap,List<QueryPriceResVO> queryPriceResVOS,Boolean isFirstTime){
        Map<String,MiniBatchQueryPriceResVO> newExpressMap = new LinkedHashMap<>();
        for (QueryPriceResVO queryPriceResVO : queryPriceResVOS) {
            MiniBatchQueryPriceResVO miniBatchQueryPriceResVO = new MiniBatchQueryPriceResVO();
            String expressId = queryPriceResVO.getExpressId()+"";
            if(isFirstTime){
                // 设置快递名称
                miniBatchQueryPriceResVO.setDeliveryLogo(queryPriceResVO.getDeliveryLogo());
                miniBatchQueryPriceResVO.setExpressId(queryPriceResVO.getExpressId());
                miniBatchQueryPriceResVO.setDeliveryName(queryPriceResVO.getDeliveryName());
                miniBatchQueryPriceResVO.setLastPrice(queryPriceResVO.getLastPrice());
                miniBatchQueryPriceResVO.setVipPrice(queryPriceResVO.getVipPrice());
                miniBatchQueryPriceResVO.setTag1(queryPriceResVO.getTag1());
                miniBatchQueryPriceResVO.setTag2(queryPriceResVO.getTag2());
                miniBatchQueryPriceResVO.setDefPrice(queryPriceResVO.getDefPrice());
                miniBatchQueryPriceResVO.setPrice(queryPriceResVO.getPrice());
                // 设置快递
                List<MiniBatchPriceResVO> miniBatchPriceResList = new ArrayList<>();
                MiniBatchPriceResVO miniQueryPriceReq = new MiniBatchPriceResVO();
                BeanUtil.copyProperties(queryPriceResVO, miniQueryPriceReq);
                miniQueryPriceReq.setSenderCity(queryPriceVO.getSenderProvince());
                miniQueryPriceReq.setReceiveCity(queryPriceVO.getReceiveProvince());
                miniQueryPriceReq.setWeight(queryPriceVO.getWeight());
                miniQueryPriceReq.setGoods(queryPriceVO.getGoods());
                miniBatchPriceResList.add(miniQueryPriceReq);
                miniBatchQueryPriceResVO.setItems(miniBatchPriceResList);
                newExpressMap.put(expressId,miniBatchQueryPriceResVO);
            } else {
                if(expressMap.containsKey(expressId)){
                    miniBatchQueryPriceResVO = expressMap.get(expressId);
                    BigDecimal lastPrice = (ObjectUtil.isNotEmpty(miniBatchQueryPriceResVO.getLastPrice()) ? miniBatchQueryPriceResVO.getLastPrice() : BigDecimal.ZERO).
                            add(ObjectUtil.isNotEmpty(queryPriceResVO.getLastPrice()) ? queryPriceResVO.getLastPrice() : BigDecimal.ZERO);

                    BigDecimal vipPrice = (ObjectUtil.isNotEmpty(miniBatchQueryPriceResVO.getVipPrice()) ? miniBatchQueryPriceResVO.getVipPrice() : BigDecimal.ZERO).
                            add(ObjectUtil.isNotEmpty(queryPriceResVO.getVipPrice()) ? queryPriceResVO.getVipPrice() : BigDecimal.ZERO);

                    miniBatchQueryPriceResVO.setLastPrice(lastPrice);
                    miniBatchQueryPriceResVO.setVipPrice(vipPrice);
                    // 设置快递
                    List<MiniBatchPriceResVO> miniBatchPriceResList = miniBatchQueryPriceResVO.getItems();
                    MiniBatchPriceResVO miniQueryPriceReq = new MiniBatchPriceResVO();
                    BeanUtil.copyProperties(queryPriceResVO, miniQueryPriceReq);
                    miniQueryPriceReq.setSenderCity(queryPriceVO.getSenderProvince());
                    miniQueryPriceReq.setReceiveCity(queryPriceVO.getReceiveProvince());
                    miniQueryPriceReq.setWeight(queryPriceVO.getWeight());
                    miniQueryPriceReq.setGoods(queryPriceVO.getGoods());
                    miniBatchPriceResList.add(miniQueryPriceReq);
                    miniBatchQueryPriceResVO.setItems(miniBatchPriceResList);
                    newExpressMap.put(expressId,miniBatchQueryPriceResVO);
                }
            }
        }
        return newExpressMap;
    }


    /**
     * 判断BigDecimal是否是小数，如果是小数则向上取整
     * @param weight 要判断的BigDecimal
     * @return 如果是小数则返回向上取整后的值，否则返回原值
     */
    public static BigDecimal roundUpIfDecimal(BigDecimal weight) {
        // 取整后的值
        BigDecimal roundedWeight = weight.setScale(0, RoundingMode.CEILING);

//        // 如果原值与取整后的值不相等，则表示原值是小数，返回向上取整后的值
//        if (weight.compareTo(roundedWeight) != 0) {
//            return roundedWeight;
//        }
        // 如果原值与取整后的值相等，则表示原值不是小数，返回原值
        return roundedWeight;
    }

    /**
     * 查询订单需支付金额
     *
     * @param orderId
     * @return
     */
    @Operation(summary = "查询订单需支付金额", description = "根据订单ID查询需要支付的金额")
    @ApiResponse(responseCode = "200", description = "成功返回订单需支付金额")
    @Parameter(name = "orderId", description = "订单ID", required = true)
    @Parameter(name = "type", description = "订单类型(order:普通订单,compensatin:补差订单)")
    @GetMapping("/queryOrderNeedPay")
    public CommonResult<BigDecimal> queryOrderNeedPay(@RequestParam("orderId") String orderId, @RequestParam(value = "type", required = false) String type) {
        if (ObjectUtil.isEmpty(type)) {
            type = "order";
        }
        if ("compensatin".equals(type)) {
            // 查询补差金额
            OrderDO order = orderService.getOrder(orderId);
            BigDecimal compensationPaid0 = ObjectUtil.isNotEmpty(order.getCompensationPaid0()) ? order.getCompensationPaid0() : BigDecimal.valueOf(0);
            return CommonResult.success((order.getCompensationAmount().subtract(compensationPaid0)).abs());
        } else {
            //  TODO order分库改动
            FeeUserDO order = feeUserService.getFeeUser(orderId);
            if(ObjectUtil.isEmpty(order)){
                return CommonResult.error(310002, "当前订单支付已超时，请重写下单。");
            }

            BigDecimal lastPrice = order.getLastPrice();
            return CommonResult.success(lastPrice);
        }
    }

    /**
     * 查询订单需支付金额(新的)
     *
     * @param orderId
     * @return
     */
    @Operation(summary = "查询订单需支付金额V2", description = "根据订单ID查询需要支付的金额(增强版)")
    @ApiResponse(responseCode = "200", description = "成功返回订单需支付金额及支付方式信息")
    @Parameter(name = "orderId", description = "订单ID", required = true)
    @Parameter(name = "type", description = "订单类型(order:普通订单,compensatin:补差订单)")
    @GetMapping("/queryOrderNeedPayV2")
    public CommonResult<MiniOrderNeedPayVO> queryOrderNeedPayV2(@RequestParam("orderId") String orderId, @RequestParam(value = "type", required = false) String type) {
        if (ObjectUtil.isEmpty(type)) {
            type = "order";
        }
        MiniOrderNeedPayVO miniOrderNeedPayVO = new MiniOrderNeedPayVO();
        if ("compensatin".equals(type)) {
            // 查询补差金额
            OrderDO order = orderService.getOrder(orderId);
            BigDecimal compensationPaid0 = ObjectUtil.isNotEmpty(order.getCompensationPaid0()) ? order.getCompensationPaid0() : BigDecimal.valueOf(0);
            miniOrderNeedPayVO.setPayAmount((order.getCompensationAmount().subtract(compensationPaid0)).abs());
            return CommonResult.success(miniOrderNeedPayVO);
        } else {
            //  TODO order分库改动
            FeeUserDO order = feeUserService.getFeeUser(orderId);
            if(ObjectUtil.isEmpty(order)){
                return CommonResult.error(310002, "当前订单支付已超时，请重写下单。");
            }

            BigDecimal lastPrice = order.getLastPayAmount();

            miniOrderNeedPayVO.setPayAmount(lastPrice);
            if (ObjectUtil.isNotEmpty(order.getCouponId())) {
                // 使用了优惠券，只能使用微信支付
                miniOrderNeedPayVO.setIsWeChat(false);
            }

            return CommonResult.success(miniOrderNeedPayVO);
        }
    }


    @Operation(summary = "云通创建订单", description = "通过云通API创建快递订单")
    @ApiResponse(responseCode = "200", description = "订单创建成功")
    @PostMapping("/yTCreate")
    public CommonResult<YunTongSubmitOrderResp> yunTongOrderCreate(@RequestBody CreateOrderVO createOrderVO) {
        return CommonResult.success(yunTongApiService.createOrder(createOrderVO));
    }

    @Operation(summary = "云通取消订单", description = "通过云通API取消已创建的快递订单")
    @ApiResponse(responseCode = "200", description = "订单取消成功")
    @PostMapping("/yTCancel")
    public CommonResult<YunTongCancelOrderResp> yunTongOrderCancel(@RequestBody YunTongCancelOrderReq cancelOrderReq) {
        return CommonResult.success(yunTongApiService.cancelOrder(cancelOrderReq));
    }

    /**
     * 把支付成功的订单放redis缓存20秒，防止还未回调成功，又重新支付
     *
     * @param orderId
     * @return
     */
    @Operation(summary = "订单支付状态缓存", description = "将支付成功的订单放入Redis缓存20秒，防止重复支付")
    @ApiResponse(responseCode = "200", description = "缓存设置成功")
    @Parameter(name = "orderId", description = "订单ID", required = true)
    @PostMapping("orderWxPayCache")
    public CommonResult orderWxPayCache(@RequestParam("orderId") String orderId) {
        if (ObjectUtil.isEmpty(orderId)) {
            CommonResult.success("");
        }
        OrderDO orderDO = orderService.getOrder(orderId);
        if (ObjectUtil.isEmpty(orderDO)) {
            CommonResult.success("");
        }
        String redisLockKey = orderId + "";
        Long tenantId = TenantContextHolder.getTenantId();
        if (ObjectUtil.isNotEmpty(tenantId)) {
            redisLockKey = tenantId + "_" + orderId;
        }
        redisTemplate.opsForValue().setIfAbsent("wxpay::lock::" + redisLockKey, orderId, 20, TimeUnit.SECONDS);

        return CommonResult.success("");
    }


    /**
     * 删除订单
     *
     * @param orderId
     * @return
     */
    @Operation(summary = "删除订单", description = "将订单标记为删除状态")
    @ApiResponse(responseCode = "200", description = "订单删除成功")
    @Parameter(name = "orderId", description = "订单ID", required = true)
    @PostMapping("cancels")
//    @AutoLog(value = "用户删除订单",businessType= LogBusinessType.KUAIDI)
    public CommonResult cancels(@RequestParam("orderId") String orderId) {
        // orderService.cancelOrder(orderId, "订单删除自动取消订单");
        orderService.deleteOrder(orderId);

        return CommonResult.success("");
    }

    /**
     * 取消订单
     *
     * @param orderId
     * @param reason
     * @return
     */
    @Operation(summary = "取消订单", description = "取消用户的快递订单")
    @ApiResponse(responseCode = "200", description = "订单取消成功")
    @Parameter(name = "orderId", description = "订单ID", required = true)
    @Parameter(name = "reason", description = "取消原因")
    @Parameter(name = "reasonId", description = "取消原因ID")
//    @AutoLog(value = "用户取消订单",businessType= LogBusinessType.KUAIDI)
    @PostMapping("/cancel")
    public CommonResult cancel(@RequestParam("orderId") String orderId,
                               @RequestParam(value = "reason", required = false) String reason,
                               @RequestParam(value = "reasonId", required = false) String reasonId) {
        if (StrUtil.isEmpty(reason)) {
            reason = "用户主动取消";
        }

        Boolean cancelOrder = orderService.cancelOrder(orderId, reason, OrderConstants.KUAIDI_CANCEL_TYPE_ONE, true, reasonId);

        if (!cancelOrder) {
            return CommonResult.error(31521, "订单取消失败，请联系在线客服。");
        }

        return CommonResult.success("");
    }

    /**
     * 订单上传称重图片
     * @param req
     * @return
     */
    @Operation(summary = "上传称重图片", description = "上传快递包裹的称重图片")
    @ApiResponse(responseCode = "200", description = "图片上传成功")
    @PostMapping("/uploadWeightImg")
    public CommonResult<Long> uploadWeightImg(@RequestBody MiniOrderWeightImgReqVO req) {
        if (StringUtils.isEmpty(req.getOrderId())) {
            throw new ServiceException(501203, "上传失败，缺少必要参数");
        }

        // 查询订单
        OrderDO orderDO = orderService.getOrder(req.getOrderId());
        if (ObjectUtil.isEmpty(orderDO)) {
            return CommonResult.error(GlobalErrorCodeConstants.NOT_FOUND.getCode(), "订单不存在");
        }

        OrderParcelDO parcelDO = parcelService.getOrderParcelByOrderId(req.getOrderId());
        if (ObjectUtil.isEmpty(orderDO)) {
            return CommonResult.error(GlobalErrorCodeConstants.NOT_FOUND.getCode(), "订单包裹信息不存在");
        }

        if (!parcelDO.getOrderId().equals(req.getOrderId())) {
            return CommonResult.error(GlobalErrorCodeConstants.NOT_FOUND.getCode(), "请求数据被篡改，请勿提交称重图片");
        }

        if (StringUtils.isNotEmpty(req.getUrls())) {
            OrderParcelUpdateReqVO reqVO = new OrderParcelUpdateReqVO();
            BeanUtil.copyProperties(parcelDO, reqVO);
            reqVO.setUrls(req.getUrls());
            parcelService.updateOrderParcel(reqVO);
        }
        return CommonResult.success(parcelDO.getId());
    }

    /**
     * 查看订单详情
     *
     * @return
     */
    @Operation(summary = "查看订单详情", description = "获取订单的详细信息，包括状态、物流信息等")
    @ApiResponse(responseCode = "200", description = "成功返回订单详情")
    @Parameter(name = "orderId", description = "订单ID", required = true)
    @PostMapping("/detail")
//    @AutoLog(value = "用户查看订单详情",businessType= LogBusinessType.KUAIDI)
    public CommonResult<MiniOrderDetailsResVO> detail(@RequestParam("orderId") String orderId) {
        MiniOrderDetailsResVO miniOrderDetailsResVO = new MiniOrderDetailsResVO();
        // 查询订单
        OrderDO orderDO = orderService.getOrder(orderId);
        if (ObjectUtil.isEmpty(orderDO)) {
            return CommonResult.error(GlobalErrorCodeConstants.NOT_FOUND.getCode(), "订单不存在");
        }
        String statusLabel = DictFrameworkUtils.getDictDataLabel(DictTypeConstants.KUAIDI_STATUS, orderDO.getStatus());

        miniOrderDetailsResVO.setStatusStr(statusLabel);
        miniOrderDetailsResVO.setOrderId(orderDO.getId());
        miniOrderDetailsResVO.setStatus(orderDO.getStatus());
        miniOrderDetailsResVO.setOrderNo(orderDO.getOrderNo());
        miniOrderDetailsResVO.setCreateTime(LocalDateTimeUtil.formatNormal(orderDO.getCreateTime()));


        miniOrderDetailsResVO.setPayType(orderDO.getPayType());
        miniOrderDetailsResVO.setRefundFlag(orderDO.getRefundFlag());
        if(ObjectUtil.isNotEmpty(orderDO.getRefundFlag())){
            String dictDataLabel = DictFrameworkUtils.getDictDataLabel(DictTypeConstants.ORDER_REFUND_FLAG, orderDO.getRefundFlag());
            if(ObjectUtil.isNotEmpty(dictDataLabel)){
                miniOrderDetailsResVO.setRefundFlagStr(dictDataLabel);
            }
        }
        // 修改返回的超重补差金额
        BigDecimal compensationAmount = ObjectUtil.isNotEmpty(orderDO.getCompensationAmount()) ? orderDO.getCompensationAmount() : BigDecimal.valueOf(0);
        BigDecimal compensationPaid0 = ObjectUtil.isNotEmpty(orderDO.getCompensationPaid0()) ? orderDO.getCompensationPaid0() : BigDecimal.valueOf(0);
        if (OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO.equals(orderDO.getCompensatingState()) && OrderConstants.ORDER_COMPENSATING_RESULT_TWO.equals(orderDO.getCompensatingResult())) {
            miniOrderDetailsResVO.setCompensationAmount((compensationAmount.subtract(compensationPaid0)).abs());
        }else if (OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO.equals(orderDO.getCompensatingState()) && OrderConstants.ORDER_COMPENSATING_RESULT_THREE.equals(orderDO.getCompensatingResult())){
            miniOrderDetailsResVO.setCompensationAmount(compensationPaid0.abs());
        }
//        miniOrderDetailsResVO.setCompensationAmount(compensationAmount.subtract(compensationPaid0).subtract(compensationPaid1).subtract(compensationPaid2));

        miniOrderDetailsResVO.setChannel(orderDO.getChannel());
        miniOrderDetailsResVO.setCompensatingState(orderDO.getCompensatingState());
        miniOrderDetailsResVO.setCompensatingResult(orderDO.getCompensatingResult());


        if(ObjectUtil.isNotEmpty(orderDO.getTenantChannelId())){
            KdChannelExportReqVO channelAndExpressName = kdChannelService.getChannelAndExpressName(Long.valueOf(orderDO.getChannelExpressId()));
            if (ObjectUtil.isNotEmpty(channelAndExpressName)) {
                miniOrderDetailsResVO.setChannelExpressName(channelAndExpressName.getExpressName());
                miniOrderDetailsResVO.setLogo(channelAndExpressName.getExpressLogo());
            }
        } else {
            ChannelExpressDetailVO channelExpressDetail = channelExpressService.getChannelExpressDetail(orderDO.getChannelExpressId());
            if (ObjectUtil.isNotEmpty(channelExpressDetail)) {
                miniOrderDetailsResVO.setChannelExpressName(channelExpressDetail.getName());
                miniOrderDetailsResVO.setLogo(channelExpressDetail.getLogo());
            }
        }

        // 查询寄件人信息
        OrderContactsDO senderContacts = contactsService.getOrderContactsByOrderId(orderId, "sender");
        if (ObjectUtil.isNotEmpty(senderContacts)) {
            miniOrderDetailsResVO.setSenderProvince(senderContacts.getProvince());
            miniOrderDetailsResVO.setSenderName(senderContacts.getName());
            miniOrderDetailsResVO.setSenderMobile(ObjectUtil.isNotEmpty(senderContacts.getMobile()) ? senderContacts.getMobile() : senderContacts.getPhone());
            miniOrderDetailsResVO.setSenderAddressDetail(senderContacts.getProvince() + senderContacts.getCity() + senderContacts.getDistrict() + senderContacts.getAddress());
        }


        // 收件人信息
        OrderContactsDO recevierContacts = contactsService.getOrderContactsByOrderId(orderId, "receive");
        if (ObjectUtil.isNotEmpty(recevierContacts)) {
            miniOrderDetailsResVO.setReceiveProvince(recevierContacts.getProvince());
            miniOrderDetailsResVO.setReceiveName(recevierContacts.getName());
            miniOrderDetailsResVO.setReceiveMobile(ObjectUtil.isNotEmpty(recevierContacts.getMobile()) ? recevierContacts.getMobile() : recevierContacts.getPhone());
            miniOrderDetailsResVO.setReceiveAddressDetail(recevierContacts.getProvince() + recevierContacts.getCity() + recevierContacts.getDistrict() + recevierContacts.getAddress());
        }

        FeeUserDO feeUserDO = feeUserService.getFeeUser(orderId);
        // 查询用户费用明细
        if(ObjectUtil.isNotEmpty(feeUserDO)){
            miniOrderDetailsResVO.setFreight(feeUserDO.getLastPrice());
            miniOrderDetailsResVO.setPayAmount(feeUserDO.getPayAmount());
            // 优惠金额
            miniOrderDetailsResVO.setReliefAmount(feeUserDO.getReliefAmount());
        }



        // 物品信息
        OrderParcelDO orderParcelDO = orderParcelService.getOrderParcelByOrderId(orderId);
        if (ObjectUtil.isNotEmpty(orderParcelDO)) {
            miniOrderDetailsResVO.setGoods(orderParcelDO.getGoods());
            miniOrderDetailsResVO.setWeight(orderParcelDO.getWeight());
            miniOrderDetailsResVO.setRealWeight(orderParcelDO.getRealWeight());
            miniOrderDetailsResVO.setCalcFeeWeight(orderParcelDO.getCalcFeeWeight());
            miniOrderDetailsResVO.setInsuredAmount(orderParcelDO.getInsuredAmount());

            // 计算体积
            DecimalFormat df = new DecimalFormat("#0.00");
            if (ObjectUtil.isNotEmpty(orderParcelDO.getRealVolume()) && orderParcelDO.getRealVolume().compareTo(BigDecimal.ZERO) == 1){
                miniOrderDetailsResVO.setRealVolume(new BigDecimal(df.format(orderParcelDO.getRealVolume())));
            }
            if (ObjectUtil.isNotEmpty(orderParcelDO.getVloumLong()) && ObjectUtil.isNotEmpty(orderParcelDO.getVloumWidth()) && ObjectUtil.isNotEmpty(orderParcelDO.getVloumHeight())) {
                miniOrderDetailsResVO.setVolume(new BigDecimal(df.format(orderParcelDO.getVloumLong().multiply(orderParcelDO.getVloumWidth()).multiply(orderParcelDO.getVloumHeight()))));
            }
            miniOrderDetailsResVO.setRemark(orderParcelDO.getRemark());
            miniOrderDetailsResVO.setUrls(orderParcelDO.getUrls());


            if (ObjectUtil.isNotEmpty(orderDO.getStatus()) && !orderDO.getStatus().equals(OrderConstants.ORDER_STATUS_TREAT_PAYMENT)){
                miniOrderDetailsResVO.setPaymentTime(ObjectUtil.isEmpty(orderParcelDO.getPaymentTime()) ? LocalDateTimeUtil.formatNormal(orderDO.getCreateTime()) : LocalDateTimeUtil.formatNormal(orderParcelDO.getPaymentTime()));
            }
            // 截取快递信息
            if(ObjectUtil.isNotEmpty(orderParcelDO.getPickUpInfo())){
                String[] items = orderParcelDO.getPickUpInfo().split(",");
                if(items.length > 3){
                    miniOrderDetailsResVO.setPickUpCode(items[1]);
                    miniOrderDetailsResVO.setCourierName(items[2]);
                    miniOrderDetailsResVO.setCourierPhone(items[3]);
                }
            }

            miniOrderDetailsResVO.setMessage(orderParcelDO.getMessage());
            miniOrderDetailsResVO.setThirdExpressName(orderParcelDO.getThirdExpressName());
            miniOrderDetailsResVO.setThirdExpressType(orderParcelDO.getThirdExpressType());
            miniOrderDetailsResVO.setCancelTime(LocalDateTimeUtil.formatNormal(orderParcelDO.getCancelTime()));
            miniOrderDetailsResVO.setRefundTime(LocalDateTimeUtil.formatNormal(orderParcelDO.getRefundTime()));

            if(ObjectUtil.isNotEmpty(orderParcelDO.getAppointStartTime())){
                miniOrderDetailsResVO.setPickUpStartTime(LocalDateTimeUtil.formatNormal(orderParcelDO.getAppointStartTime()));
            }
            if(ObjectUtil.isNotEmpty(orderParcelDO.getAppointEndTime())){
                miniOrderDetailsResVO.setPickUpEndTime(LocalDateTimeUtil.formatNormal(orderParcelDO.getAppointEndTime()));
            }
        }


        // 费用明细
        List<OrderFeeDO> orderFeeDOList = orderFeeService.getFeeListByOrderId(orderId);
        if (ObjectUtil.isNotEmpty(orderFeeDOList)) {
            BigDecimal totalFreight = new BigDecimal(0);
            for (OrderFeeDO bean : orderFeeDOList) {
                totalFreight = totalFreight.add(ObjectUtil.isNotEmpty(bean.getAmount()) ? bean.getAmount() : new BigDecimal(0));
            }
            List<MiniOrderFeeResVo> orderFeeList = cn.hutool.core.bean.BeanUtil.copyToList(orderFeeDOList, MiniOrderFeeResVo.class);
            miniOrderDetailsResVO.setTotalFreight(totalFreight);
            miniOrderDetailsResVO.setOrderFeeList(orderFeeList);
        }

        // 如果是超重补差 状态变更 为 "运输中"
        if (OrderConstants.ORDER_STATUS_REFUND.equals(miniOrderDetailsResVO.getStatus())) {
            miniOrderDetailsResVO.setStatus(OrderConstants.ORDER_STATUS_IN_TRANSIT);
            miniOrderDetailsResVO.setStatusStr("运输中");
        }

        // 轨迹信息
        try {
            List<OrderTrackReturnVo> orderTrackReturnVos = orderService.trackOrder(orderId);
            miniOrderDetailsResVO.setTrackList(orderTrackReturnVos);
        } catch (Exception e) {
            // 订单：{} 查询轨迹信息失败
        }



        return CommonResult.success(miniOrderDetailsResVO);
    }


    /**
     * 发起订单补偿
     *
     * @return
     */
    @Operation(summary = "发起订单支付补偿", description = "为支付异常的订单发起补偿")
    @ApiResponse(responseCode = "200", description = "补偿发起成功")
    @PostMapping("/initiatePaymentCompensation")
    public CommonResult<Boolean> initiatePaymentCompensation(@RequestBody MQOrderPayRemedyVo params) {
        // 优惠券到账通知
        orderService.initiatePaymentCompensation(params);
        return CommonResult.success(true);
    }


    /**
     * 笔运单需要补费的数量
     *
     * @return
     */
    @Operation(summary = "获取需补费订单数量", description = "获取当前用户需要补费的订单数量")
    @ApiResponse(responseCode = "200", description = "成功返回需补费订单数量")
    @GetMapping("getCompensatingCount")
    public CommonResult<Long> getCompensatingCount() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 查询需要补价的订单数量
        Long count = orderService.getOrderCompensatingCount(userId, OrderConstants.ORDER_COMPENSATING_RESULT_TWO);
        return CommonResult.success(count);
    }


    /**
     * 用户优惠券列表
     *
     * @return
     */
    @Operation(summary = "获取用户优惠券列表", description = "获取当前用户的优惠券列表")
    @ApiResponse(responseCode = "200", description = "成功返回优惠券列表")
    @Parameter(name = "type", description = "优惠券类型(1:可用,2:不可用)", required = true)
    @GetMapping("getUserCoupon")
    public CommonResult<List<MiniCouponUserResVo>> getUserCoupon(@RequestParam("type") String type) {


        Long userId = SecurityFrameworkUtils.getLoginUserId();

        CouponUserPageReqVO reqVO = new CouponUserPageReqVO();
        LocalDateTime localDateTime = LocalDateTime.now();
        if ("1".equals(type)) {
            reqVO.setStatus(CouponConstants.COUPON_USER_STATUS_TWO);
            reqVO.setCurrentTime(localDateTime);
        } else if ("2".equals(type)) {
            List<String> statusList = new LinkedList<>();
            statusList.add(CouponConstants.COUPON_USER_STATUS_ONE);
            statusList.add(CouponConstants.COUPON_USER_STATUS_THREE);
            statusList.add(CouponConstants.COUPON_USER_STATUS_FOUR);
            reqVO.setStatusList(statusList);
        }

        reqVO.setMbrId(userId);

        List<CouponUserRespVO> userCouponList = couponUserService.getMiniCouponUserList(reqVO);
        // 转化为小程序对象返回
        List<MiniCouponUserResVo> miniCouponUserList = new LinkedList<>();
        for (CouponUserRespVO bean : userCouponList) {
            MiniCouponUserResVo miniAddressVO = new MiniCouponUserResVo();
            BeanUtil.copyProperties(bean, miniAddressVO);
            if (ObjectUtil.isNotEmpty(bean.getEffectiveTime())) {
                miniAddressVO.setEffectiveTime(LocalDateTimeUtil.format(bean.getEffectiveTime(), "yyyy.MM.dd"));
            }
            if (ObjectUtil.isNotEmpty(bean.getExpiryTime())) {
                miniAddressVO.setExpiryTime(LocalDateTimeUtil.format(bean.getExpiryTime(), "yyyy.MM.dd"));
            }
            if (ObjectUtil.isNotEmpty(miniAddressVO.getStatus())) {
                String statusLabel = DictFrameworkUtils.getDictDataLabel(DictTypeConstants.COUPON_USER_STATUS, miniAddressVO.getStatus());
                miniAddressVO.setStatusLabel(statusLabel);
            }
            miniCouponUserList.add(miniAddressVO);
        }

        return CommonResult.success(miniCouponUserList);
    }


    /**
     * 获取优惠券配置
     *
     * @return
     */
    @Operation(summary = "获取优惠券配置", description = "获取系统优惠券相关配置")
    @ApiResponse(responseCode = "200", description = "成功返回优惠券配置")
    @GetMapping("getCouponConfig")
    public CommonResult<MiniCouponConfigVo> getCouponConfig() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Long tenantId = TenantContextHolder.getTenantId();
        // 查询需要补价的订单数量
        Boolean isCouponFirst = orderService.isCouponFirst(userId);
        String couponStatus = paramApi.getCouponStatus(tenantId);
        String secondCouponAmount = paramApi.getSecondCouponAmount(tenantId);
        MiniCouponConfigVo miniCouponConfigVo = new MiniCouponConfigVo();
        miniCouponConfigVo.setCouponSwitch(couponStatus);
        miniCouponConfigVo.setIsCouponFirst(isCouponFirst);
        if (ObjectUtil.isNotEmpty(secondCouponAmount)) {
            miniCouponConfigVo.setSecondCouponAmount(new BigDecimal(secondCouponAmount));
        } else {
            miniCouponConfigVo.setSecondCouponAmount(new BigDecimal(0));
        }
        return CommonResult.success(miniCouponConfigVo);
    }


    /**
     * 获取优惠券配置
     *
     * @return
     */
    @Operation(summary = "获取优惠券开关状态", description = "获取系统优惠券功能的开启状态")
    @ApiResponse(responseCode = "200", description = "成功返回优惠券开关状态")
    @GetMapping("getCouponSwitch")
    public CommonResult<String> getCouponSwitch() {
        Long tenantId = TenantContextHolder.getTenantId();
        String couponStatus = paramApi.getCouponStatus(tenantId);
        if(ObjectUtil.isEmpty(couponStatus)){
            couponStatus = "1";
        }

        return CommonResult.success(couponStatus);
    }


    /**
     * 兑换优惠卷
     *
     * @return
     */
    @Operation(summary = "兑换优惠券", description = "通过兑换码兑换优惠券")
    @ApiResponse(responseCode = "200", description = "优惠券兑换成功")
    @Parameter(name = "couponCode", description = "优惠券兑换码", required = true)
    @PostMapping("exchangeCoupon")
    public CommonResult<CouponCodeOldDO> exchangeCoupon(@RequestParam("couponCode") String couponCode) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();

        CouponUserPageReqVO reqVO = new CouponUserPageReqVO();
        LocalDateTime localDateTime = LocalDateTime.now();
        reqVO.setCurrentTime(localDateTime);

        CouponCodeOldDO userCouponList = couponCodeOldService.receiveCoupon(couponCode, userId);

        return CommonResult.success(userCouponList);
    }




    /**
     * 优惠券到账通知
     *
     * @return
     */
    @Operation(summary = "获取优惠券通知模板ID", description = "获取优惠券到账通知的微信模板ID")
    @ApiResponse(responseCode = "200", description = "成功返回模板ID列表")
    @PostMapping("/getMiniCouponTmplList")
    public CommonResult<List<String>> getMiniCouponTmplList() {
        Long tenantId = TenantContextHolder.getTenantId();

        List<String> codeList = new ArrayList<>();
        // 优惠卷发放通知
        codeList.add("coupon_issuance");
        // 过期通知
        codeList.add("coupon_expiration");


        // 查询订单相关模版
        List<NoticeGroupMiniRespVO> noticeGroupAndMiniCodeV2 = messageSendApi.getNoticeGroupAndMiniCodeV2(tenantId, codeList).getData();

        List<String> tmplIds = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(noticeGroupAndMiniCodeV2)) {
            // 使用Map优化性能，避免嵌套循环
            Map<String, String> codeToTemplateMap = noticeGroupAndMiniCodeV2.stream()
                    .filter(bean -> ObjectUtil.isNotEmpty(bean.getNoticeGroupCode()) && ObjectUtil.isNotEmpty(bean.getNoticeMiniCode()))
                    .collect(Collectors.toMap(
                            NoticeGroupMiniRespVO::getNoticeGroupCode,
                            NoticeGroupMiniRespVO::getNoticeMiniCode,
                            (existing, replacement) -> existing // 如果有重复key，保留第一个
                    ));

            // 按照codeList的顺序来添加tmplIds，确保返回顺序与定义顺序一致
            for (String code : codeList) {
                String templateId = codeToTemplateMap.get(code);
                if (ObjectUtil.isNotEmpty(templateId)) {
                    tmplIds.add(templateId);
                }
            }
        }
        return CommonResult.success(tmplIds);
//        // 优惠券到账通知
//        String miniTmplCouponAccount = paramApi.getMiniTmplCouponAccount(tenantId);
//
//        List<String> tmpls = new ArrayList<>();
//        if (StrUtil.isNotEmpty(miniTmplCouponAccount)) {
//            tmpls.add(miniTmplCouponAccount);
//        }
//        return CommonResult.success(tmpls);
    }


    /**
     * 获取订单小程序模板列表
     */
    @Operation(summary = "获取订单相关通知模板", description = "获取订单相关的微信通知模板ID和公众号关注状态")
    @ApiResponse(responseCode = "200", description = "成功返回模板ID和关注状态")
    @GetMapping("getOrderCorrelation")
    public CommonResult<Map<String, Object>> getOrderCorrelation() {
        Long tenantId = TenantContextHolder.getTenantId();
        // 补差通知
        String miniTmplDifferencePrice = paramApi.getMiniTmplDifferencePrice(tenantId);
//        // 接单成功提醒
//        String miniTmplOrderSuccess = paramApi.getMiniTmplOrderSuccess(tenantId);
//        // 签收成功提醒
//        String miniTmplReceivedSuccess = paramApi.getMiniTmplReceivedSuccess(tenantId);
        // 获取到让用户订阅消息模板
        List<String> tmplIds = new ArrayList<>();
        tmplIds.add(miniTmplDifferencePrice);
//        if(ObjectUtil.isNotEmpty(miniTmplOrderSuccess)){
//            tmplIds.add(miniTmplOrderSuccess);
//        }
//        if(ObjectUtil.isNotEmpty(miniTmplReceivedSuccess)){
//            tmplIds.add(miniTmplReceivedSuccess);
//        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("tmplIds", tmplIds);
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Boolean mpOpenId = memberUserApi.byUnionQueryIsAttention(userId).getData();
        returnMap.put("mpOpenId", mpOpenId);
        return CommonResult.success(returnMap);
    }


    /**
     * 获取订单小程序模板列表
     */
    @Operation(summary = "获取订单相关通知模板", description = "获取订单相关的微信通知模板ID和公众号关注状态")
    @ApiResponse(responseCode = "200", description = "成功返回模板ID和关注状态")
    @GetMapping("getOrderCorrelationV2")
    public CommonResult<Map<String, Object>> getOrderCorrelationV2() {
        Long tenantId = TenantContextHolder.getTenantId();


        List<String> codeList = new ArrayList<>();
        // 补差通知
        codeList.add("overweight_surcharge");
        // 接单成功通知
        codeList.add("courier_accepted");
        // 取消订单通知
        codeList.add("order_canceled");

        // 查询订单相关模版
        List<NoticeGroupMiniRespVO> noticeGroupAndMiniCodeV2 = messageSendApi.getNoticeGroupAndMiniCodeV2(tenantId, codeList).getData();

        List<String> tmplIds = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(noticeGroupAndMiniCodeV2)) {
            // 使用Map优化性能，避免嵌套循环
            Map<String, String> codeToTemplateMap = noticeGroupAndMiniCodeV2.stream()
                    .filter(bean -> ObjectUtil.isNotEmpty(bean.getNoticeGroupCode()) && ObjectUtil.isNotEmpty(bean.getNoticeMiniCode()))
                    .collect(Collectors.toMap(
                            NoticeGroupMiniRespVO::getNoticeGroupCode,
                            NoticeGroupMiniRespVO::getNoticeMiniCode,
                            (existing, replacement) -> existing // 如果有重复key，保留第一个
                    ));

            // 按照codeList的顺序来添加tmplIds，确保返回顺序与定义顺序一致
            for (String code : codeList) {
                String templateId = codeToTemplateMap.get(code);
                if (ObjectUtil.isNotEmpty(templateId)) {
                    tmplIds.add(templateId);
                }
            }
        } else {
            // 补差通知
            String miniTmplDifferencePrice = paramApi.getMiniTmplDifferencePrice(tenantId);
//        // 接单成功提醒
//        String miniTmplOrderSuccess = paramApi.getMiniTmplOrderSuccess(tenantId);
//        // 签收成功提醒
//        String miniTmplReceivedSuccess = paramApi.getMiniTmplReceivedSuccess(tenantId);
            // 获取到让用户订阅消息模板

            tmplIds.add(miniTmplDifferencePrice);
        }

//        if(ObjectUtil.isNotEmpty(miniTmplOrderSuccess)){
//            tmplIds.add(miniTmplOrderSuccess);
//        }
//        if(ObjectUtil.isNotEmpty(miniTmplReceivedSuccess)){
//            tmplIds.add(miniTmplReceivedSuccess);
//        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("tmplIds", tmplIds);
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Boolean mpOpenId = memberUserApi.byUnionQueryIsAttention(userId).getData();
        returnMap.put("mpOpenId", mpOpenId);
        return CommonResult.success(returnMap);
    }

    /**
     * 获取小程序工单通知模板
     */
    @Operation(summary = "获取工单通知模板", description = "获取工单处理通知的微信模板ID")
    @ApiResponse(responseCode = "200", description = "成功返回工单通知模板ID")
    @GetMapping("getWorkNoticeTmpl")
    public CommonResult<Map<String, Object>> getWorkNoticeTmpl() {
        Long tenantId = TenantContextHolder.getTenantId();
        // 补差通知
        String miniTmplDifferencePrice = paramApi.getMiniTmplWorkOrderHandle(tenantId);
        // 获取到让用户订阅消息模板
        List<String> tmplIds = new ArrayList<>();
        tmplIds.add(miniTmplDifferencePrice);
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("tmplIds", tmplIds);
        return CommonResult.success(returnMap);
    }


    /**
     * 获取小程序工单通知模板
     */
    @Operation(summary = "获取工单通知模板", description = "获取工单处理通知的微信模板ID")
    @ApiResponse(responseCode = "200", description = "成功返回工单通知模板ID")
    @GetMapping("getWorkNoticeTmplV2")
    public CommonResult<Map<String, Object>> getWorkNoticeTmplV2() {
        Long tenantId = TenantContextHolder.getTenantId();

        List<String> codeList = new ArrayList<>();
        codeList.add("work_order_reply");
        List<NoticeGroupMiniRespVO> noticeGroupAndMiniCodeV2 = messageSendApi.getNoticeGroupAndMiniCodeV2(tenantId, codeList).getData();

        // 获取到让用户订阅消息模板
        List<String> tmplIds = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(noticeGroupAndMiniCodeV2) && noticeGroupAndMiniCodeV2.size() > 0) {
            if (ObjectUtil.isNotEmpty(noticeGroupAndMiniCodeV2.get(0).getNoticeMiniCode())) {
                tmplIds.add(noticeGroupAndMiniCodeV2.get(0).getNoticeMiniCode());
            }
        }

        // 补差通知
//        String miniTmplDifferencePrice = paramApi.getMiniTmplWorkOrderHandle(tenantId);
//        tmplIds.add(miniTmplDifferencePrice);

        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("tmplIds", tmplIds);
        return CommonResult.success(returnMap);
    }

    /**
     * 获取小程序投诉建议通知模板
     */
    @Operation(summary = "获取投诉建议通知模板", description = "获取投诉建议处理通知的微信模板ID")
    @ApiResponse(responseCode = "200", description = "成功返回投诉建议通知模板ID")
    @GetMapping("getComplainTmpl")
    public CommonResult<Map<String, Object>> getComplainTmpl() {
        Long tenantId = TenantContextHolder.getTenantId();
        // 补差通知
        String miniTmplDifferencePrice = paramApi.getMiniTmplComplaintHandling(tenantId);
        // 获取到让用户订阅消息模板
        List<String> tmplIds = new ArrayList<>();
        tmplIds.add(miniTmplDifferencePrice);
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("tmplIds", tmplIds);
        return CommonResult.success(returnMap);
    }


    /**
     * 获取小程序投诉建议通知模板
     */
    @Operation(summary = "获取投诉建议通知模板", description = "获取投诉建议处理通知的微信模板ID")
    @ApiResponse(responseCode = "200", description = "成功返回投诉建议通知模板ID")
    @GetMapping("getComplainTmplV2")
    public CommonResult<Map<String, Object>> getComplainTmplV2() {
        Long tenantId = TenantContextHolder.getTenantId();

        List<String> codeList = new ArrayList<>();
        codeList.add("complaint_reply");
        List<NoticeGroupMiniRespVO> noticeGroupAndMiniCodeV2 = messageSendApi.getNoticeGroupAndMiniCodeV2(tenantId, codeList).getData();

        // 获取到让用户订阅消息模板
        List<String> tmplIds = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(noticeGroupAndMiniCodeV2) && noticeGroupAndMiniCodeV2.size() > 0) {
            if (ObjectUtil.isNotEmpty(noticeGroupAndMiniCodeV2.get(0).getNoticeMiniCode())) {
                tmplIds.add(noticeGroupAndMiniCodeV2.get(0).getNoticeMiniCode());
            }
        }
//        else {
//            // 补差通知
//            String miniTmplDifferencePrice = paramApi.getMiniTmplComplaintHandling(tenantId);
//            tmplIds.add(miniTmplDifferencePrice);
//        }

        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("tmplIds", tmplIds);
        return CommonResult.success(returnMap);
    }

    /**
     * 查询当前用户是否已关注公众号
     *
     * @return
     */
    @Operation(summary = "查询用户公众号关注状态", description = "查询当前用户是否已关注微信公众号")
    @ApiResponse(responseCode = "200", description = "成功返回关注状态")
    @GetMapping("getIsInterestMp")
    public CommonResult<String> getIsInterestMp() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String mpOpenId = memberUserApi.getMpOpenId(userId).getData();
        if (ObjectUtil.isNotEmpty(mpOpenId)) {
            return CommonResult.success(mpOpenId);
        }
        return CommonResult.success("");
    }

    /**
     * 获取订单取消原因列表
     */
    @Operation(summary = "获取订单取消原因列表", description = "获取可用于取消订单的原因列表")
    @ApiResponse(responseCode = "200", description = "成功返回取消原因列表",
            content = @Content(schema = @Schema(implementation = ReasonParamVO.class)))
    @Parameter(name = "tenantId", description = "租户ID", required = true)
    @GetMapping("/getCancelReasonList")
    public CommonResult<List<ReasonParamVO>> getCancelReasonList(@RequestParam("tenantId") String tenantId) {
        return success(orderService.getCancelReasonList(tenantId, "mini"));
    }

    /**
     * 根据订单编号和取消原因修改订单取消标识
     */
    @Operation(summary = "更新订单取消标识", description = "根据订单编号和取消原因修改订单取消标识")
    @ApiResponse(responseCode = "200", description = "更新成功")
    @Parameter(name = "orderId", description = "订单ID", required = true)
    @Parameter(name = "reasonId", description = "取消原因ID")
    @Parameter(name = "reason", description = "取消原因")
    @PostMapping("/updateOrderCancelFlag")
    public CommonResult<Boolean> updateOrderCancelFlag(@RequestParam("orderId") String orderId,
                                                       @RequestParam(value = "reasonId", required = false) String reasonId,
                                                       @RequestParam(value = "reason", required = false) String reason) {
        if (StringUtils.isBlank(orderId)) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "订单编号不能为空");
        }
        if (StringUtils.isBlank(reasonId)) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "取消原因不能为空");
        }

        OrderDO orderDO = orderService.getOrder(orderId);

        OrderParcelDO orderParcelDO = parcelService.getOrderParcelByOrderId(orderId);
        orderParcelDO.setCancelFlag(false);
        orderParcelDO.setReasonId(reasonId);

        OrderParcelUpdateReqVO updateObj = BeanUtil.copyProperties(orderParcelDO, OrderParcelUpdateReqVO.class);
        parcelService.updateOrderParcel(updateObj);

        CancelButtonStatisticsDO statisticsDO = new CancelButtonStatisticsDO();
        statisticsDO.setOrderId(orderId);
        statisticsDO.setReasonCode(reasonId);
        statisticsDO.setOrderNo(orderDO.getOrderNo());
        statisticsDO.setUserId(orderDO.getUserId());
        statisticsDO.setButtonType("abandon");
        statisticsDO.setReason(reason);
        cancelButtonStatisticsService.saveOrUpdateCancelButtonStatistics(statisticsDO);
        return CommonResult.success(true);
    }
}
