package com.hnyiti.kuaidi.module.pgsql.service.notification.checker;

import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationRecord;
import com.hnyiti.kuaidi.module.pgsql.dal.mapper.notification.NotificationRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 前序通知检查器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PreviousNotificationChecker {

    @Autowired
    private NotificationRecordMapper notificationRecordMapper;

    /**
     * 检查前序通知是否都已完成
     * 电话通知需要小程序、公众号、短信都完成后才能执行
     *
     * @param orderId 订单ID
     * @param tenantId 租户ID
     * @return 前序通知是否都已完成
     */
    public boolean isPreviousNotificationsCompleted(String orderId, Long tenantId) {
        List<String> requiredTypes = Arrays.asList(
                NotificationRecord.NotificationType.MINI_PROGRAM.getCode(),
                NotificationRecord.NotificationType.WECHAT_MP.getCode(),
                NotificationRecord.NotificationType.SMS.getCode()
        );

        log.debug("[isPreviousNotificationsCompleted] 检查前序通知完成情况，订单ID: {}, 租户ID: {}", orderId, tenantId);

        for (String type : requiredTypes) {
            NotificationRecord record = notificationRecordMapper.selectLatestByOrderIdAndType(orderId, tenantId, type);
            
            if (record == null) {
                log.debug("[isPreviousNotificationsCompleted] 通知类型 {} 无记录，订单ID: {}", type, orderId);
                return false;
            }
            
            if (!NotificationRecord.SendStatus.SUCCESS.getCode().equals(record.getSendStatus())) {
                log.debug("[isPreviousNotificationsCompleted] 通知类型 {} 未成功，状态: {}, 订单ID: {}", 
                        type, record.getSendStatus(), orderId);
                return false;
            }
        }

        log.info("[isPreviousNotificationsCompleted] 前序通知都已完成，订单ID: {}", orderId);
        return true;
    }

    /**
     * 获取已完成的通知类型列表
     *
     * @param orderId 订单ID
     * @param tenantId 租户ID
     * @return 已完成的通知类型列表
     */
    public List<String> getCompletedNotificationTypes(String orderId, Long tenantId) {
        List<String> allTypes = Arrays.asList(
                NotificationRecord.NotificationType.MINI_PROGRAM.getCode(),
                NotificationRecord.NotificationType.WECHAT_MP.getCode(),
                NotificationRecord.NotificationType.SMS.getCode(),
                NotificationRecord.NotificationType.PHONE_CALL.getCode()
        );

        return allTypes.stream()
                .filter(type -> {
                    NotificationRecord record = notificationRecordMapper.selectLatestByOrderIdAndType(orderId, tenantId, type);
                    return record != null && NotificationRecord.SendStatus.SUCCESS.getCode().equals(record.getSendStatus());
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 检查特定通知类型是否已完成
     *
     * @param orderId 订单ID
     * @param tenantId 租户ID
     * @param notificationType 通知类型
     * @return 是否已完成
     */
    public boolean isNotificationCompleted(String orderId, Long tenantId, String notificationType) {
        NotificationRecord record = notificationRecordMapper.selectLatestByOrderIdAndType(orderId, tenantId, notificationType);
        
        boolean completed = record != null && NotificationRecord.SendStatus.SUCCESS.getCode().equals(record.getSendStatus());
        
        log.debug("[isNotificationCompleted] 检查通知完成情况，订单ID: {}, 类型: {}, 是否完成: {}", 
                orderId, notificationType, completed);
        
        return completed;
    }
}
