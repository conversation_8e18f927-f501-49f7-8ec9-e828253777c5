<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hnyiti-module-drp-kuaidi</artifactId>
        <groupId>com.hnyiti.cloud</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hnyiti-module-drp-kuaidi-biz</artifactId>


    <dependencies>  <!-- 5. 新增依赖，这里引入的都是比较常用的业务组件、技术组件 -->
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-env</artifactId>
        </dependency>

        <!--加载数据源模块-->
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-datasource</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-module-message-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-module-pay-biz</artifactId>
            <version>${revision}</version>
        </dependency>


        <!-- 业务组件 -->
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-banner</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-biz-dict</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-biz-error-code</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-monitor</artifactId>
        </dependency>

        <!-- Spring Boot Admin Client -->
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>2.7.10</version>
        </dependency>
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-biz-kuaidi</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-biz-weixin</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-asynctool</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>me.zhyd.oauth</groupId>
            <artifactId>JustAuth</artifactId>
            <version>1.16.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-biz-social</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-module-drp-kuaidi-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-module-drp-kuaidi-callback-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.dingtalk.open</groupId>
            <artifactId>app-stream-client</artifactId>
            <version>1.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>2.0.14</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-leaf</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
            <version>2021.0.1.0</version>
        </dependency>

        <dependency> <!-- 主要想使用 seata 1.1.0 版本 -->
            <groupId>io.seata</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
            <version>1.7.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-integration</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-stream</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-mqtt</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.2.2</version>
        </dependency>
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-biz-log</artifactId>
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-spring-boot-starter-file</artifactId>
        </dependency>

        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20231013</version>
        </dependency>

        <dependency>
            <groupId>com.esotericsoftware</groupId>
            <artifactId>kryo</artifactId>
            <version>5.4.0</version> <!-- 确保使用最新稳定版本 -->
        </dependency>

        <dependency>
            <groupId>de.javakaffee</groupId>
            <artifactId>kryo-serializers</artifactId>
            <version>0.45</version> <!-- 确保使用最新稳定版本 -->
        </dependency>

        <!-- RocketMQ Admin工具 -->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-tools</artifactId>
            <version>4.9.4</version>
        </dependency>
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-module-pgsql-api</artifactId>
            <version>2.0.0</version>
            <scope>compile</scope>
        </dependency>

        <!-- 新增：pgsql-biz 模块依赖，用于多阶段通知服务 -->
        <dependency>
            <groupId>com.hnyiti.cloud</groupId>
            <artifactId>hnyiti-module-pgsql-biz</artifactId>
            <version>2.0.0</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>
    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
