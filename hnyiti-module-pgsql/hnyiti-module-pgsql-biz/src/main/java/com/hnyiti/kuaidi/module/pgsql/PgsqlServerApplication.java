package com.hnyiti.kuaidi.module.pgsql;

import com.hnyiti.kuaidi.module.pgsql.consumer.KuaiDiInput;
import com.hnyiti.kuaidi.module.pgsql.consumer.NoticeInput;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.stream.annotation.EnableBinding;

@EnableBinding({KuaiDiInput.class, NoticeInput.class})
@SpringBootApplication(scanBasePackages = {"com.hnyiti.kuaidi.**"})
public class PgsqlServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(PgsqlServerApplication.class, args);
    }
}
