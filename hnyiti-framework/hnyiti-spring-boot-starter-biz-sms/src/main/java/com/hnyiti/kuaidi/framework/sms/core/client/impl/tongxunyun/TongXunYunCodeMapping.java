package com.hnyiti.kuaidi.framework.sms.core.client.impl.tongxunyun;

import com.hnyiti.kuaidi.framework.common.exception.ErrorCode;
import com.hnyiti.kuaidi.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.hnyiti.kuaidi.framework.sms.core.client.SmsCodeMapping;
import com.hnyiti.kuaidi.framework.sms.core.enums.SmsFrameworkErrorCodeConstants;

/**
 * 通讯云 SmsCodeMapping 实现类
 *
 * <AUTHOR>
 */
public class TongXunYunCodeMapping implements SmsCodeMapping {

    @Override
    public ErrorCode apply(String apiCode) {
        switch (apiCode) {
            case "0":
                return GlobalErrorCodeConstants.SUCCESS;
            case "1":
                return SmsFrameworkErrorCodeConstants.SMS_ACCOUNT_INVALID; // 账号或密码错误
            case "3":
                return SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID; // msgid太长，不得超过64位
            case "4":
                return SmsFrameworkErrorCodeConstants.SMS_MOBILE_INVALID; // 错误号码/限制运营商号码
            case "5":
                return SmsFrameworkErrorCodeConstants.SMS_SEND_BUSINESS_LIMIT_CONTROL; // 手机号码个数超过最大限制
            case "6":
                return SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID; // 短信内容超过最大限制
            case "7":
                return SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID; // 扩展子号码无效
            case "8":
                return SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID; // 定时时间格式错误
            case "14":
                return SmsFrameworkErrorCodeConstants.SMS_MOBILE_INVALID; // 手机号码为空
            case "19":
                return SmsFrameworkErrorCodeConstants.SMS_ACCOUNT_INVALID; // 用户被禁发或禁用
            case "20":
                return SmsFrameworkErrorCodeConstants.SMS_ACCOUNT_INVALID; // ip鉴权失败
            case "21":
                return SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID; // 短信内容为空
            case "24":
                return SmsFrameworkErrorCodeConstants.SMS_SEND_BUSINESS_LIMIT_CONTROL; // 无可用号码
            case "25":
                return SmsFrameworkErrorCodeConstants.SMS_SEND_BUSINESS_LIMIT_CONTROL; // 批量提交短信数超过最大限制
            case "98":
                return SmsFrameworkErrorCodeConstants.SMS_UNKNOWN; // 系统正忙
            case "99":
                return SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID; // 消息格式错误
            default:
                return SmsFrameworkErrorCodeConstants.SMS_UNKNOWN;
        }
    }

}
