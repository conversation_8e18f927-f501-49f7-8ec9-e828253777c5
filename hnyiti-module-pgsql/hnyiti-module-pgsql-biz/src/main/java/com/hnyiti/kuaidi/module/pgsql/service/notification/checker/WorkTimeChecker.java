package com.hnyiti.kuaidi.module.pgsql.service.notification.checker;

import com.hnyiti.kuaidi.module.pgsql.config.NotificationConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 工作时间检查器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WorkTimeChecker {

    @Autowired
    private NotificationConfig notificationConfig;

    /**
     * 检查是否在工作时间内（9:00-24:00）
     *
     * @return 是否在工作时间内
     */
    public boolean isWorkTime() {
        LocalTime now = LocalTime.now();
        LocalTime start = notificationConfig.getWorkTime().getStart();
        LocalTime end = notificationConfig.getWorkTime().getEnd();
        
        boolean inWorkTime = !now.isBefore(start) && !now.isAfter(end);
        
        log.debug("[isWorkTime] 工作时间检查，当前时间: {}, 工作时间: {} - {}, 是否在工作时间: {}", 
                now, start, end, inWorkTime);
        
        return inWorkTime;
    }

    /**
     * 计算下一个工作时间
     *
     * @return 下一个工作时间
     */
    public LocalDateTime getNextWorkTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalTime currentTime = now.toLocalTime();
        LocalTime workStart = notificationConfig.getWorkTime().getStart();
        
        LocalDateTime nextWorkTime;
        
        if (currentTime.isBefore(workStart)) {
            // 当前时间早于工作开始时间，安排到今天的工作开始时间
            nextWorkTime = now.toLocalDate().atTime(workStart);
        } else {
            // 当前时间晚于工作结束时间，安排到明天的工作开始时间
            nextWorkTime = now.toLocalDate().plusDays(1).atTime(workStart);
        }
        
        log.debug("[getNextWorkTime] 计算下一个工作时间，当前时间: {}, 下一个工作时间: {}", now, nextWorkTime);
        
        return nextWorkTime;
    }

    /**
     * 检查指定时间是否在工作时间内
     *
     * @param dateTime 指定时间
     * @return 是否在工作时间内
     */
    public boolean isWorkTime(LocalDateTime dateTime) {
        LocalTime time = dateTime.toLocalTime();
        LocalTime start = notificationConfig.getWorkTime().getStart();
        LocalTime end = notificationConfig.getWorkTime().getEnd();
        
        return !time.isBefore(start) && !time.isAfter(end);
    }
}
