package com.hnyiti.kuaidi.module.pgsql.service.notification.channel;

import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.service.notification.model.NotificationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 短信通知渠道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SmsNotificationChannel implements NotificationChannel {

    @Override
    public NotificationResult send(NotificationTask task) {
        log.info("[send] 发送短信通知，任务ID: {}, 订单ID: {}", task.getId(), task.getOrderId());

        try {
            // 从任务数据中获取必要信息
            Map<String, Object> taskData = task.getTaskData();
            String mobile = (String) taskData.get("mobile");
            String templateCode = (String) taskData.get("templateCode");

            if (mobile == null || templateCode == null) {
                return NotificationResult.failure("缺少必要参数：mobile或templateCode");
            }

            // TODO: 调用短信发送服务API
            // 这里需要集成现有的短信发送服务

            // 构造消息内容
            String content = buildMessageContent(taskData);

            // 模拟发送成功
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("bizId", "mock_bizId_" + System.currentTimeMillis());
            responseData.put("code", "OK");
            responseData.put("message", "发送成功");

            log.info("[send] 短信通知发送成功，任务ID: {}, mobile: {}", task.getId(), mobile);

            return NotificationResult.success(mobile, content, responseData);

        } catch (Exception e) {
            log.error("[send] 发送短信通知异常，任务ID: {}", task.getId(), e);
            return NotificationResult.failure("发送异常: " + e.getMessage());
        }
    }

    @Override
    public boolean isAvailable() {
        // TODO: 检查短信服务是否可用
        return true;
    }

    @Override
    public String getChannelType() {
        return NotificationTask.TaskType.SMS.getCode();
    }

    /**
     * 构造消息内容
     */
    private String buildMessageContent(Map<String, Object> taskData) {
        // 从任务数据中提取订单信息构造消息内容
        String orderId = (String) taskData.get("orderId");
        Object compensationAmount = taskData.get("compensationAmount");

        return String.format("【快递补差】您的订单%s需要补差%s元，请及时处理。回复TD退订",
                orderId, compensationAmount != null ? compensationAmount.toString() : "0");
    }
}
