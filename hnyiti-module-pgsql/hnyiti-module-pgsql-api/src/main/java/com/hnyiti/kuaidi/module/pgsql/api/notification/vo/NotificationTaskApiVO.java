package com.hnyiti.kuaidi.module.pgsql.api.notification.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知任务 API 响应 VO
 *
 * <AUTHOR>
 */
@Data
public class NotificationTaskApiVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 任务类型：MINI_PROGRAM, WECHAT_MP, SMS, PHONE_CALL
     */
    private String taskType;

    /**
     * 任务状态：PENDING, EXECUTING, COMPLETED, CANCELLED, FAILED
     */
    private String taskStatus;

    /**
     * 计划执行时间
     */
    private LocalDateTime scheduledTime;

    /**
     * 实际执行时间
     */
    private LocalDateTime actualExecuteTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 下次重试时间
     */
    private LocalDateTime nextRetryTime;

    /**
     * 任务数据（收件人、内容等）
     */
    private Map<String, Object> taskData;

    /**
     * 执行结果数据
     */
    private Map<String, Object> resultData;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
