package com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知任务表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "notification_task", autoResultMap = true)
public class NotificationTask {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 任务类型：MINI_PROGRAM, WECHAT_MP, SMS, PHONE_CALL
     */
    private String taskType;

    /**
     * 任务状态：PENDING, EXECUTING, COMPLETED, CANCELLED, FAILED
     */
    private String taskStatus;

    /**
     * 计划执行时间
     */
    private LocalDateTime scheduledTime;

    /**
     * 实际执行时间
     */
    private LocalDateTime actualExecuteTime;

    /**
     * 重试次数
     */
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount = 3;

    /**
     * 下次重试时间
     */
    private LocalDateTime nextRetryTime;

    /**
     * 任务数据（收件人、内容等）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> taskData;

    /**
     * 执行结果数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> resultData;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 任务类型枚举
     */
    public enum TaskType {
        MINI_PROGRAM("MINI_PROGRAM", "小程序通知"),
        WECHAT_MP("WECHAT_MP", "公众号通知"),
        SMS("SMS", "短信通知"),
        PHONE_CALL("PHONE_CALL", "电话通知");

        private final String code;
        private final String name;

        TaskType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("PENDING", "待执行"),
        EXECUTING("EXECUTING", "执行中"),
        COMPLETED("COMPLETED", "已完成"),
        CANCELLED("CANCELLED", "已取消"),
        FAILED("FAILED", "执行失败");

        private final String code;
        private final String name;

        TaskStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
