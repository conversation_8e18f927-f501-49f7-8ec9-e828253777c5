package com.hnyiti.kuaidi.module.system.service.tenant;

import com.hnyiti.kuaidi.framework.common.pojo.PageResult;
import com.hnyiti.kuaidi.framework.tenant.core.context.TenantContextHolder;
import com.hnyiti.kuaidi.module.system.api.tenant.dto.*;
import com.hnyiti.kuaidi.module.system.dal.dataobject.tenant.TenantDO;
import com.hnyiti.kuaidi.module.system.service.tenant.handler.TenantInfoHandler;
import com.hnyiti.kuaidi.module.system.service.tenant.handler.TenantMenuHandler;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 租户 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantService {

    /**
     * 创建租户
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenant(@Valid TenantCreateReqVO createReqVO);

    /**
     * 更新租户
     *
     * @param updateReqVO 更新信息
     */
    void updateTenant(@Valid TenantUpdateReqVO updateReqVO);

    /**
     * 更新租户的角色菜单
     *
     * @param tenantId 租户编号
     * @param menuIds 菜单编号数组
     */
    void updateTenantRoleMenu(Long tenantId, Set<Long> menuIds);

    /**
     * 删除租户
     *
     * @param id 编号
     */
    void deleteTenant(Long id);

    /**
     * 获得租户
     *
     * @param id 编号
     * @return 租户
     */
    TenantDO getTenant(Long id);

    /**
     * 获得租户分页
     *
     * @param pageReqVO 分页查询
     * @return 租户分页
     */
    PageResult<TenantRespVO> getTenantPage(TenantPageVO pageReqVO);


    /**
     * 列表查询
     * @param reqVO
     * @return
     */
    List<TenantDO> getTenantList(TenantQueryVO reqVO);

    /**
     * 列表查询
     *
     * @param reqVO
     * @return
     */
    List<TenantDO> getTenantAllList(TenantQueryVO reqVO);

    /**
     * 获得名字对应的租户
     *
     * @param name 组户名
     * @return 租户
     */
    TenantDO getTenantByName(String name);

    /**
     * 获得域名对应的租户
     *
     * @param domain 域名
     * @return 租户
     */
    TenantDO getTenantByDomain(String domain);

    /**
     * 获得使用指定权限的租户数量
     *
     * @param packageId 租户权限编号
     * @return 租户数量
     */
    Long getTenantCountByPackageId(Long packageId);

    /**
     * 获得使用指定权限的租户数组
     *
     * @param packageId 租户权限编号
     * @return 租户数组
     */
    List<TenantDO> getTenantListByPackageId(Long packageId);

    /**
     * 进行租户的信息处理逻辑
     * 其中，租户编号从 {@link TenantContextHolder} 上下文中获取
     *
     * @param handler 处理器
     */
    void handleTenantInfo(TenantInfoHandler handler);

    /**
     * 进行租户的菜单处理逻辑
     * 其中，租户编号从 {@link TenantContextHolder} 上下文中获取
     *
     * @param handler 处理器
     */
    void handleTenantMenu(TenantMenuHandler handler);

    /**
     * 获得所有租户
     *
     * @return 租户编号数组
     */
    List<Long> getTenantIdList();

    /**
     * 校验租户是否合法
     *
     * @param id 租户编号
     */
    void validTenant(Long id);

    /**
     * 加盟商费用 操作
     * @param tenantId
     * @param tenantAmount 扣费金额 (前面有负数代码减)
     * @param scene
     * @param remark
     * @return
     */
    boolean deductBalance(Long tenantId, BigDecimal tenantAmount,Long orderId, String scene, String remark);

    List<TenantDO> getTenantList2(TenantQueryVO reqVO);
}
