package com.hnyiti.kuaidi.module.delivery.service.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.hnyiti.kuaidi.framework.security.core.util.SecurityFrameworkUtils;
import com.hnyiti.kuaidi.framework.tenant.core.context.TenantContextHolder;
import com.hnyiti.kuaidi.module.consumer.wxmsg.WxMiniMsgVo;
import com.hnyiti.kuaidi.module.consumer.wxmsg.WxMqProducer;
import com.hnyiti.kuaidi.module.consumer.wxmsg.WxMsgTypeConstants;
import com.hnyiti.kuaidi.module.delivery.controller.admin.kd.tenantChannel.vo.TenantChannelConfigVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.orderfee.vo.OrderFeeBaseVO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.channelprice.ChannelPriceDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.expressproduct.ExpressProductDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.feesnapshot.FeeSnapshotDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.order.OrderDO;
import com.hnyiti.kuaidi.module.delivery.enums.OrderConstants;
import com.hnyiti.kuaidi.module.delivery.service.cache.CacheService;
import com.hnyiti.kuaidi.module.delivery.service.channelprice.ChannelPriceService;
import com.hnyiti.kuaidi.module.delivery.service.expressproduct.ExpressProductService;
import com.hnyiti.kuaidi.module.delivery.service.feesnapshot.FeeSnapshotService;
import com.hnyiti.kuaidi.module.delivery.service.kd.tenantChannel.TenantChannelService;
import com.hnyiti.kuaidi.module.member.api.levels.MemberLevelsApi;
import com.hnyiti.kuaidi.module.member.api.memberdiscount.vo.MemberDiscountRespVO;
import com.hnyiti.kuaidi.module.member.api.user.MemberUserApi;
import com.hnyiti.kuaidi.module.member.api.user.dto.MemberUserRespDTO;
import com.hnyiti.kuaidi.module.system.api.param.ParamApi;
import com.hnyiti.kuaidi.module.system.api.tenant.TenantApi;
import com.hnyiti.kuaidi.module.system.api.user.dto.CacheUserDTO;
import com.hnyiti.kuaidi.module.wxmini.weCom.WeChatGroupLevelEnum;
import com.hnyiti.kuaidi.module.wxmini.weCom.WeChatGroupMessageTypeEnum;
import com.hnyiti.kuaidi.vo.QueryPriceResVO;
import com.hnyiti.kuaidi.vo.QueryPriceVO;
import com.hnyiti.kuaidi.vo.UpdateFeesRepVO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Slf4j
@Service
public class OrderCalcService {

    @Resource
    private ChannelPriceService channelPriceService;

    @Resource
    private CacheService cacheService;

    @Resource
    private ExpressProductService expressProductService;

    @Resource
    private FeeSnapshotService feeSnapshotService;

    @Resource
    private TenantChannelService tenantChannelService;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private MemberLevelsApi levelsApi;

    @Resource
    private MemberUserApi memberUserApi;

    @Lazy
    @Resource
    private WxMqProducer wxMqProducer;

    @Resource
    private ParamApi paramApi;

    public void calcPrice(QueryPriceVO queryPriceVO, List<QueryPriceResVO> result) {
        if (CollUtil.isEmpty(result)) {
            return;
        }
        String orderType = ObjectUtil.isNotEmpty(queryPriceVO.getOrderType()) ? queryPriceVO.getOrderType() : OrderConstants.ORDER_TYPE_USER;

        Long userId = ObjectUtil.isNotEmpty(queryPriceVO.getUserId()) ? queryPriceVO.getUserId() : SecurityFrameworkUtils.getLoginUserId();
        for (QueryPriceResVO queryPriceResVO : result) {
            log.info("{}, 快递原价 {}", userId, queryPriceResVO.getPrice());

            // 附加费加价
            if(ObjectUtil.isNotEmpty(queryPriceResVO.getSurchargePriceId())){
                systemSurchargeCalc(queryPriceVO, queryPriceResVO);
            }

            // 总部加价方案
            systemPriceCalc(queryPriceVO, queryPriceResVO);

            // 后台下单午休加代理商配置的加价
            if(orderType != OrderConstants.ORDER_TYPE_SYS) {
                // 代理商加价方案
                tenantPriceCalc(queryPriceVO, queryPriceResVO, userId);
            } else {
                queryPriceResVO.setLastPrice(queryPriceResVO.getTenantPayAmount());
            }
        }
    }

    private void systemSurchargeCalc(QueryPriceVO queryPriceVO, QueryPriceResVO queryPriceResVO){
        Long surchargePriceId = queryPriceResVO.getSurchargePriceId();

        ChannelPriceDO channelPrice = channelPriceService.getChannelPrice(surchargePriceId);
        if(ObjectUtil.isEmpty(channelPrice)){
            return;
        }
        if(ObjectUtil.isEmpty(channelPrice.getFirstPrice()) || ObjectUtil.isEmpty(channelPrice.getContinuePrice())){
            return;
        }
        // 0 不加价 1 渠道加价 2 官方折扣 -- 都不需要进行加价计算
        if (null != surchargePriceId && surchargePriceId > 2) {
            BigDecimal calcFeeWeight = calcWeightByVolume(queryPriceVO.getVloumLong(), queryPriceVO.getVloumWidth(), queryPriceVO.getVloumHeight(), queryPriceResVO.getWeightRatio(), queryPriceVO.getWeight());
            // 计算附加费
            BigDecimal lastPrice = channelPrice.getFirstPrice();
            if (calcFeeWeight.compareTo(BigDecimal.ONE) > 0) {
                // 续重
                BigDecimal continueWeight = calcFeeWeight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                lastPrice = lastPrice.add(continueWeight.multiply(channelPrice.getContinuePrice()));
            }

            if(lastPrice.compareTo(BigDecimal.ZERO) > 0){
                lastPrice = queryPriceResVO.getPrice().add(lastPrice);
                queryPriceResVO.setPrice(lastPrice);
            }
        }


    }


    private void systemPriceCalc(QueryPriceVO queryPriceVO, QueryPriceResVO queryPriceResVO) {
        Long systemPriceId = queryPriceResVO.getSystemPriceId();
        queryPriceResVO.setTenantPayAmount(queryPriceResVO.getPrice());

        // 0 不加价 1 渠道加价 2 官方折扣 -- 都不需要进行加价计算
        if (null != systemPriceId && systemPriceId > 2) {
            ChannelPriceDO channelPrice = channelPriceService.getChannelPrice(systemPriceId);

            // 设置系统快照版本号
            if(ObjectUtil.isNotEmpty(channelPrice.getVersion())){
                queryPriceResVO.setSysVersion(channelPrice.getVersion());
            }

            // 总部加价考虑抛比加价、首重加价、续重加价
//            BigDecimal weight = queryPriceVO.getWeight();
//            queryPriceVO.setWeight(calcWeightByVolume(queryPriceVO.getVloumLong(), queryPriceVO.getVloumWidth(), queryPriceVO.getVloumHeight(), channelPrice.getRation(), weight));
            BigDecimal weight = calcWeightByVolume(queryPriceVO.getVloumLong(), queryPriceVO.getVloumWidth(), queryPriceVO.getVloumHeight(), queryPriceResVO.getWeightRatio(), queryPriceVO.getWeight());
            Long priceType = channelPrice.getPriceType();

            BigDecimal systemPrice = queryPriceResVO.getPrice();
            BigDecimal addPrice = queryPriceResVO.getAddPrice();
            // 平台快递增加比例
            BigDecimal ration = channelPrice.getRation();
            BigDecimal firstPrice = ObjectUtil.isEmpty(channelPrice.getFirstPrice()) ? BigDecimal.ZERO : channelPrice.getFirstPrice();
            BigDecimal continuePrice = ObjectUtil.isEmpty(channelPrice.getContinuePrice()) ? BigDecimal.ZERO : channelPrice.getContinuePrice();

            if (OrderConstants.PRICE_TYPE_RATIO.equals(priceType+"")) {
                // 按比例加价 只取首重比例
                if (isCorrectNumber(ration)) {
                    systemPrice = systemPrice.add(systemPrice.multiply(ration.divide(new BigDecimal(100))));
                    queryPriceResVO.setTenantPayAmount(systemPrice);
                    if(ObjectUtil.isNotEmpty(addPrice)){
                        addPrice = addPrice.add(addPrice.multiply(ration.divide(new BigDecimal(100)))).setScale(2, RoundingMode.UP);
                        queryPriceResVO.setAddPrice(addPrice);
                    }
                    log.info("{}, 平台 按照比例加价 {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), ration, systemPrice);
                }
            } else if (OrderConstants.PRICE_TYPE_FIX.equals(priceType+"")) {
                // 按照固定金额加价
                systemPrice = systemPrice.add(firstPrice);
                if (weight.compareTo(BigDecimal.ZERO) > 0) {
                    // 续重
                    BigDecimal continueWeight = weight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                    systemPrice = systemPrice.add(continueWeight.multiply(continuePrice));
                    if(ObjectUtil.isNotEmpty(addPrice)){
                        addPrice = addPrice.add(continuePrice).setScale(2, RoundingMode.UP);
                        queryPriceResVO.setAddPrice(addPrice);
                    }
                }
                queryPriceResVO.setTenantPayAmount(systemPrice);
                log.info("{}, 平台 按照固定金额加价 {} {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), firstPrice, continuePrice, systemPrice);
            }
        }
        // 总是向上舍入
        if(ObjectUtil.isNotEmpty(queryPriceResVO.getTenantPayAmount())){
            queryPriceResVO.setTenantPayAmount(queryPriceResVO.getTenantPayAmount().setScale(2, RoundingMode.CEILING));
        }
    }

    private void tenantPriceCalc(QueryPriceVO queryPriceVO, QueryPriceResVO queryPriceResVO,Long userId) {
        Long tenantPriceId = queryPriceResVO.getTenantPriceId();
        queryPriceResVO.setLastPrice(queryPriceResVO.getTenantPayAmount());

        BigDecimal weight = calcWeightByVolume(queryPriceVO.getVloumLong(), queryPriceVO.getVloumWidth(), queryPriceVO.getVloumHeight(), queryPriceResVO.getWeightRatio(), queryPriceVO.getWeight());
        // 0 不加价 1 渠道加价 2 官方折扣 -- 都不需要进行加价计算
        if (null != tenantPriceId && tenantPriceId > 2) {
            ChannelPriceDO channelPrice = channelPriceService.getChannelPrice(tenantPriceId);

            // 设置系统快照版本号
            if(ObjectUtil.isNotEmpty(channelPrice.getVersion())){
                queryPriceResVO.setTenantVersion(channelPrice.getVersion());
            }

            // 代理加价考虑抛比、vip折扣、首重加价、续重加价
//            BigDecimal weight = queryPriceVO.getWeight();
//            queryPriceVO.setWeight(calcWeightByVolume(queryPriceVO.getVloumLong(), queryPriceVO.getVloumWidth(), queryPriceVO.getVloumHeight(), channelPrice.getRation(), queryPriceVO.getWeight()));

            Long priceType = channelPrice.getPriceType();

            BigDecimal lastPrice = queryPriceResVO.getTenantPayAmount();
            BigDecimal addPrice = queryPriceResVO.getAddPrice();
            BigDecimal firstWeightPrice = queryPriceResVO.getFirstWeightPrice();
            // 平台快递增加比例
            BigDecimal ration = channelPrice.getRation();
            BigDecimal firstPrice = channelPrice.getFirstPrice();
            BigDecimal continuePrice = channelPrice.getContinuePrice();

            if (OrderConstants.PRICE_TYPE_RATIO.equals(priceType+"")) {
                // 按比例加价 只取首重比例
                if (isCorrectNumber(ration)) {
                    lastPrice = lastPrice.add(lastPrice.multiply(ration.divide(new BigDecimal(100))));
                    queryPriceResVO.setLastPrice(lastPrice);
                    queryPriceResVO.setUserPrice(lastPrice);
                    if(ObjectUtil.isNotEmpty(addPrice)){
                        addPrice = addPrice.add(addPrice.multiply(ration.divide(new BigDecimal(100)))).setScale(2, RoundingMode.UP);
                        queryPriceResVO.setAddPrice(addPrice);
                    }
                    if(ObjectUtil.isNotEmpty(firstWeightPrice)){
                        firstWeightPrice = firstWeightPrice.add(firstWeightPrice.multiply(ration.divide(new BigDecimal(100)))).setScale(2, RoundingMode.UP);
                        queryPriceResVO.setFirstWeightPrice(firstWeightPrice);
                    }
                    log.info("{}, 代理 按照比例加价 {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), ration, lastPrice);
                }
            } else if (OrderConstants.PRICE_TYPE_FIX.equals(priceType+"")) {
                // 按照固定金额加价
                lastPrice = lastPrice.add(firstPrice);
                // 续重价格加价
                if (weight.compareTo(BigDecimal.ZERO) > 0) {
                    // 续重
                    BigDecimal continueWeight = weight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                    lastPrice = lastPrice.add(continueWeight.multiply(continuePrice));
                    if(ObjectUtil.isNotEmpty(addPrice)){
                        addPrice = addPrice.add(continuePrice).setScale(2, RoundingMode.UP);
                        queryPriceResVO.setAddPrice(addPrice);
                    }
                }
                // 首重价格加价
                if(ObjectUtil.isNotEmpty(firstPrice) && ObjectUtil.isNotEmpty(firstWeightPrice)){
                    firstWeightPrice = firstWeightPrice.add(firstPrice).setScale(2, RoundingMode.UP);
                    queryPriceResVO.setFirstWeightPrice(firstWeightPrice);
                }
                queryPriceResVO.setLastPrice(lastPrice);
                queryPriceResVO.setUserPrice(lastPrice);
                log.info("{}, 代理 按照固定金额加价 {}, 重量{} , 结果 {}", SecurityFrameworkUtils.getLoginUserId(), firstPrice, weight, lastPrice);
            }
        }

        // 总是向上舍入
        if(ObjectUtil.isNotEmpty(queryPriceResVO.getLastPrice())){
            queryPriceResVO.setLastPrice(queryPriceResVO.getLastPrice().setScale(2, RoundingMode.CEILING));
        }

//        // 计费会员优惠价格 vip 折扣 取值在[1-99]
//        String vipDiscount = queryPriceResVO.getVipDiscount();  // 会员折扣
//
//        /**
//         * 如果是模式5的情况下，会员折扣率要去会员等级管理表中拿
//         * 会员等级管理表-配置的会员折扣率会有如下几种值：固定金额：0.2；百分比：97%；首续重：0.2,0.2(首重,续重)
//         * 把首续重解析为固定金额（就是计算出需要优惠多少钱）
//         */
//        // 查询当前代理商活动类型
//        TenantRespVO tenantRespVO = tenantApi.getTenantInfo(TenantContextHolder.getTenantId()).getData();
//        if (null != tenantRespVO && null != tenantRespVO.getMktType() && "5".equals(tenantRespVO.getMktType())) {
//            CacheUserDTO userInfo = cacheService.getUserInfo(userId);
//            if(null != userInfo && null != userInfo.getUserLevel()){
//                if (userInfo.getUserLevel() == 1) {
//                    // 如果当前用户是普通用户，那么就去查询钻石会员的优惠，用于展示会员价
//                    LevelsRespVO infoByLevel = levelsApi.getInfoByLevel(2);
//                    vipDiscount = infoByLevel.getOrderDiscount();
//                } else {
//                    // 获取会员等级管理表-配置的会员折扣率
//                    LevelsRespVO infoByLevel = levelsApi.getInfoByLevel(userInfo.getUserLevel());
//                    String orderDiscount = infoByLevel.getOrderDiscount();
//                    if (orderDiscount.contains(",")) {
//                        // 百分比(首重,续重)优惠
//                        String[] split = orderDiscount.split(",");
//                        if (!split[0].isEmpty() && !split[1].isEmpty()) {
//
//                            BigDecimal firstPrice = new BigDecimal(split[0]);
//                            BigDecimal multiply = BigDecimal.ZERO;
//                            if (weight.compareTo(BigDecimal.ZERO) > 0) {
//                                // 续重
//                                BigDecimal continueWeight = weight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
//                                BigDecimal continuePrice = new BigDecimal(split[1]);
//                                multiply = continueWeight.multiply(continuePrice);
//                            }
//
//                            // 计算出百分比优惠的金额 转成 固定金额 的算法。这样就不要动之前的逻辑
//                            vipDiscount = String.valueOf(firstPrice.add(multiply));
//                        } else {
//                            vipDiscount = "0";
//                        }
//                    } else {
//                        vipDiscount = orderDiscount;
//                    }
//
//                    // 把查价时当前代理商的模式类型、会员折扣率返回出去。然后创建订单接口传进去保存起来。防止查价后下单前切换模式导致报错的模式不一致的问题
//                    queryPriceResVO.setModeFiveMktType(tenantRespVO.getMktType());
//                    queryPriceResVO.setModeFiveVipDiscount(vipDiscount);
//
//                    if (orderDiscount.contains(",")) {
//                        queryPriceResVO.setModeFiveVipDiscount(orderDiscount);
//                    }
//                }
//            }
//        }
//
//        if (isCorrectDiscount(vipDiscount)) {
////            BigDecimal vipPrice = queryPriceResVO.getLastPrice().multiply(vipDiscount).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.CEILING);
//            // 计算会员则扣
//            BigDecimal vipPrice = calVipPrice(vipDiscount,queryPriceResVO.getLastPrice());
//            queryPriceResVO.setVipPrice(vipPrice);
//
//            log.info("{}, 按照比例计算会员 金额加价 {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), vipDiscount, vipPrice);
//        } else {
//            queryPriceResVO.setVipPrice(queryPriceResVO.getLastPrice());
//        }


        // 如果是会员 将价格塞入最终的金额中
        if(ObjectUtil.isNotEmpty(userId)){
            // 查询用户
            MemberUserRespDTO userInfo = memberUserApi.getUser(userId);

            if(ObjectUtil.isNotEmpty(userInfo.getUserLevel())) {
                // 计算会员价格
                MemberDiscountRespVO memberDiscount = levelsApi.getMemberDiscountIsDefaultDisplay(userInfo.getLoginMode()).getData();
                // 根据会员折扣计算vipPrice
                if (ObjectUtil.isNotEmpty(memberDiscount) && !memberDiscount.getMemberLevel().equals(userInfo.getUserLevel())) {
                    // 抽取为独立方法
                    calcVipPriceByMemberDiscount(queryPriceResVO, memberDiscount, weight);
                }
            }

            if(ObjectUtil.isNotEmpty(userInfo.getUserLevel()) && ObjectUtil.isNotEmpty(userInfo.getLoginMode())) {
                // 获取用户折扣配置
                MemberDiscountRespVO memberDiscount = levelsApi.getMemberDiscount(userInfo.getUserLevel(), userInfo.getLoginMode()).getData();
                // 根据会员折扣计算价格
                if (ObjectUtil.isNotEmpty(memberDiscount)) {
                    calcMemberDiscountPrice(queryPriceResVO, memberDiscount, weight);
                }
            }
        }

        if(ObjectUtil.isNotEmpty(queryPriceResVO.getVipPrice())){
            queryPriceResVO.setVipPrice(queryPriceResVO.getVipPrice().setScale(2, RoundingMode.CEILING));
        }

        if(ObjectUtil.isNotEmpty(queryPriceResVO.getUserPrice())){
            queryPriceResVO.setUserPrice(queryPriceResVO.getUserPrice().setScale(2, RoundingMode.CEILING));
        }

        if (ObjectUtil.isNotEmpty(queryPriceResVO.getLastPrice()) && ObjectUtil.isNotEmpty(queryPriceResVO.getPrice())
                && queryPriceResVO.getPrice().compareTo(queryPriceResVO.getLastPrice()) >= 0) {
            try {
                // 如果最终价格大于成本价，则按配置加价
                priceDifferenceMarkup(queryPriceVO, queryPriceResVO, queryPriceResVO.getPrice());
            } catch (Exception e) {
                log.error("{}, 代理 最终金额小于第三方金额按照加价配置额外加价失败, 异常信息 {}", SecurityFrameworkUtils.getLoginUserId(), e.getMessage());
            }
        }

        // 如果成本价小于提完之后的价格(lastPrice)则用户支付，成本价格。
        if(ObjectUtil.isNotEmpty(queryPriceResVO.getLastPrice()) && ObjectUtil.isNotEmpty(queryPriceResVO.getPrice())
                && queryPriceResVO.getPrice().compareTo(queryPriceResVO.getLastPrice()) >= 0){
            // 发送企业微信群通知
            abnormalPriceMsg(queryPriceVO, queryPriceResVO);
        }

        // 如果成本价小于提完之后的价格(lastPrice)则用户支付，成本价格。
        if(ObjectUtil.isNotEmpty(queryPriceResVO.getLastPrice()) && ObjectUtil.isNotEmpty(queryPriceResVO.getPrice())
                && queryPriceResVO.getPrice().compareTo(queryPriceResVO.getLastPrice()) > 0){
            queryPriceResVO.setLastPrice(queryPriceResVO.getPrice());
            queryPriceResVO.setVipPrice(queryPriceResVO.getPrice());
        }
       if(ObjectUtil.isNotEmpty(queryPriceResVO.getDefPrice())){
            BigDecimal defPrice = new BigDecimal(queryPriceResVO.getDefPrice());
            if(defPrice.compareTo(queryPriceResVO.getLastPrice()) <= 0){
                defPrice = queryPriceResVO.getLastPrice().add(new BigDecimal(2)).setScale(2, RoundingMode.CEILING);
            }
            queryPriceResVO.setDefPrice(defPrice.toString());
        }

        if(ObjectUtil.isEmpty(queryPriceResVO.getDefPrice())){
            BigDecimal defPrice = queryPriceResVO.getLastPrice().add(new BigDecimal(2)).setScale(2, RoundingMode.CEILING);
            queryPriceResVO.setDefPrice(defPrice.toString());
        }
    }

    /**
     * 价格差异按配置加价(如果最终价格小于第三方价格)
     */
    private void priceDifferenceMarkup(QueryPriceVO queryPriceVO, QueryPriceResVO queryPriceResVO, BigDecimal lastPrice) {
        // 加价状态
        String markupStatus = paramApi.getMarkupStatus(TenantContextHolder.getTenantId());
        // 加价类型
        String priceType = paramApi.getMarkupType(TenantContextHolder.getTenantId());
        // 加价比例
        String markupRation = paramApi.getMarkupRation(TenantContextHolder.getTenantId());
        // 首重加价
        String markupFirstPrice = paramApi.getMarkupFirstPrice(TenantContextHolder.getTenantId());
        //  续重加价
        String markupContinuePrice = paramApi.getMarkupContinuePrice(TenantContextHolder.getTenantId());

        log.error(" 最终金额小于第三方金额按照比例加价参数：markupStatus：{}, priceType：{}, markupRation：{}, markupFirstPrice：{}, markupContinuePrice：{}",
                markupStatus, priceType, markupRation, markupFirstPrice, markupContinuePrice);

        if (ObjectUtil.isEmpty(markupStatus) || ObjectUtil.isEmpty(priceType) || !"0".equals(markupStatus)) {
            return;
        }
        // 平台快递增加比例
        BigDecimal ration = null;
        BigDecimal firstPrice = null;
        BigDecimal continuePrice = null;
        String extraCharge = "";
        // 百分比加价
        if ("100".equals(priceType)) {
            if (ObjectUtil.isEmpty(markupRation)) {
                return;
            }
            try {
                ration = new BigDecimal(markupRation);
            } catch (Exception e) {
                log.error("加价配置百分比转换异常", e);
                return;
            }
            extraCharge = ration + "%";
        } else if ("101".equals(priceType)) {
            // 固定加价
            if (ObjectUtil.isEmpty(markupFirstPrice) || ObjectUtil.isEmpty(markupContinuePrice)) {
                return;
            }
            try {
                firstPrice = new BigDecimal(markupFirstPrice);
                continuePrice = new BigDecimal(markupContinuePrice);
            } catch (Exception e) {
                log.error("加价配置首重，续重加价转换异常", e);
                return;
            }
            extraCharge = firstPrice + "," + continuePrice;
        } else {
            // 非法类型
            return;
        }

        BigDecimal weight = calcWeightByVolume(queryPriceVO.getVloumLong(), queryPriceVO.getVloumWidth(), queryPriceVO.getVloumHeight(), queryPriceResVO.getWeightRatio(), queryPriceVO.getWeight());

        if (OrderConstants.PRICE_TYPE_RATIO.equals(priceType)) {
            // 按比例加价 只取首重比例
            if (isCorrectNumber(ration)) {
                lastPrice = lastPrice.add(lastPrice.multiply(ration.divide(new BigDecimal(100))));
                queryPriceResVO.setLastPrice(lastPrice);
                queryPriceResVO.setUserPrice(lastPrice);
                queryPriceResVO.setExtraCharge(extraCharge);

                log.info("{}, 代理 最终金额小于第三方金额按照比例加价 {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), ration, lastPrice);
            }
        } else if (OrderConstants.PRICE_TYPE_FIX.equals(priceType)) {
            // 按照固定金额加价
            lastPrice = lastPrice.add(firstPrice);
            // 续重价格加价
            if (weight.compareTo(BigDecimal.ZERO) > 0) {
                // 续重
                BigDecimal continueWeight = weight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                lastPrice = lastPrice.add(continueWeight.multiply(continuePrice));
            }
            log.error(" 最终金额小于第三方金额按照比例加价最终金额：lastPrice：{}", lastPrice);
            queryPriceResVO.setLastPrice(lastPrice);
            queryPriceResVO.setUserPrice(lastPrice);
            queryPriceResVO.setExtraCharge(extraCharge);
            log.info("{}, 代理 最终金额小于第三方金额按照加价配置固定金额加价 {}, 重量{} , 结果 {}", SecurityFrameworkUtils.getLoginUserId(), firstPrice, weight, lastPrice);
        }
    }


    private void abnormalPriceMsg(QueryPriceVO queryPriceVO ,QueryPriceResVO queryPriceResVO) {
        WxMiniMsgVo miniMsgVo = new WxMiniMsgVo();
        miniMsgVo.setTenantId(TenantContextHolder.getTenantId());
        miniMsgVo.setBizId(SecurityFrameworkUtils.getLoginUserId()+"");
        miniMsgVo.setCallBackJson("");

        StringBuilder stackTraceStr = new StringBuilder();
        stackTraceStr.append("\n").append("请求json: ").append("\n").append(JacksonUtils.toJson(queryPriceVO)).append("\n");
        stackTraceStr.append("返回json:").append("\n").append(JacksonUtils.toJson(queryPriceResVO)).append("\n");


        // 设置错误信息
        miniMsgVo.setExceptionMessage("运费查价警告：总部渠道id:"+queryPriceResVO.getChannelExpressId()+",渠道id:"+
                queryPriceResVO.getProductId()+"，第三方价格："+queryPriceResVO.getPrice()+",最终价格："+queryPriceResVO.getLastPrice());


        miniMsgVo.setMsgType(WxMsgTypeConstants.ERROR);
        miniMsgVo.setBizType(WxMsgTypeConstants.PRICE_ABNORMAL);
        miniMsgVo.setGroupLevel(WeChatGroupLevelEnum.BUSINESS_LEVEL);
        miniMsgVo.setGroupMessageType(WeChatGroupMessageTypeEnum.PRICE_VARIANCE_NOTIFY);
        miniMsgVo.setCallBackJson(stackTraceStr.toString());
        wxMqProducer.sendMiniMessage(miniMsgVo);
//        wxMqProducer.sendMiniMessage(miniMsgVo);
    }

    /**
     * 根据会员折扣计算vipPrice
     *
     * @param queryPriceResVO 价格对象
     * @param memberDiscount 会员折扣信息
     * @param weight 重量
     */
    private void calcVipPriceByMemberDiscount(QueryPriceResVO queryPriceResVO, MemberDiscountRespVO memberDiscount, BigDecimal weight) {
        // 获取折扣类型
        Integer discountType = memberDiscount.getDiscountType();
        if (ObjectUtil.isEmpty(discountType)) {
            return;
        }

        // 获取当前价格
        BigDecimal lastPrice = queryPriceResVO.getLastPrice();
        if (ObjectUtil.isEmpty(lastPrice)) {
            return;
        }

        BigDecimal discountedPrice = null;

        // 根据不同折扣类型进行计算
        if (discountType == 1) {
            // 1. 百分比折扣
            BigDecimal discountRate = memberDiscount.getDiscountRate();
            if (ObjectUtil.isNotEmpty(discountRate) && discountRate.compareTo(BigDecimal.ZERO) > 0 && discountRate.compareTo(BigDecimal.ONE) < 0) {

                // 计算折扣后价格 = 最终金额 * 折扣率，保留两位小数向上取整
                discountedPrice = lastPrice.multiply(discountRate).setScale(2, RoundingMode.CEILING);

                log.info("{}, 计算vipPrice - 按照百分比折扣计算会员价格 {}, 结果 {}",
                        SecurityFrameworkUtils.getLoginUserId(), discountRate, discountedPrice);
            }
        } else if (discountType == 2) {
            // 2. 首重减免
            BigDecimal firstWeightDiscount = memberDiscount.getFirstWeightDiscount();
            if (ObjectUtil.isNotEmpty(firstWeightDiscount) && firstWeightDiscount.compareTo(BigDecimal.ZERO) > 0) {

                // 计算折扣后价格 = 最终金额 - 首重减免金额，保留两位小数向上取整
                discountedPrice = lastPrice.subtract(firstWeightDiscount);
                if (discountedPrice.compareTo(BigDecimal.ZERO) < 0) {
                    discountedPrice = BigDecimal.ZERO;
                }
                discountedPrice = discountedPrice.setScale(2, RoundingMode.CEILING);

                log.info("{}, 计算vipPrice - 按照首重减免计算会员价格 {}, 结果 {}",
                        SecurityFrameworkUtils.getLoginUserId(), firstWeightDiscount, discountedPrice);
            }
        } else if (discountType == 3) {
            // 3. 首重、续重减免
            BigDecimal firstWeightDiscount = memberDiscount.getFirstWeightDiscount();
            BigDecimal continuedWeightDiscount = memberDiscount.getContinuedWeightDiscount();

            if (ObjectUtil.isNotEmpty(firstWeightDiscount) && firstWeightDiscount.compareTo(BigDecimal.ZERO) > 0) {
                // 计算续重部分减免金额
                BigDecimal continuedDiscount = BigDecimal.ZERO;
                if (ObjectUtil.isNotEmpty(continuedWeightDiscount) && continuedWeightDiscount.compareTo(BigDecimal.ZERO) > 0) {
                    if (weight.compareTo(BigDecimal.ONE) > 0) {
                        BigDecimal continuedWeight = weight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                        continuedDiscount = continuedWeight.multiply(continuedWeightDiscount);
                    }
                }

                // 计算折扣后价格 = 最终金额 - 首重减免金额 - 续重减免金额，保留两位小数向上取整
                BigDecimal totalDiscount = firstWeightDiscount.add(continuedDiscount);
                discountedPrice = lastPrice.subtract(totalDiscount);
                if (discountedPrice.compareTo(BigDecimal.ZERO) < 0) {
                    discountedPrice = BigDecimal.ZERO;
                }
                discountedPrice = discountedPrice.setScale(2, RoundingMode.CEILING);

                log.info("{}, 计算vipPrice - 按照首重续重减免计算会员价格 - 首重:{}, 续重:{}, 结果 {}",
                        SecurityFrameworkUtils.getLoginUserId(), firstWeightDiscount, continuedDiscount, discountedPrice);
            }
        }

        // 如果成功计算了折扣价格，设置到vipPrice
        if (ObjectUtil.isNotEmpty(discountedPrice)) {
            queryPriceResVO.setVipPrice(discountedPrice);
        }
    }

    /**
     * 根据会员折扣信息计算价格
     *
     * @param queryPriceResVO 价格对象
     * @param memberDiscount 会员折扣信息
     * @param weight 重量
     */
    private void calcMemberDiscountPrice(QueryPriceResVO queryPriceResVO, MemberDiscountRespVO memberDiscount, BigDecimal weight) {
        // 获取折扣类型
        Integer discountType = memberDiscount.getDiscountType();
        if (ObjectUtil.isEmpty(discountType)) {
            return;
        }

        // 设置折扣类型
        queryPriceResVO.setDiscountType(discountType.toString());

        // 获取当前价格
        BigDecimal lastPrice = queryPriceResVO.getLastPrice();
        if (ObjectUtil.isEmpty(lastPrice)) {
            return;
        }

        // 根据不同折扣类型进行计算
        if (discountType == 1) {
            // 1. 百分比折扣
            BigDecimal discountRate = memberDiscount.getDiscountRate();
            if (ObjectUtil.isEmpty(discountRate) || discountRate.compareTo(BigDecimal.ZERO) <= 0 || discountRate.compareTo(BigDecimal.ONE) >= 0) {
                return;
            }

            // 设置折扣率
            queryPriceResVO.setUserDiscounts(discountRate.toString());

            // 计算折扣后价格 = 最终金额 *  折扣率，保留两位小数向上取整
            BigDecimal discountedPrice = lastPrice.multiply(discountRate).setScale(2, RoundingMode.CEILING);
            queryPriceResVO.setLastPrice(discountedPrice);

            // 对加价金额和首重金额应用相同的折扣
            applyDiscountToAdditionalFees(queryPriceResVO, discountRate, discountType);

            log.info("{}, 按照百分比折扣计算会员价格 {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), discountRate, discountedPrice);

        } else if (discountType == 2) {
            // 2. 首重减免
            BigDecimal firstWeightDiscount = memberDiscount.getFirstWeightDiscount();
            if (ObjectUtil.isEmpty(firstWeightDiscount) || firstWeightDiscount.compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }

            // 设置折扣信息
            queryPriceResVO.setFirstWeightDiscount(firstWeightDiscount);

            // 计算折扣后价格 = 最终金额 - 首重减免金额，保留两位小数向上取整
            BigDecimal discountedPrice = lastPrice.subtract(firstWeightDiscount);
            if (discountedPrice.compareTo(BigDecimal.ZERO) < 0) {
                discountedPrice = BigDecimal.ZERO;
            }
            discountedPrice = discountedPrice.setScale(2, RoundingMode.CEILING);
            queryPriceResVO.setLastPrice(discountedPrice);

            // 对加价金额和首重金额应用相同的折扣
            applyDiscountToAdditionalFees(queryPriceResVO, firstWeightDiscount, discountType);

            log.info("{}, 按照首重减免计算会员价格 {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), firstWeightDiscount, discountedPrice);

        } else if (discountType == 3) {
            // 3. 首重、续重减免
            BigDecimal firstWeightDiscount = memberDiscount.getFirstWeightDiscount();
            BigDecimal continuedWeightDiscount = memberDiscount.getContinuedWeightDiscount();

            if (ObjectUtil.isEmpty(firstWeightDiscount) || firstWeightDiscount.compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }

            // 计算续重部分减免金额
            BigDecimal continuedDiscount = BigDecimal.ZERO;
            if (ObjectUtil.isNotEmpty(continuedWeightDiscount) && continuedWeightDiscount.compareTo(BigDecimal.ZERO) > 0) {
                if (weight.compareTo(BigDecimal.ONE) > 0) {
                    BigDecimal continuedWeight = weight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                    continuedDiscount = continuedWeight.multiply(continuedWeightDiscount);
                }
            }

            // 设置折扣信息
            queryPriceResVO.setFirstWeightDiscount(firstWeightDiscount);
            queryPriceResVO.setContinuedWeightDiscount(continuedWeightDiscount);

            // 计算折扣后价格 = 最终金额 - 首重减免金额 - 续重减免金额，保留两位小数向上取整
            BigDecimal totalDiscount = firstWeightDiscount.add(continuedDiscount);
            BigDecimal discountedPrice = lastPrice.subtract(totalDiscount);
            if (discountedPrice.compareTo(BigDecimal.ZERO) < 0) {
                discountedPrice = BigDecimal.ZERO;
            }
            discountedPrice = discountedPrice.setScale(2, RoundingMode.CEILING);
            queryPriceResVO.setLastPrice(discountedPrice);

            // 对加价金额和首重金额应用相同的折扣
            applyDiscountToAdditionalFees(queryPriceResVO, firstWeightDiscount, discountType, continuedWeightDiscount);

            log.info("{}, 按照首重续重减免计算会员价格 - 首重:{}, 续重:{}, 结果 {}",
                    SecurityFrameworkUtils.getLoginUserId(), firstWeightDiscount, continuedDiscount, discountedPrice);
        }
    }

    /**
     * 回调根据会员折扣信息计算价格
     *
     * @param queryPriceResVO 价格对象
     * @param weight 重量
     */
    private void calcCallbackMemberDiscountPrice(QueryPriceResVO queryPriceResVO, BigDecimal weight) {
        // 获取折扣类型
        String discountTypeStr = queryPriceResVO.getDiscountType();
        if (ObjectUtil.isEmpty(discountTypeStr)) {
            return;
        }
        Integer discountType = Integer.parseInt(discountTypeStr);

        // 获取当前价格
        BigDecimal lastPrice = queryPriceResVO.getLastPrice();
        if (ObjectUtil.isEmpty(lastPrice)) {
            return;
        }

        try {
            // 根据不同折扣类型进行计算
            if (discountType == 1) {
                // 1. 百分比折扣
                String userDiscounts = queryPriceResVO.getUserDiscounts();
                if (ObjectUtil.isEmpty(userDiscounts)) {
                    log.error("会员折扣值为空，无法执行百分比折扣计算");
                    return;
                }
//                BigDecimal discountRate = new BigDecimal(userDiscounts);
                BigDecimal discountRate;
                try {
                    discountRate = new BigDecimal(userDiscounts);
                } catch (NumberFormatException e) {
                    log.error("会员折扣值[{}]格式不正确，无法转换为有效数字", userDiscounts);
                    return;
                }
                if (ObjectUtil.isEmpty(discountRate) || discountRate.compareTo(BigDecimal.ZERO) <= 0 || discountRate.compareTo(BigDecimal.ONE) >= 0) {
                    return;
                }

                // 设置折扣率
                queryPriceResVO.setUserDiscounts(discountRate.toString());

                // 计算折扣后价格 = 最终金额 *  折扣率，保留两位小数向上取整
                BigDecimal discountedPrice = lastPrice.multiply(discountRate).setScale(2, RoundingMode.CEILING);
                queryPriceResVO.setLastPrice(discountedPrice);

                // 对加价金额和首重金额应用相同的折扣
                applyDiscountToAdditionalFees(queryPriceResVO, discountRate, discountType);

                log.info("{}, 按照百分比折扣计算会员价格 {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), discountRate, discountedPrice);

            } else if (discountType == 2) {
                // 2. 首重减免
                BigDecimal firstWeightDiscount = queryPriceResVO.getFirstWeightDiscount();
                if (ObjectUtil.isEmpty(firstWeightDiscount) || firstWeightDiscount.compareTo(BigDecimal.ZERO) <= 0) {
                    return;
                }

                // 计算折扣后价格 = 最终金额 - 首重减免金额，保留两位小数向上取整
                BigDecimal discountedPrice = lastPrice.subtract(firstWeightDiscount);
                if (discountedPrice.compareTo(BigDecimal.ZERO) < 0) {
                    discountedPrice = BigDecimal.ZERO;
                }
                discountedPrice = discountedPrice.setScale(2, RoundingMode.CEILING);
                queryPriceResVO.setLastPrice(discountedPrice);

                log.info("{}, 按照首重减免计算会员价格 {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), firstWeightDiscount, discountedPrice);

            } else if (discountType == 3) {
                // 3. 首重、续重减免
                BigDecimal firstWeightDiscount = queryPriceResVO.getFirstWeightDiscount();
                BigDecimal continuedWeightDiscount = queryPriceResVO.getContinuedWeightDiscount();

                if (ObjectUtil.isEmpty(firstWeightDiscount) || firstWeightDiscount.compareTo(BigDecimal.ZERO) <= 0) {
                    return;
                }

                // 计算续重部分减免金额
                BigDecimal continuedDiscount = BigDecimal.ZERO;
                if (ObjectUtil.isNotEmpty(continuedWeightDiscount) && continuedWeightDiscount.compareTo(BigDecimal.ZERO) > 0) {
                    if (weight != null && weight.compareTo(BigDecimal.ONE) > 0) {
                        BigDecimal continuedWeight = weight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                        continuedDiscount = continuedWeight.multiply(continuedWeightDiscount);
                    }
                }
                // 计算折扣后价格 = 最终金额 - 首重减免金额 - 续重减免金额，保留两位小数向上取整
                BigDecimal totalDiscount = firstWeightDiscount.add(continuedDiscount);
                BigDecimal discountedPrice = lastPrice.subtract(totalDiscount);
                if (discountedPrice.compareTo(BigDecimal.ZERO) < 0) {
                    discountedPrice = BigDecimal.ZERO;
                }
                discountedPrice = discountedPrice.setScale(2, RoundingMode.CEILING);
                queryPriceResVO.setLastPrice(discountedPrice);

                log.info("{}, 按照首重续重减免计算会员价格 - 首重:{}, 续重:{}, 结果 {}",
                        SecurityFrameworkUtils.getLoginUserId(), firstWeightDiscount, continuedDiscount, discountedPrice);
            }
        } catch (Exception e) {
            log.error("计算会员折扣价格异常: {}", e.getMessage() , e);
            // 发生异常时保留原价格
        }
    }

    /**
     * 对加价金额和首重金额应用折扣
     *
     * @param queryPriceResVO 价格对象
     * @param discountValue 折扣值（折扣率或减免金额）
     * @param discountType 折扣类型
     */
    private void applyDiscountToAdditionalFees(QueryPriceResVO queryPriceResVO, BigDecimal discountValue, Integer discountType) {
        applyDiscountToAdditionalFees(queryPriceResVO, discountValue, discountType, null);
    }

    /**
     * 对加价金额和首重金额应用折扣
     *
     * @param queryPriceResVO 价格对象
     * @param discountValue 折扣值（折扣率或减免金额）
     * @param discountType 折扣类型
     * @param continuedWeightDiscount 续重减免金额（仅折扣类型为3时使用）
     */
    private void applyDiscountToAdditionalFees(QueryPriceResVO queryPriceResVO, BigDecimal discountValue, Integer discountType, BigDecimal continuedWeightDiscount) {
        // 处理加价金额的折扣
        if(ObjectUtil.isNotEmpty(queryPriceResVO.getAddPrice())) {
            BigDecimal addPrice = queryPriceResVO.getAddPrice();
            BigDecimal discountedAddPrice = addPrice;

            if(discountType == 1) {
                // 百分比折扣
                discountedAddPrice = addPrice.multiply(discountValue);
            }

            if (discountType == 3 && ObjectUtil.isNotEmpty(continuedWeightDiscount)) {
                // 固定减免金额折扣（对加价金额应用首重减免）
                discountedAddPrice = addPrice.subtract(continuedWeightDiscount);
                if(discountedAddPrice.compareTo(BigDecimal.ZERO) < 0) {
                    discountedAddPrice = BigDecimal.ZERO;
                }
            }

            // 保留两位小数，向上取整
            discountedAddPrice = discountedAddPrice.setScale(2, RoundingMode.CEILING);

            queryPriceResVO.setAddPrice(discountedAddPrice);

            log.info("{}, 加价金额折扣: 原价 {}, 折后 {}", SecurityFrameworkUtils.getLoginUserId(), addPrice, discountedAddPrice);
        }

        // 处理首重金额的折扣
        if(ObjectUtil.isNotEmpty(queryPriceResVO.getFirstWeightPrice())) {
            BigDecimal firstWeightPrice = queryPriceResVO.getFirstWeightPrice();
            BigDecimal discountedFirstWeightPrice = firstWeightPrice;

            if(discountType == 1) {
                // 百分比折扣
                discountedFirstWeightPrice = firstWeightPrice.multiply(discountValue);
            } else if(discountType == 2 || discountType == 3) {
                // 固定减免金额折扣（对首重金额应用首重减免）
                discountedFirstWeightPrice = firstWeightPrice.subtract(discountValue);
                if(discountedFirstWeightPrice.compareTo(BigDecimal.ZERO) < 0) {
                    discountedFirstWeightPrice = BigDecimal.ZERO;
                }
            }

            // 保留两位小数，向上取整
            discountedFirstWeightPrice = discountedFirstWeightPrice.setScale(2, RoundingMode.CEILING);
            queryPriceResVO.setFirstWeightPrice(discountedFirstWeightPrice);
            log.info("{}, 首重金额折扣: 原价 {}, 折后 {}", SecurityFrameworkUtils.getLoginUserId(), firstWeightPrice, discountedFirstWeightPrice);
        }
    }

    /**
     * 计算会员折扣
     * @param commission 比列
     * @param profit  金额
     */
    private BigDecimal calVipPrice(String commission,BigDecimal profit){

        if ("0".equals(commission) || StrUtil.isEmpty(commission)) {
            return null;
        }
        // 按照百分比计算
        else if (commission.contains("%")) {
            BigDecimal multiplicand = new BigDecimal(commission.replaceAll("%", ""));
            BigDecimal amount = profit.multiply(multiplicand).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.CEILING);
            return amount;
        } else {
            // 按固定金额优惠
            BigDecimal amount = new BigDecimal(commission);
            if(amount.compareTo(new BigDecimal(80)) >= 0){
                return profit.multiply(amount).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.CEILING);
            }
            return profit.subtract(amount);
        }

    }

    /**
     * 会员折扣
     */
    private void memberDiscountCalculate(QueryPriceResVO queryPriceResVO,String vipDiscount){
        if(ObjectUtil.isNotEmpty(queryPriceResVO.getAddPrice())){
//            BigDecimal addPrice = queryPriceResVO.getAddPrice().multiply(vipDiscount).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.CEILING);
            BigDecimal addPrice = calVipPrice(vipDiscount,queryPriceResVO.getAddPrice());
            if (ObjectUtil.isNotEmpty(vipDiscount) && vipDiscount.endsWith("%")){
                queryPriceResVO.setAddPrice(addPrice);
            }
        }
        if(ObjectUtil.isNotEmpty(queryPriceResVO.getFirstWeightPrice())){
//            BigDecimal firstWeightPrice = queryPriceResVO.getFirstWeightPrice().multiply(vipDiscount).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.CEILING);
            BigDecimal firstWeightPrice = calVipPrice(vipDiscount,queryPriceResVO.getFirstWeightPrice());
            queryPriceResVO.setFirstWeightPrice(firstWeightPrice);
        }
    }



    private void systemPriceCalcV2(QueryPriceVO queryPriceVO, QueryPriceResVO queryPriceResVO) {
        Long systemPriceId = queryPriceResVO.getSystemPriceId();
        queryPriceResVO.setTenantPayAmount(queryPriceResVO.getPrice());

        // 0 不加价 1 渠道加价 2 官方折扣 -- 都不需要进行加价计算
        if (null != systemPriceId && systemPriceId > 2) {
//            ChannelPriceDO channelPrice = channelPriceService.getChannelPrice(systemPriceId);
            ChannelPriceDO channelPrice = channelPriceService.getChannelPriceByPriceIdOrVersion(systemPriceId,queryPriceResVO.getSysVersion());
            // 总部加价考虑抛比加价、首重加价、续重加价
//            BigDecimal weight = queryPriceVO.getWeight();
//            queryPriceVO.setWeight(calcWeightByVolume(queryPriceVO.getVloumLong(), queryPriceVO.getVloumWidth(), queryPriceVO.getVloumHeight(), channelPrice.getRation(), weight));
            BigDecimal weight = calcWeightByVolume(queryPriceVO.getVloumLong(), queryPriceVO.getVloumWidth(), queryPriceVO.getVloumHeight(), queryPriceResVO.getWeightRatio(), queryPriceVO.getWeight());
            Long priceType = channelPrice.getPriceType();

            BigDecimal systemPrice = queryPriceResVO.getPrice();
            BigDecimal addPrice = queryPriceResVO.getAddPrice();
            // 平台快递增加比例
            BigDecimal ration = channelPrice.getRation();
            BigDecimal firstPrice = ObjectUtil.isEmpty(channelPrice.getFirstPrice()) ? BigDecimal.ZERO : channelPrice.getFirstPrice();
            BigDecimal continuePrice = ObjectUtil.isEmpty(channelPrice.getContinuePrice()) ? BigDecimal.ZERO : channelPrice.getContinuePrice();

            if (OrderConstants.PRICE_TYPE_RATIO.equals(priceType+"")) {
                // 按比例加价 只取首重比例
                if (isCorrectNumber(ration)) {
                    systemPrice = systemPrice.add(systemPrice.multiply(ration.divide(new BigDecimal(100))));
                    queryPriceResVO.setTenantPayAmount(systemPrice);
                    if(ObjectUtil.isNotEmpty(addPrice)){
                        addPrice = addPrice.add(addPrice.multiply(ration.divide(new BigDecimal(100)))).setScale(2, RoundingMode.UP);
                        queryPriceResVO.setAddPrice(addPrice);
                    }
                    log.info("{}, 平台 按照比例加价 {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), ration, systemPrice);
                }
            } else if (OrderConstants.PRICE_TYPE_FIX.equals(priceType+"")) {
                // 按照固定金额加价
                systemPrice = systemPrice.add(firstPrice);
                if (weight.compareTo(BigDecimal.ONE) > 0) {
                    // 续重
                    BigDecimal continueWeight = weight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                    systemPrice = systemPrice.add(continueWeight.multiply(continuePrice));
                    if(ObjectUtil.isNotEmpty(addPrice)){
                        addPrice = addPrice.add(continuePrice).setScale(2, RoundingMode.UP);
                        queryPriceResVO.setAddPrice(addPrice);
                    }
                }
                queryPriceResVO.setTenantPayAmount(systemPrice);
                log.info("{}, 平台 按照固定金额加价 {} {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), firstPrice, continuePrice, systemPrice);
            }
        }
        // 总是向上舍入
        if(ObjectUtil.isNotEmpty(queryPriceResVO.getTenantPayAmount())){
            queryPriceResVO.setTenantPayAmount(queryPriceResVO.getTenantPayAmount().setScale(2, RoundingMode.CEILING));
        }
    }

    private void tenantPriceCalcV2(QueryPriceVO queryPriceVO, QueryPriceResVO queryPriceResVO,Long userId,String userDiscounts,Boolean isNewOrder) {
        Long tenantPriceId = queryPriceResVO.getTenantPriceId();
        queryPriceResVO.setLastPrice(queryPriceResVO.getTenantPayAmount());

        BigDecimal weight = calcWeightByVolume(queryPriceVO.getVloumLong(), queryPriceVO.getVloumWidth(), queryPriceVO.getVloumHeight(), queryPriceResVO.getWeightRatio(), queryPriceVO.getWeight());
        // 0 不加价 1 渠道加价 2 官方折扣 -- 都不需要进行加价计算
        if (null != tenantPriceId && tenantPriceId > 2) {
//            ChannelPriceDO channelPrice = channelPriceService.getChannelPrice(tenantPriceId);
            ChannelPriceDO channelPrice = channelPriceService.getChannelPriceByPriceIdOrVersion(tenantPriceId,queryPriceResVO.getTenantVersion());
            // 代理加价考虑抛比、vip折扣、首重加价、续重加价
//            BigDecimal weight = queryPriceVO.getWeight();
//            queryPriceVO.setWeight(calcWeightByVolume(queryPriceVO.getVloumLong(), queryPriceVO.getVloumWidth(), queryPriceVO.getVloumHeight(), channelPrice.getRation(), queryPriceVO.getWeight()));

            Long priceType = channelPrice.getPriceType();

            BigDecimal lastPrice = queryPriceResVO.getTenantPayAmount();
            BigDecimal addPrice = queryPriceResVO.getAddPrice();
            // 平台快递增加比例
            BigDecimal ration = channelPrice.getRation();
            BigDecimal firstPrice = channelPrice.getFirstPrice();
            BigDecimal continuePrice = channelPrice.getContinuePrice();

            if (OrderConstants.PRICE_TYPE_RATIO.equals(priceType+"")) {
                // 按比例加价 只取首重比例
                if (isCorrectNumber(ration)) {
                    lastPrice = lastPrice.add(lastPrice.multiply(ration.divide(new BigDecimal(100))));
                    queryPriceResVO.setLastPrice(lastPrice);
                    queryPriceResVO.setUserPrice(lastPrice);
                    if(ObjectUtil.isNotEmpty(addPrice)){
                        addPrice = addPrice.add(addPrice.multiply(ration.divide(new BigDecimal(100)))).setScale(2, RoundingMode.UP);
                        queryPriceResVO.setAddPrice(addPrice);
                    }
                    log.info("{}, 代理 按照比例加价 {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), ration, lastPrice);
                }
            } else if (OrderConstants.PRICE_TYPE_FIX.equals(priceType+"")) {
                // 按照固定金额加价
                lastPrice = lastPrice.add(firstPrice);

                if (weight.compareTo(BigDecimal.ONE) > 0) {
                    // 续重
                    BigDecimal continueWeight = weight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                    lastPrice = lastPrice.add(continueWeight.multiply(continuePrice));
                    if(ObjectUtil.isNotEmpty(addPrice)){
                        addPrice = addPrice.add(continuePrice).setScale(2, RoundingMode.UP);
                        queryPriceResVO.setAddPrice(addPrice);
                    }
                }
                queryPriceResVO.setLastPrice(lastPrice);
                queryPriceResVO.setUserPrice(lastPrice);
                log.info("{}, 代理 按照固定金额加价 {} {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), ration, lastPrice);
            }
        }


        // 总是向上舍入  (老订单数据不在此处向上取整)
        if(ObjectUtil.isNotEmpty(queryPriceResVO.getLastPrice()) && isNewOrder){
            queryPriceResVO.setLastPrice(queryPriceResVO.getLastPrice().setScale(2, RoundingMode.CEILING));
        }

        // 回调老的计算折扣方法
        if (ObjectUtil.isEmpty(queryPriceResVO.getDiscountType())) {
            // 计费会员优惠价格 vip 折扣 取值在[1-99]
            String vipDiscount = queryPriceResVO.getVipDiscount();
            if (isCorrectDiscount(vipDiscount)) {
//            BigDecimal vipPrice = queryPriceResVO.getLastPrice().multiply(vipDiscount).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.CEILING);
                // 计算会员则扣
                BigDecimal vipPrice = calVipPrice(vipDiscount,queryPriceResVO.getLastPrice());
                queryPriceResVO.setVipPrice(vipPrice);
                log.info("{}, 按照比例计算会员 金额加价 {}, 结果 {}", SecurityFrameworkUtils.getLoginUserId(), vipDiscount, vipPrice);
            } else {
                queryPriceResVO.setVipPrice(queryPriceResVO.getLastPrice());
            }
            // 如果是会员 将价格塞入最终的金额中
            if(ObjectUtil.isNotEmpty(userId) && ObjectUtil.isEmpty(userDiscounts)){
                CacheUserDTO userInfo = cacheService.getUserInfo(userId);
                if (null != userInfo && null != userInfo.getUserLevel() && userInfo.getUserLevel() != 1) {
                    queryPriceResVO.setLastPrice(queryPriceResVO.getVipPrice());
                }
            } else if (ObjectUtil.isNotEmpty(userId) && ObjectUtil.isNotEmpty(userDiscounts)) {
//            BigDecimal vipPrice = queryPriceResVO.getLastPrice().multiply(userDiscounts).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.CEILING);
                // 计算会员则扣
                BigDecimal vipPrice = calVipPrice(userDiscounts,queryPriceResVO.getLastPrice());
                queryPriceResVO.setLastPrice(vipPrice);
            }
        } else {
            calcCallbackMemberDiscountPrice(queryPriceResVO, weight);
        }

        if(ObjectUtil.isNotEmpty(queryPriceResVO.getVipPrice())){
            queryPriceResVO.setVipPrice(queryPriceResVO.getVipPrice().setScale(2, RoundingMode.CEILING));
        }

        if(ObjectUtil.isNotEmpty(queryPriceResVO.getUserPrice())){
            queryPriceResVO.setUserPrice(queryPriceResVO.getUserPrice().setScale(2, RoundingMode.CEILING));
        }

        // 总是向上舍入  (新订单数据不在此处向上取整)
        if(ObjectUtil.isNotEmpty(queryPriceResVO.getLastPrice()) && !isNewOrder){
            queryPriceResVO.setLastPrice(queryPriceResVO.getLastPrice().setScale(2, RoundingMode.CEILING));
        }

        // 如果成本价小于提完之后的价格(lastPrice)则用户支付，成本价格。
        if (ObjectUtil.isNotEmpty(queryPriceResVO.getLastPrice()) && ObjectUtil.isNotEmpty(queryPriceResVO.getPrice())
                && queryPriceResVO.getPrice().compareTo(queryPriceResVO.getLastPrice()) > 0 && ObjectUtil.isNotEmpty(queryPriceResVO.getExtraCharge())) {
            try {
                // 如果最终价格大于成本价，则按配置加价
                priceDifferenceCallbackMarkup(queryPriceResVO, weight, queryPriceResVO.getPrice());
            } catch (Exception e) {
                log.error("{}, 代理 回调最终金额小于第三方金额按照加价配置额外加价失败, 异常信息 {}", SecurityFrameworkUtils.getLoginUserId(), e.getMessage());
            }
        }

        // 如果成本价小于提完之后的价格(lastPrice)则用户支付，成本价格。
        if(ObjectUtil.isNotEmpty(queryPriceResVO.getLastPrice()) && ObjectUtil.isNotEmpty(queryPriceResVO.getPrice())
                && queryPriceResVO.getPrice().compareTo(queryPriceResVO.getLastPrice()) > 0){
            queryPriceResVO.setLastPrice(queryPriceResVO.getPrice());
            queryPriceResVO.setVipPrice(queryPriceResVO.getPrice());
        }
        if(ObjectUtil.isNotEmpty(queryPriceResVO.getDefPrice())){
            BigDecimal defPrice = new BigDecimal(queryPriceResVO.getDefPrice());
            if(defPrice.compareTo(queryPriceResVO.getLastPrice()) <= 0){
                defPrice = queryPriceResVO.getLastPrice().add(new BigDecimal(2)).setScale(2, RoundingMode.CEILING);
            }
            queryPriceResVO.setDefPrice(defPrice.toString());
        }

        if(ObjectUtil.isEmpty(queryPriceResVO.getDefPrice())){
            BigDecimal defPrice = queryPriceResVO.getLastPrice().add(new BigDecimal(2)).setScale(2, RoundingMode.CEILING);
            queryPriceResVO.setDefPrice(defPrice.toString());
        }
    }

    /**
     * 价格差异按配置加价(如果最终价格小于第三方价格)
     */
    private void priceDifferenceCallbackMarkup(QueryPriceResVO queryPriceResVO, BigDecimal weight, BigDecimal lastPrice) {
        String extraCharge = queryPriceResVO.getExtraCharge();

        if (ObjectUtil.isEmpty(extraCharge)) {
            return;
        }


        // 平台快递增加比例
        BigDecimal ration = null;
        BigDecimal firstPrice = null;
        BigDecimal continuePrice = null;

        if (extraCharge.contains("%")) {
            ration = new BigDecimal(extraCharge.replaceAll("%", ""));
            if (ObjectUtil.isEmpty(ration)) {
                return;
            }
            // 按比例加价 只取首重比例
            if (isCorrectNumber(ration)) {
                lastPrice = lastPrice.add(lastPrice.multiply(ration.divide(new BigDecimal(100))));
                queryPriceResVO.setLastPrice(lastPrice);
            }
        }

        if (extraCharge.contains(",")) {
            String[] charges = extraCharge.split(",");
            firstPrice = new BigDecimal(charges[0]);
            continuePrice = new BigDecimal(charges[1]);

            if (ObjectUtil.isEmpty(firstPrice) || ObjectUtil.isEmpty(continuePrice)) {
                return;
            }
            // 按照固定金额加价
            lastPrice = lastPrice.add(firstPrice);
            // 续重价格加价
            if (weight.compareTo(BigDecimal.ZERO) > 0) {
                // 续重
                BigDecimal continueWeight = weight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                lastPrice = lastPrice.add(continueWeight.multiply(continuePrice));
            }
            queryPriceResVO.setLastPrice(lastPrice);
        }
    }

    /**
     * 是否正确的会员则扣
     * @param commission
     * @return
     */
    private boolean isCorrectDiscount(String commission) {
        try {
            if ("0".equals(commission) || StrUtil.isEmpty(commission)) {
                return false;
            }
            // 按照百分比计算
            else if (commission.contains("%")) {
                BigDecimal multiplicand = new BigDecimal(commission.replaceAll("%", ""));
                return multiplicand.compareTo(BigDecimal.ZERO) > 0 && multiplicand.compareTo(new BigDecimal("100")) < 0;
            } else {
                // 反固定金额
                BigDecimal amount = new BigDecimal(commission);
                // 直推分佣 间推分佣 后台设置多少钱 就反多少，不需要考虑利润 所以把此出注释
                if (amount.compareTo(BigDecimal.ZERO) < 0) {
                    return false;
                }
                return true;
            }
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isCorrectNumber(BigDecimal number) {
        return null != number && number.compareTo(BigDecimal.ZERO) > 0 && number.compareTo(new BigDecimal("100")) < 0;
    }

    /**
     * 根据体积计算重量
     *
     * @param volumeLong   长
     * @param volumeWidth  宽
     * @param volumeHeight 高
     * @param lightGood    抛比
     * @param weight       重量
     */
    public BigDecimal calcWeightByVolume(BigDecimal volumeLong, BigDecimal volumeWidth, BigDecimal volumeHeight, BigDecimal lightGood, BigDecimal weight) {
        if (null != volumeHeight && null != volumeLong && null != volumeWidth && null != lightGood) {
            BigDecimal volumeWeight = volumeHeight.multiply(volumeLong).multiply(volumeWidth).divide(lightGood, 2, RoundingMode.HALF_UP).setScale(0, RoundingMode.UP);
            return weight.compareTo(volumeWeight) < 0 ? volumeWeight : weight;
        } else {
            return weight;
        }
    }


    /**
     * 老渠道
     * @return
     */
    /**
     * 计算回调的时候 订单总额 20240821版本新加 [要根据快照信息加价,查询渠道新表]
     *
     * @param orderFeeList
     * @param weight
     * @return
     */
    public BigDecimal calcCallbackFeeNewV2(List<OrderFeeBaseVO> orderFeeList, UpdateFeesRepVO updateFeesRepVO, OrderDO orderDO, BigDecimal weight) {
        // 根据orderFeeList的amount计算出总价
        if (CollUtil.isEmpty(orderFeeList)) {
            return null;
        }
        // 成本价
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (OrderFeeBaseVO orderFeeBaseVO : orderFeeList) {
            totalAmount = totalAmount.add(orderFeeBaseVO.getAmount());
        }



//        updateOrder.setReallyOriginalPrice(totalAmount);

        // 查询定价方案
        Long expressProductId = orderDO.getExpressProductId();
        TenantChannelConfigVO expressProduct = tenantChannelService.getTenantChannelConfig(expressProductId);



        if (expressProduct == null) {
            return null;
        }

        // 费用计算
        QueryPriceResVO queryPriceResVO = new QueryPriceResVO();
        queryPriceResVO.setSystemPriceId(expressProduct.getSystemPriceId());
        queryPriceResVO.setTenantPriceId(expressProduct.getTenantPriceId());
        queryPriceResVO.setVipDiscount(expressProduct.getVipDiscount());
        queryPriceResVO.setPrice(totalAmount);
        queryPriceResVO.setWeightRatio(expressProduct.getWeightRatio());

        QueryPriceVO queryPriceVO = new QueryPriceVO();
        queryPriceVO.setWeight(weight);

        String userDiscounts = null;
        Boolean isNewOrder = false;
        // 查询快照信息
        if(ObjectUtil.isNotEmpty(orderDO) && ObjectUtil.isNotEmpty(orderDO.getId())){
            FeeSnapshotDO orderSnapshotDO = feeSnapshotService.getDataByOrderId(orderDO.getId());
            if(ObjectUtil.isNotEmpty(orderSnapshotDO)){
                isNewOrder = true;
                queryPriceResVO.setSysVersion(orderSnapshotDO.getSystemRaiseVersion());
                queryPriceResVO.setTenantVersion(orderSnapshotDO.getTenantRaiseVersion());
                queryPriceResVO.setDiscountType(orderSnapshotDO.getDiscountType());
                if(ObjectUtil.isNotEmpty(orderSnapshotDO.getDiscountType())){
                    queryPriceResVO.setUserDiscounts(orderSnapshotDO.getVipDiscount());
                } else {
                    // 根据时间判断是否走老用户逻辑
                    if(ObjectUtil.isNotEmpty(orderSnapshotDO.getCreateTime()) &&
                            orderSnapshotDO.getCreateTime().compareTo(
                                    java.time.LocalDateTime.parse("2025-04-09T22:36:37")) > 0) {
                        // 新用户逻辑
                        queryPriceResVO.setDiscountType("1");
                        queryPriceResVO.setUserDiscounts(null);
                    } else {
                        // 老用户逻辑
                        queryPriceResVO.setUserDiscounts(orderSnapshotDO.getVipDiscount());
                    }
                }
                queryPriceResVO.setTenantPriceId(ObjectUtil.isNotEmpty(orderSnapshotDO.getTenantPriceId()) ?
                        orderSnapshotDO.getTenantPriceId() : expressProduct.getTenantPriceId());
                queryPriceResVO.setSystemPriceId(ObjectUtil.isNotEmpty(orderSnapshotDO.getSystemPriceId()) ?
                        orderSnapshotDO.getSystemPriceId() : expressProduct.getSystemPriceId());

                queryPriceResVO.setDiscountType(orderSnapshotDO.getDiscountType());
                queryPriceResVO.setContinuedWeightDiscount(orderSnapshotDO.getContinuedWeightDiscount());
                queryPriceResVO.setFirstWeightDiscount(orderSnapshotDO.getFirstWeightDiscount());
                queryPriceResVO.setExtraCharge(orderSnapshotDO.getExtraCharge());

                userDiscounts = ObjectUtil.isNotEmpty(orderSnapshotDO.getVipDiscount()) ?  orderSnapshotDO.getVipDiscount() : "100%";

                // 模式5下会员折扣率特殊取值
//                userDiscounts = handleModeFiveVipDiscount(weight, orderSnapshotDO, queryPriceResVO, userDiscounts);

            }
        }

        // 系统加价
        systemPriceCalcV2(queryPriceVO, queryPriceResVO);

        // 代理加价
        tenantPriceCalcV2(queryPriceVO, queryPriceResVO, orderDO.getUserId(),userDiscounts,true);

//        updateOrder.setReallyLastPrice(queryPriceResVO.getLastPrice());
//        updateOrder.setReallyDiscountPrice(totalAmount);
//        updateOrder.setTenantPayAmount(queryPriceResVO.getTenantPayAmount());


        updateFeesRepVO.setPrice(totalAmount);
        updateFeesRepVO.setLastPrice(queryPriceResVO.getLastPrice());
        updateFeesRepVO.setTenantPayAmount(queryPriceResVO.getTenantPayAmount());
//        updateFeesRepVO.setCompensationAmount(updateOrder.getCompensationAmount());
//        updateFeesRepVO.setCompensatingState(updateOrder.getCompensatingState());

        return queryPriceResVO.getLastPrice();
    }


    /**
     * 模式五下会员折扣率特殊取值
     * @param weight
     * @param orderSnapshotDO
     * @param queryPriceResVO
     * @param userDiscounts
     * @return
     */
    private String handleModeFiveVipDiscount(BigDecimal weight, FeeSnapshotDO orderSnapshotDO, QueryPriceResVO queryPriceResVO, String userDiscounts) {
        if (!StringUtil.isBlank(orderSnapshotDO.getModeFiveVipDiscount())) {
            String modeFiveVipDiscount = orderSnapshotDO.getModeFiveVipDiscount();

            // 如果是首续重会员折扣，那么这里还要根据第三方推送的重量算出实际需要优惠的金额
            if (modeFiveVipDiscount.contains(",")) {
                // 百分比(首重,续重)优惠
                String[] split = modeFiveVipDiscount.split(",");
                String vipDiscount = "0";
                if (!split[0].isEmpty() && !split[1].isEmpty()) {

                    BigDecimal firstPrice = new BigDecimal(split[0]);
                    BigDecimal multiply = BigDecimal.ZERO;
                    if (weight.compareTo(BigDecimal.ZERO) > 0) {
                        // 续重
                        BigDecimal continueWeight = weight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                        BigDecimal continuePrice = new BigDecimal(split[1]);
                        multiply = continueWeight.multiply(continuePrice);
                    }

                    // 计算出百分比优惠的金额 转成 固定金额 的算法。这样就不要动之前的逻辑
                    vipDiscount = String.valueOf(firstPrice.add(multiply));
                }

                queryPriceResVO.setVipDiscount(vipDiscount);
                queryPriceResVO.setUserDiscounts(vipDiscount);
                userDiscounts = vipDiscount;
            } else {
                queryPriceResVO.setVipDiscount(orderSnapshotDO.getModeFiveVipDiscount());
                queryPriceResVO.setUserDiscounts(orderSnapshotDO.getModeFiveVipDiscount());
                userDiscounts = orderSnapshotDO.getModeFiveVipDiscount();
            }
        }
        return userDiscounts;
    }


    /**
     * 计算回调的时候 订单总额 20240706版本新加 [要根据快照信息加价]
     *
     * @param orderFeeList
     * @param weight
     * @return
     */
    public BigDecimal calcCallbackFeeV2(List<OrderFeeBaseVO> orderFeeList, UpdateFeesRepVO updateFeesRepVO, OrderDO orderDO, BigDecimal weight) {
        // 根据orderFeeList的amount计算出总价
        if (CollUtil.isEmpty(orderFeeList)) {
            return null;
        }

        BigDecimal totalAmount = BigDecimal.ZERO;
        for (OrderFeeBaseVO orderFeeBaseVO : orderFeeList) {
            totalAmount = totalAmount.add(orderFeeBaseVO.getAmount());
        }

//        updateOrder.setReallyOriginalPrice(totalAmount);

        // 查询定价方案
        Long expressProductId = orderDO.getExpressProductId();
        ExpressProductDO expressProduct = null;


        if (null != expressProductId) {
            expressProduct = expressProductService.getExpressProduct(expressProductId);
        } else {
            // 老数据没有expressProductId改用code查询
            String deliveryBusiness = orderDO.getDeliveryBusiness();
            expressProduct = expressProductService.getExpressProductByCode(deliveryBusiness, orderDO.getTenantId());
        }

        if (expressProduct == null) {
            return null;
        }

        // 费用计算
        QueryPriceResVO queryPriceResVO = new QueryPriceResVO();
        queryPriceResVO.setSystemPriceId(expressProduct.getSystemPriceId());
        queryPriceResVO.setTenantPriceId(expressProduct.getTenantPriceId());
        queryPriceResVO.setVipDiscount(expressProduct.getVipDiscount());
        queryPriceResVO.setPrice(totalAmount);
        queryPriceResVO.setWeightRatio(expressProduct.getWeightRatio());

        QueryPriceVO queryPriceVO = new QueryPriceVO();
        queryPriceVO.setWeight(weight);

        String userDiscounts = null;
        Boolean isNewOrder = false;
        // 查询快照信息
        if(ObjectUtil.isNotEmpty(orderDO) && ObjectUtil.isNotEmpty(orderDO.getId())){
            FeeSnapshotDO orderSnapshotDO = feeSnapshotService.getDataByOrderId(orderDO.getId());
            if(ObjectUtil.isNotEmpty(orderSnapshotDO)){
                isNewOrder = true;
                queryPriceResVO.setSysVersion(orderSnapshotDO.getSystemRaiseVersion());
                queryPriceResVO.setTenantVersion(orderSnapshotDO.getTenantRaiseVersion());
                queryPriceResVO.setUserDiscounts(ObjectUtil.isNotEmpty(orderSnapshotDO.getVipDiscount()) ?
                        orderSnapshotDO.getVipDiscount() : expressProduct.getVipDiscount());
                queryPriceResVO.setTenantPriceId(ObjectUtil.isNotEmpty(orderSnapshotDO.getTenantPriceId()) ?
                        orderSnapshotDO.getTenantPriceId() : expressProduct.getTenantPriceId());
                queryPriceResVO.setSystemPriceId(ObjectUtil.isNotEmpty(orderSnapshotDO.getSystemPriceId()) ?
                        orderSnapshotDO.getSystemPriceId() : expressProduct.getSystemPriceId());


                userDiscounts = ObjectUtil.isNotEmpty(orderSnapshotDO.getVipDiscount()) ?  orderSnapshotDO.getVipDiscount() : "100%";

                // 模式5下会员折扣率特殊取值
                userDiscounts = handleModeFiveVipDiscount(weight, orderSnapshotDO, queryPriceResVO, userDiscounts);
            }
        }

        // 系统加价
        systemPriceCalcV2(queryPriceVO, queryPriceResVO);

        // 代理加价
        tenantPriceCalcV2(queryPriceVO, queryPriceResVO, orderDO.getUserId(),userDiscounts,isNewOrder);

//        updateOrder.setReallyLastPrice(queryPriceResVO.getLastPrice());
//        updateOrder.setReallyDiscountPrice(totalAmount);
//        updateOrder.setTenantPayAmount(queryPriceResVO.getTenantPayAmount());

        updateFeesRepVO.setPrice(totalAmount);
        updateFeesRepVO.setLastPrice(queryPriceResVO.getLastPrice());
        updateFeesRepVO.setTenantPayAmount(queryPriceResVO.getTenantPayAmount());

        return queryPriceResVO.getLastPrice();
    }

    /**
     * 计算费用明细
     *
     * @param orderFeeList
     * @return
     */
    public BigDecimal calcFeeInfo(OrderFeeBaseVO orderFeeList, OrderDO orderDO, BigDecimal weight) {
        if (ObjectUtil.isEmpty(orderFeeList)) {
            return null;
        }

        BigDecimal totalAmount = BigDecimal.ZERO;

        totalAmount = orderFeeList.getAmount();

        // 查询定价方案
        Long expressProductId = orderDO.getExpressProductId();
        ExpressProductDO expressProduct = null;

        if (null != expressProductId) {
            expressProduct = expressProductService.getExpressProduct(expressProductId);
        } else {
            // 老数据没有expressProductId改用code查询
            String deliveryBusiness = orderDO.getDeliveryBusiness();
            expressProduct = expressProductService.getExpressProductByCode(deliveryBusiness, orderDO.getTenantId());
        }

        if (expressProduct == null) {
            return null;
        }
        // 费用计算
        QueryPriceResVO queryPriceResVO = new QueryPriceResVO();
        queryPriceResVO.setSystemPriceId(expressProduct.getSystemPriceId());
        queryPriceResVO.setTenantPriceId(expressProduct.getTenantPriceId());
        queryPriceResVO.setVipDiscount(expressProduct.getVipDiscount());
        queryPriceResVO.setPrice(totalAmount);
        queryPriceResVO.setWeightRatio(expressProduct.getWeightRatio());

        QueryPriceVO queryPriceVO = new QueryPriceVO();
        queryPriceVO.setWeight(weight);

        // 系统加价
        systemPriceCalc(queryPriceVO, queryPriceResVO);

        // 代理加价
        tenantPriceCalc(queryPriceVO, queryPriceResVO, orderDO.getUserId());


        return queryPriceResVO.getLastPrice();
    }


    /**
     * 计算费用明细
     *
     * @param orderFeeList
     * @return
     */
    public BigDecimal calcFeeInfoV2(OrderFeeBaseVO orderFeeList, OrderDO orderDO, BigDecimal weight) {
        if (ObjectUtil.isEmpty(orderFeeList)) {
            return null;
        }

        BigDecimal totalAmount = BigDecimal.ZERO;

        totalAmount = orderFeeList.getAmount();

        // 查询定价方案
        Long expressProductId = orderDO.getExpressProductId();
        ExpressProductDO expressProduct = null;

        if (null != expressProductId) {
            expressProduct = expressProductService.getExpressProduct(expressProductId);
        } else {
            // 老数据没有expressProductId改用code查询
            String deliveryBusiness = orderDO.getDeliveryBusiness();
            expressProduct = expressProductService.getExpressProductByCode(deliveryBusiness, orderDO.getTenantId());
        }

        if (expressProduct == null) {
            return null;
        }
        // 费用计算
        QueryPriceResVO queryPriceResVO = new QueryPriceResVO();
        queryPriceResVO.setSystemPriceId(expressProduct.getSystemPriceId());
        queryPriceResVO.setTenantPriceId(expressProduct.getTenantPriceId());
        queryPriceResVO.setVipDiscount(expressProduct.getVipDiscount());
        queryPriceResVO.setPrice(totalAmount);
        queryPriceResVO.setWeightRatio(expressProduct.getWeightRatio());

        String userDiscounts = null;
        Boolean isNewOrder = false;
        // 查询快照信息
        if(ObjectUtil.isNotEmpty(orderDO) && ObjectUtil.isNotEmpty(orderDO.getId())){
            FeeSnapshotDO orderSnapshotDO = feeSnapshotService.getDataByOrderId(orderDO.getId());
            if(ObjectUtil.isNotEmpty(orderSnapshotDO)){
                isNewOrder = true;
                queryPriceResVO.setSysVersion(orderSnapshotDO.getSystemRaiseVersion());
                queryPriceResVO.setTenantVersion(orderSnapshotDO.getTenantRaiseVersion());
                queryPriceResVO.setUserDiscounts(ObjectUtil.isNotEmpty(orderSnapshotDO.getVipDiscount()) ?
                        orderSnapshotDO.getVipDiscount() : expressProduct.getVipDiscount());
                queryPriceResVO.setTenantPriceId(ObjectUtil.isNotEmpty(orderSnapshotDO.getTenantPriceId()) ?
                        orderSnapshotDO.getTenantPriceId() : expressProduct.getTenantPriceId());
                queryPriceResVO.setSystemPriceId(ObjectUtil.isNotEmpty(orderSnapshotDO.getSystemPriceId()) ?
                        orderSnapshotDO.getSystemPriceId() : expressProduct.getSystemPriceId());

                queryPriceResVO.setDiscountType(orderSnapshotDO.getDiscountType());
                queryPriceResVO.setContinuedWeightDiscount(orderSnapshotDO.getContinuedWeightDiscount());
                queryPriceResVO.setFirstWeightDiscount(orderSnapshotDO.getFirstWeightDiscount());
                queryPriceResVO.setExtraCharge(orderSnapshotDO.getExtraCharge());

                userDiscounts = ObjectUtil.isNotEmpty(orderSnapshotDO.getVipDiscount()) ?  orderSnapshotDO.getVipDiscount() : "100%";

                // 模式5下会员折扣率特殊取值
                userDiscounts = handleModeFiveVipDiscount(weight, orderSnapshotDO, queryPriceResVO, userDiscounts);
            }
        }

        QueryPriceVO queryPriceVO = new QueryPriceVO();
        queryPriceVO.setWeight(weight);

        // 系统加价
        systemPriceCalcV2(queryPriceVO, queryPriceResVO);

        // 代理加价
        tenantPriceCalcV2(queryPriceVO, queryPriceResVO, orderDO.getUserId(),userDiscounts,isNewOrder);


        return queryPriceResVO.getLastPrice();
    }

    /**
     * 计算费用明细
     *
     * @param orderFeeList
     * @return
     */
    public BigDecimal calcFeeInfoNewV2(OrderFeeBaseVO orderFeeList, OrderDO orderDO, BigDecimal weight) {
        if (ObjectUtil.isEmpty(orderFeeList)) {
            return null;
        }

        BigDecimal totalAmount = BigDecimal.ZERO;

        totalAmount = orderFeeList.getAmount();

        // 查询定价方案
        Long expressProductId = orderDO.getExpressProductId();
        TenantChannelConfigVO expressProduct = tenantChannelService.getTenantChannelConfig(expressProductId);



        if (expressProduct == null) {
            return null;
        }
        if (expressProduct == null) {
            return null;
        }
        // 费用计算
        QueryPriceResVO queryPriceResVO = new QueryPriceResVO();
        queryPriceResVO.setSystemPriceId(expressProduct.getSystemPriceId());
        queryPriceResVO.setTenantPriceId(expressProduct.getTenantPriceId());
        queryPriceResVO.setVipDiscount(expressProduct.getVipDiscount());
        queryPriceResVO.setPrice(totalAmount);
        queryPriceResVO.setWeightRatio(expressProduct.getWeightRatio());

        String userDiscounts = null;
        Boolean isNewOrder = false;
        // 查询快照信息
        if(ObjectUtil.isNotEmpty(orderDO) && ObjectUtil.isNotEmpty(orderDO.getId())){
            FeeSnapshotDO orderSnapshotDO = feeSnapshotService.getDataByOrderId(orderDO.getId());
            if(ObjectUtil.isNotEmpty(orderSnapshotDO)){
                isNewOrder = true;
                queryPriceResVO.setSysVersion(orderSnapshotDO.getSystemRaiseVersion());
                queryPriceResVO.setTenantVersion(orderSnapshotDO.getTenantRaiseVersion());
                queryPriceResVO.setDiscountType(orderSnapshotDO.getDiscountType());
                if(ObjectUtil.isNotEmpty(orderSnapshotDO.getDiscountType())){
                    queryPriceResVO.setUserDiscounts(orderSnapshotDO.getVipDiscount());
                } else {
                    // 根据时间判断是否走老用户逻辑
                    if(ObjectUtil.isNotEmpty(orderSnapshotDO.getCreateTime()) &&
                            orderSnapshotDO.getCreateTime().compareTo(
                                    java.time.LocalDateTime.parse("2025-04-09T22:36:37")) > 0) {
                        // 新用户逻辑
                        queryPriceResVO.setDiscountType("1");
                        queryPriceResVO.setUserDiscounts(null);
                    } else {
                        // 老用户逻辑
                        queryPriceResVO.setUserDiscounts(orderSnapshotDO.getVipDiscount());
                    }
                }

                queryPriceResVO.setTenantPriceId(ObjectUtil.isNotEmpty(orderSnapshotDO.getTenantPriceId()) ?
                        orderSnapshotDO.getTenantPriceId() : expressProduct.getTenantPriceId());
                queryPriceResVO.setSystemPriceId(ObjectUtil.isNotEmpty(orderSnapshotDO.getSystemPriceId()) ?
                        orderSnapshotDO.getSystemPriceId() : expressProduct.getSystemPriceId());

                queryPriceResVO.setDiscountType(orderSnapshotDO.getDiscountType());
                queryPriceResVO.setContinuedWeightDiscount(orderSnapshotDO.getContinuedWeightDiscount());
                queryPriceResVO.setFirstWeightDiscount(orderSnapshotDO.getFirstWeightDiscount());
                queryPriceResVO.setExtraCharge(orderSnapshotDO.getExtraCharge());

                userDiscounts = ObjectUtil.isNotEmpty(orderSnapshotDO.getVipDiscount()) ?  orderSnapshotDO.getVipDiscount() : "100%";

                // 模式5下会员折扣率特殊取值
                userDiscounts = handleModeFiveVipDiscount(weight, orderSnapshotDO, queryPriceResVO, userDiscounts);
            }
        }

        QueryPriceVO queryPriceVO = new QueryPriceVO();
        queryPriceVO.setWeight(weight);

        // 系统加价
        systemPriceCalcV2(queryPriceVO, queryPriceResVO);

        // 代理加价
        tenantPriceCalcV2(queryPriceVO, queryPriceResVO, orderDO.getUserId(),userDiscounts,isNewOrder);


        return queryPriceResVO.getLastPrice();
    }
}
