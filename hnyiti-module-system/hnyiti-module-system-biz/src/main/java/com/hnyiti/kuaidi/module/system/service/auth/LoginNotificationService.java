package com.hnyiti.kuaidi.module.system.service.auth;

import cn.hutool.core.util.StrUtil;
import com.hnyiti.kuaidi.framework.ip.core.Area;
import com.hnyiti.kuaidi.framework.ip.core.utils.AreaUtils;
import com.hnyiti.kuaidi.framework.ip.core.utils.IPUtils;
import com.hnyiti.kuaidi.module.system.enums.logger.LoginLogTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录通知服务
 * 负责异步发送登录通知到企业微信
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LoginNotificationService {

    @Resource
    private RestTemplate restTemplate;

    /**
     * 企业微信机器人 Webhook URL
     */
    private static final String WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5c8432d4-d951-42f8-8806-fddfc5171a61";

    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 异步发送登录通知到企业微信
     *
     * @param username 用户名/手机号
     * @param loginType 登录方式
     * @param userIp 用户IP
     */
    @Async
    public void sendLoginNotification(String username, LoginLogTypeEnum loginType, String userIp) {
        try {
            // 构建登录通知消息
            String message = buildLoginMessage(username, loginType, userIp);

            // 发送到企业微信
            sendToWeChat(message);

            log.info("[sendLoginNotification][登录通知发送成功] username:{} loginType:{} ip:{}",
                    username, loginType.name(), userIp);

        } catch (Exception e) {
            // 异常不影响登录流程，只记录日志
            log.error("[sendLoginNotification][登录通知发送失败] username:{} loginType:{} ip:{}",
                    username, loginType.name(), userIp, e);
        }
    }

    /**
     * 构建登录消息内容
     *
     * @param username 用户名
     * @param loginType 登录类型
     * @param userIp 用户IP
     * @return 消息内容
     */
    private String buildLoginMessage(String username, LoginLogTypeEnum loginType, String userIp) {
        // 获取IP对应的地区信息
        String location = getLocationByIp(userIp);

        // 获取登录方式描述和对应的图标
        String loginMethod = getLoginMethodDescription(loginType);
        String loginIcon = getLoginMethodIcon(loginType);

        // 获取当前时间
        String loginTime = LocalDateTime.now().format(FORMATTER);

        // 获取地区图标
        String locationIcon = getLocationIcon(location);

        // 判断是否为可疑登录
        boolean isSuspicious = isSuspiciousLogin(userIp, location);
        String alertLevel = isSuspicious ? "⚠️ **高风险登录**" : "✅ **正常登录**";

        // 构建美化的消息内容
        StringBuilder message = new StringBuilder();
        message.append("# 🔐 系统登录通知\n\n");
        message.append("---\n\n");

        // 风险等级提示
        message.append("## ").append(alertLevel).append("\n\n");

        // 用户信息
        message.append("### 👤 用户信息\n");
        message.append("- **账号：** `").append(username).append("`\n\n");

        // 访问信息
        message.append("### 🌐 访问信息\n");
        message.append("- **IP地址：** `").append(userIp).append("`\n");
        message.append("- **地区：** ").append(locationIcon).append(" ").append(location).append("\n\n");

        // 登录详情
        message.append("### 🔑 登录详情\n");
        message.append("- **登录方式：** ").append(loginIcon).append(" ").append(loginMethod).append("\n");
        message.append("- **登录时间：** ⏰ ").append(loginTime).append("\n\n");

        // 安全提示
        message.append("---\n\n");
        if (isSuspicious) {
            message.append("### ⚠️ 安全提醒\n");
            message.append("> **检测到异常登录行为！**\n");
            message.append("> - 请确认是否为本人操作\n");
            message.append("> - 如非本人操作，请立即修改密码\n");
            message.append("> - 建议启用双因子认证\n\n");
        } else {
            message.append("### 💡 温馨提示\n");
            message.append("> 如非本人操作，请及时检查账户安全\n\n");
        }

        // 系统信息
        message.append("---\n");
        message.append("*快递云系统安全中心*");

        return message.toString();
    }

    /**
     * 根据IP获取地区信息
     *
     * @param ip IP地址
     * @return 地区信息
     */
    private String getLocationByIp(String ip) {
        try {
            if (StrUtil.isBlank(ip)) {
                return "未知";
            }

            // 使用框架提供的IP工具类获取地区
            Area area = IPUtils.getArea(ip);
            if (area == null) {
                return "未知";
            }

            // 格式化地区信息
            return AreaUtils.format(area.getId());

        } catch (Exception e) {
            log.warn("[getLocationByIp][IP地址解析失败] ip:{}", ip, e);
            return "未知";
        }
    }

    /**
     * 获取登录方式描述
     *
     * @param loginType 登录类型
     * @return 登录方式描述
     */
    private String getLoginMethodDescription(LoginLogTypeEnum loginType) {
        switch (loginType) {
            case LOGIN_USERNAME:
                return "账号密码登录";
            case LOGIN_MOBILE:
                return "手机号密码登录";
            case LOGIN_SMS:
                return "短信验证码登录";
            case LOGIN_SOCIAL:
                return "社交登录";
            default:
                return "其他方式登录";
        }
    }

    /**
     * 获取登录方式图标
     *
     * @param loginType 登录类型
     * @return 登录方式图标
     */
    private String getLoginMethodIcon(LoginLogTypeEnum loginType) {
        switch (loginType) {
            case LOGIN_USERNAME:
                return "🔑";
            case LOGIN_MOBILE:
                return "📱";
            case LOGIN_SMS:
                return "💬";
            case LOGIN_SOCIAL:
                return "🌐";
            default:
                return "🔐";
        }
    }

    /**
     * 获取地区图标
     *
     * @param location 地区信息
     * @return 地区图标
     */
    private String getLocationIcon(String location) {
        if (StrUtil.isBlank(location) || "未知".equals(location)) {
            return "🌍";
        }

        // 根据地区返回不同图标
        if (location.contains("北京")) {
            return "🏛️";
        } else if (location.contains("上海")) {
            return "🏙️";
        } else if (location.contains("广州") || location.contains("深圳")) {
            return "🌴";
        } else if (location.contains("杭州")) {
            return "🌸";
        } else if (location.contains("成都")) {
            return "🐼";
        } else if (location.contains("西安")) {
            return "🏺";
        } else {
            return "📍";
        }
    }

    /**
     * 判断是否为可疑登录
     *
     * @param userIp 用户IP
     * @param location 地区信息
     * @return 是否可疑
     */
    private boolean isSuspiciousLogin(String userIp, String location) {
        // 本地IP不算可疑
        if ("127.0.0.1".equals(userIp) || "localhost".equals(userIp) || userIp.startsWith("192.168.") || userIp.startsWith("10.")) {
            return false;
        }

        // 未知地区算可疑
        if (StrUtil.isBlank(location) || "未知".equals(location)) {
            return true;
        }

        // 国外IP算可疑（这里可以根据实际需求调整）
        if (!location.contains("中国") && !location.contains("北京") && !location.contains("上海") &&
            !location.contains("广州") && !location.contains("深圳") && !location.contains("杭州") &&
            !location.contains("成都") && !location.contains("西安") && !location.contains("省") &&
            !location.contains("市") && !location.contains("区")) {
            return true;
        }

        return false;
    }

    /**
     * 发送消息到企业微信
     *
     * @param message 消息内容
     */
    private void sendToWeChat(String message) {
        try {
            // 构建请求体 - 使用 Markdown 格式
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("msgtype", "markdown");

            Map<String, String> markdownContent = new HashMap<>();
            markdownContent.put("content", message);
            requestBody.put("markdown", markdownContent);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String response = restTemplate.postForObject(WEBHOOK_URL, requestEntity, String.class);

            log.debug("[sendToWeChat][企业微信响应] response:{}", response);

        } catch (Exception e) {
            log.error("[sendToWeChat][发送企业微信消息失败] message:{}", message, e);
            throw e;
        }
    }
}
