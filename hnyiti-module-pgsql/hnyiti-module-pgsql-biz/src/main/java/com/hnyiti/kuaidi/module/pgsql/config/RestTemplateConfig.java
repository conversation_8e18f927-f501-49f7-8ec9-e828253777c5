package com.hnyiti.kuaidi.module.pgsql.config;

import com.hnyiti.kuaidi.module.pgsql.service.notification.voice.config.DahanVoiceConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate 配置
 *
 * <AUTHOR>
 */
@Configuration
public class RestTemplateConfig {

    @Autowired
    private DahanVoiceConfig dahanVoiceConfig;

    /**
     * 配置用于语音服务的 RestTemplate
     */
    @Bean("voiceRestTemplate")
    public RestTemplate voiceRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(dahanVoiceConfig.getConnectTimeout());
        factory.setReadTimeout(dahanVoiceConfig.getReadTimeout());
        
        return new RestTemplate(factory);
    }

    /**
     * 默认的 RestTemplate（如果项目中没有的话）
     */
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(30000); // 30秒连接超时
        factory.setReadTimeout(60000);    // 60秒读取超时
        
        return new RestTemplate(factory);
    }
}
