spring:
    cloud:
        stream:
            bindings:
                order-sync-pgsql-output:
                    destination: ORDER-SYNC-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: order-sync-producer-group-topic-01 # 消费者分组
                order-assembly-output:
                    destination: ORDER-ASSEMBLY-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: order-assembly-producer-group-topic-01 # 消费者分组
                yida-input:
                    destination: YIDA-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: yida-producer-group-topic-01 # 消费者分组
                yidawork-input:
                    destination: YIDAWORK-TOPIC-01
                    content-type: application/json
                    group: yidawork-producer-group-topic-01
                kuaidi100-input:
                    destination: KUAIDI100-TOPIC-01
                    content-type: application/json
                    group: kuaidi100-producer-group-topic-01
                kuaidi100work-input:
                    destination: KUAIDI100WORK-TOPIC-01
                    content-type: application/json
                    group: kuaidi100work-producer-group-topic-01
                kuaidi100locus-input:
                    destination: KUAIDI100LOCUS-TOPIC-01
                    content-type: application/json
                    group: kuaidi100locus-producer-group-topic-01
                yuntong-input:
                    destination: YUNTONG-TOPIC-01
                    content-type: application/json
                    group: yuntong-producer-group-topic-01
                yuntongwork-input:
                    destination: YUNTONGWORK-TOPIC-01
                    content-type: application/json
                    group: yuntongwork-producer-group-topic-01
                # 有赞
                youzan-output:
                    destination: YOUZAN-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: youzan-producer-group-topic-01 # 消费者分组
                # 有赞
                youzan-input:
                    destination: YOUZAN-TOPIC-01
                    content-type: application/json
                    group: youzan-producer-group-topic-01
                # 德邦
                deppon-input:
                    destination: DEPPON-TOPIC-01
                    content-type: application/json
                    group: deppon-producer-group-topic-01
                # kdn
                kuaidiniao-input:
                    destination: kuaidiniao-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: kuaidiniao-producer-group-topic-01 # 消费者分组
                weixin-input:
                    destination: WEIXIN-TOPIC-01
                    content-type: application/json
                    group: weixin-producer-group-topic-01
                weixintransfer-input:
                    destination: WEIXINTRANSFER-TOPIC-01
                    content-type: application/json
                    group: weixintransfer-producer-group-topic-01
                alipay-input:
                    destination: ALIPAY-TOPIC-01
                    content-type: application/json
                    group: alipay-producer-group-topic-01
                weixinrefund-input:
                    destination: WEIXINREFUND-TOPIC-01
                    content-type: application/json
                    group: weixinrefund-producer-group-topic-01
                alipayrefund-input:
                    destination: ALIPAYREFUND-TOPIC-01
                    content-type: application/json
                    group: alipayrefund-producer-group-topic-01
                cainiao-input:
                    destination: CAINIAO-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: cainiao-producer-group-topic-01 # 消费者分组
                order-kickback-output:
                    destination: ORDER-KICKBACK-TOPIC-04 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: order-kickback-producer-group-topic-04 # 消费者分组
                order-kickback-input:
                    destination: ORDER-KICKBACK-TOPIC-04 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: order-kickback-producer-group-topic-04 # 消费者分组
                    consumer:
                        maxAttempts: 6
                        backOffInitialInterval: 1000
                        backOffMaxInterval: 10000
                        backOffMultiplier: 2.0
                #                order-assembly-input:
                #                    destination: ORDER-ASSEMBLY-TOPIC-01
                #                    content-type: application/json
                #                    group: order-assembly-producer-group-topic-01
                #订单风控
                order-risk-output:
                    destination: ORDER_RISK-TOPIC-01
                    content-type: application/json
                    group: order-risk-producer-group-topic-01
                #订单异常退款
                order-refund-output:
                    destination: ORDER_REFUND-TOPIC-01
                    content-type: application/json
                    group: order-refund-producer-group-topic-01
                order-risk-input:
                    destination: ORDER_RISK-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: order-risk-producer-group-topic-01 # 消费者分组
                order-refund-input:
                    destination: ORDER_REFUND-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: order-refund-producer-group-topic-01 # 消费者分组
                #微信公众号模版消息队列
                wx-msg-output:
                    destination: WX_MSG-TOPIC-04
                    content-type: application/json
                    group:  wx-msg-producer-group-topic-04
                wx-msg-input:
                    destination: WX_MSG-TOPIC-04 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group:  wx-msg-producer-group-topic-04 # 消费者分组
                #微信小程序模版消息队列
                mini-msg-output:
                    destination: MINI_MSG-TOPIC-04
                    content-type: application/json
                    group:  mini-msg-producer-group-topic-04
                mini-msg-input:
                    destination: MINI_MSG-TOPIC-04 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: mini-msg-producer-group-topic-04 # 消费者分组
                #订单补偿
                order-paycompensation-output:
                    destination: ORDER-PAYCOMPENSATION-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: order-paycompensation-producer-group-topic-01 # 消费者分组
                order-paycompensation-input:
                    destination: ORDER-PAYCOMPENSATION-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: order-paycompensation-producer-group-topic-01 # 消费者分组
                    consumer:
                        maxAttempts: 3
                        backOffInitialInterval: 1000
                        backOffMaxInterval: 10000
                        backOffMultiplier: 2.0
                balance-recharge-compensation-output:
                    destination: BALANCE-RECHARGE-COMPENSATION-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: balance-recharge-compensation-producer-group-topic-01 # 消费者分组
                balance-recharge-compensation-input:
                    destination: BALANCE-RECHARGE-COMPENSATION-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: balance-recharge-compensation-producer-group-topic-01 # 消费者分组
                    consumer:
                        maxAttempts: 3
                        backOffInitialInterval: 1000
                        backOffMaxInterval: 10000
                        backOffMultiplier: 2.0
                #查价差额记录日志消息队列
                kd-difference-output:
                    destination: KD-DIFFERENCE-TOPIC-01
                    content-type: application/json
                    group:  kd-difference-producer-group-topic-01
                kd-difference-input:
                    destination: KD-DIFFERENCE-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: kd-difference-producer-group-topic-01 # 消费者分组
                #企业微信群通知消息队列
                wecom-notify-output:
                    destination: WECOM-NOTIFY-TOPIC-01
                    content-type: application/json
                    group: wecom-notify-producer-group-topic-01
                wecom-notify-input:
                    destination: WECOM-NOTIFY-TOPIC-01
                    content-type: application/json
                    group: wecom-notify-producer-group-topic-01
                # 数据导出
                kd-export-output:
                    destination: KD-EXPORT-TOPIC-01
                    content-type: application/json
                    group: kd-export-producer-group-topic-01
                kd-export-input:
                    destination: KD-EXPORT-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: kd-export-producer-group-topic-01 # 消费者分组
                #订单上报coreData服务
                order-reported-output:
                    destination: ORDER-REPORTED-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: order-reported-producer-group-topic-01 # 消费者分组
                order-reported-input:
                    destination: ORDER-REPORTED-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: order-reported-producer-group-topic-01 # 消费者分组
                    consumer:
                        maxAttempts: 3
                        backOffInitialInterval: 1000
                        backOffMaxInterval: 10000
                        backOffMultiplier: 2.0
                # 总部对账单
                order-billing-output:
                    destination: ORDER-BILLING-TOPIC-01
                    content-type: application/json
                    group: order-billing-producer-group-topic-01
                order-billing-input:
                    destination: ORDER-BILLING-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: order-billing-producer-group-topic-01 # 消费者分组
                    consumer:
                        maxAttempts: 1
                        backOffInitialInterval: 1000
                        backOffMaxInterval: 10000
                        backOffMultiplier: 2.0
                # 用户受邀注册
                invited-register-input:
                    destination: INVITED-REGISTER-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: invited-register-producer-group-topic-01 # 消费者分组
                invited-delete-input:
                    destination: INVITED-DELETE-TOPIC-01
                    content-type: application/json
                    group: invited-delete-producer-group-topic-01
                #推三返一 分佣上报
                rebate-reported-output:
                    destination: REBATE-REPORTED-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: rebate-reported-producer-group-topic-01 # 消费者分组
                rebate-reported-input:
                    destination: REBATE-REPORTED-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: rebate-reported-producer-group-topic-01 # 消费者分组
                    consumer:
                        maxAttempts: 3
                        backOffInitialInterval: 1000
                        backOffMaxInterval: 10000
                        backOffMultiplier: 2.0
                #推三返一 分佣到账
                commission-rebate-output:
                    destination: COMMISSION-REBATE-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: commission-rebate-producer-group-topic-01 # 消费者分组
                commission-rebate-input:
                    destination: COMMISSION-REBATE-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: commission-rebate-producer-group-topic-01 # 消费者分组
                    consumer:
                        maxAttempts: 3
                        backOffInitialInterval: 1000
                        backOffMaxInterval: 10000
                        backOffMultiplier: 2.0
                # 支付宝用户提现队列
                alipay-user-withdrawal-output:
                    destination: ALIPAY-USER-WITHDRAWAL-TOPIC-01
                    content-type: application/json
                    group: alipay-withdrawal-producer-group-topic-01
                alipay-user-withdrawal-input:
                    destination: ALIPAY-USER-WITHDRAWAL-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: alipay-withdrawal-producer-group-topic-01 # 消费者分组
                #订单回调数据重新执行
                order-callback-repeat-output:
                    destination: ORDER-CALLBACK-REPEAT-TOPIC-01
                    content-type: application/json
                    group:  order-callback-repeat-group-topic-01
                order-callback-repeat-input:
                    destination: ORDER-CALLBACK-REPEAT-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group:  order-callback-repeat-group-topic-01 # 消费者分组
                #注册领优惠券
                newcomer-coupon-input:
                    destination: NEWCOMER-COUPON-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: newcomer-coupon-producer-group-topic-01 # 消费者分组
                newcomer-upgrade-input:
                    destination: NEWCOMER-UPGRADE-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: newcomer-upgrade-producer-group-topic-01 # 消费者分组
                user-merge-output:
                    destination: USER-MERGE-TOPIC-01
                    content-type: application/json
                    group: user-merge-producer-group-topic-01
                user-merge-input:
                    destination: USER-MERGE-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: user-merge-producer-group-topic-01 # 消费者分组
                send-message-output:
                    destination: SEND-MESSAGE-TOPIC-01
                    content-type: application/json
                    group: send-message-producer-group-topic-01
                #手动推送异常订单
                order-callback-output:
                    destination: ORDER-CALLBACK-TOPIC-01
                    content-type: application/json
                    group: order-callback-producer-group-topic-01
                order-callback-input:
                    destination: ORDER-CALLBACK-TOPIC-01 # 目的地。这里使用 RocketMQ Topic
                    content-type: application/json # 内容格式。这里使用 JSON
                    group: order-callback-producer-group-topic-01 # 消费者分组
                #补差发送电话催缴mq
                compensation-immediate-output:
                    destination: COMPENSATION-IMMEDIATE-TOPIC-01
                    content-type: application/json
                    group: compensation-immediate-producer-group-topic-01
                #日志
            # Spring Cloud Stream RocketMQ 配置项
            rocketmq:
                # RocketMQ Binder 配置项，对应 RocketMQBinderConfigurationProperties 类
                binder:
                    name-server: ${common.rockermq.ip}:${common.rockermq.port} # RocketMQ Namesrv 地址
                # RocketMQ 自定义 Binding 配置项，对应 RocketMQBindingProperties Map
                bindings:
                    order-risk-output:
                        producer:
                            group: order-risk-producer-group-topic-01
                            sync: true
                    order-refund-output:
                        producer:
                            group: order-refund-producer-group-topic-01
                            sync: true
                    #微信公众号消息队列
                    wx-msg-output:
                        producer:
                            group: wx-msg-producer-group-topic-04
                            sync: true
                    #微信小程序消息队列
                    mini-msg-output:
                        producer:
                            group: mini-msg-producer-group-topic-04
                            sync: true
                    order-fee-output:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        producer:
                            group: order-fee-producer-group-topic-01
                    order-assembly-output:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        producer:
                            group: order-assembly-output-group-topic-01
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    order-sync-pgsql-output:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        producer:
                            group: order-sync-producer-group-topic-01
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    yida-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    yidawork-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    kuaidi100-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    kuaidi100work-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    kuaidi100locus-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    yuntong-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    yuntongwork-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    youzan-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    kuaidiniao-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    youzan-output:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        producer:
                            group: youzan-producer-group-topic-01
                            sync: true
                    weixin-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    weixintransfer-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    alipay-input: # 支付宝回调
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    weixinrefund-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    alipayrefund-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    cainiao-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    deppon-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    order-kickback-output:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        producer:
                            group: order-kickback-producer-group-topic-04
                            sync: true
                    order-kickback-input:
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    #订单风控
                    order-risk-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    #下单异常退款
                    order-refund-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    #微信公众号消息队列
                    wx-msg-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    #微信小程序消息队列
                    mini-msg-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    order-paycompensation-output:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        producer:
                            group: order-paycompensation-producer-group-topic-01
                            sync: true
                    order-paycompensation-input:
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    balance-recharge-compensation-output:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        producer:
                            group: balance-recharge-compensation-producer-group-topic-01
                            sync: true
                    balance-recharge-compensation-input:
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    order-reported-output:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        producer:
                            group: order-reported-producer-group-topic-01
                            sync: true
                    order-reported-input:
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    #查价差额记录日志息队列
                    kd-difference-output:
                        producer:
                            group: kd-difference-producer-group-topic-01
                            sync: true
                    #查价差额记录日志消息队列
                    kd-difference-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    #企业微信群通知消息队列
                    wecom-notify-output:
                        producer:
                            group: wecom-notify-producer-group-topic-01
                            sync: true
                    wecom-notify-input:
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    kd-export-output:
                        producer:
                            group: kd-export-producer-group-topic-01
                            sync: true
                    kd-export-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    order-billing-output:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        producer:
                            group: order-billing-producer-group-topic-01
                            sync: true
                    order-billing-input:
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    #用户受邀注册
                    invited-register-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    invited-delete-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    # 分佣上报队列
                    rebate-reported-output:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        producer:
                            group: rebate-reported-producer-group-topic-01
                            sync: true
                    rebate-reported-input:
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    commission-rebate-output:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        producer:
                            group: commission-rebate-producer-group-topic-01
                            sync: true
                    commission-rebate-input:
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    alipay-user-withdrawal-output:
                      # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                      producer:
                        group: alipay-withdrawal-producer-group-topic-01
                        sync: true
                    alipay-user-withdrawal-input:
                      consumer:
                        enabled: true # 是否开启消费，默认为 true
                        broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    #微信公众号消息队列
                    order-callback-repeat-output:
                        producer:
                            group: order-callback-repeat-group-topic-01
                            sync: true
                    #微信公众号消息队列
                    order-callback-repeat-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    #注册领优惠券
                    newcomer-coupon-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    newcomer-upgrade-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    user-merge-output:
                        producer:
                            group: user-merge-producer-group-topic-01
                            sync: true
                    user-merge-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    send-message-output:
                        producer:
                            group: send-message-producer-group-topic-01
                            sync: true
                    #手动推送异常订单
                    order-callback-output:
                        producer:
                            group: order-callback-producer-group-topic-01
                            sync: true
                    order-callback-input:
                        # RocketMQ Consumer 配置项，对应 RocketMQConsumerProperties 类
                        consumer:
                            enabled: true # 是否开启消费，默认为 true
                            broadcasting: false # 是否使用广播消费，默认为 false 使用集群消费
                    #推送电话催缴
                    compensation-immediate-output:
                        producer:
                            group: compensation-immediate-producer-group-topic-01
                            sync: true

    main:
        allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
        allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Dubbo 或者 Feign 等会存在重复定义的服务

    # Servlet 配置
    servlet:
        # 文件上传相关配置项
        multipart:
            max-file-size: 16MB # 单个文件大小
            max-request-size: 32MB # 设置总上传的文件大小
    mvc:
        pathmatch:
            matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类

    # Jackson 配置项
    jackson:
        serialization:
            write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
            write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 **********.401，而是直接 **********401
            write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
            fail-on-empty-beans: false # 允许序列化无属性的 Bean

    # Cache 配置项
    cache:
        type: REDIS
        redis:
            time-to-live: 1h # 设置过期时间为 1 小时


--- #################### 接口文档配置 ####################


springdoc:
    api-docs:
        enabled: true # 1. 是否开启 Swagger 接文档的元数据
        path: /v3/api-docs
    swagger-ui:
        enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
        path: /swagger-ui.html

knife4j:
    enable: true # 2.2 是否开启 Swagger 文档的 Knife4j UI 界面
    setting:
        language: zh_cn

# MyBatis Plus 的配置项
mybatis-plus:
    configuration:
        map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
    #        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
    global-config:
        db-config:
            # 重要说明：如果将配置放到 Nacos 时，请注意将 id-type 设置为对应 DB 的类型，否则会报错；详细见 https://gitee.com/zhijiantianya/hnyiti-cloud/issues/I5W2N0 讨论
            #      id-type: NONE # "智能"模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
            id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
            #      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
            #      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
            logic-delete-value: 1 # 逻辑已删除值(默认为 1)
            logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    type-aliases-package: ${hnyiti.info.base-package}.dal.dataobject
    encryptor:
        password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成
--- #################### 芋道相关配置 ####################
hnyiti:
    rocketmq: true
    security:
        permit-all-urls:
            - /admin-api/kuaidi/wxmp/access/**
            - /app-api/open/**
            - /app-api/mini/v2/order/open/choosecom3
            - /app-api/mini/v2/sys/checkAddress
            - /app-api/mini/v2/sys/getLoginType/*
            - /app-api/mini/v2/sys/helplist
            - /app-api/mini/v2/sys/getCustomServiceConfig
#            - /app-api/mini/v2/order/choosecom
            - /admin-api/kuaidi/delivery/order/refundOrderBatch
            - /admin-api/infra/file/*/get/** # 获取图片，和租户无关
            - /app-api/mini/v2/mkt/share
#            - /app-api/mini/v2/api/messageTest
            - /app-api/mini/v2/order/getCouponSwitch
            - /app-api/mini/v2/mini/v2/sys/aliMiniCodeLogin
    info:
        version: 1.0.0
        base-package: com.hnyiti.kuaidi.module.**
    web:
        admin-ui:
            url: http://dashboard.hnyiti.com # Admin 快递后台 UI 的地址
    swagger:
        title: 快递后台
        description: 提供管理员管理的所有功能
        version: ${hnyiti.info.version}
        base-package: ${hnyiti.info.base-package}
    captcha:
        enable: false # 验证码的开关，默认为 true；
    error-code: # 错误码相关配置项
        constants-class-list:
            - com.hnyiti.kuaidi.module.system.enums.ErrorCodeConstants
    tenant: # 多租户相关配置项
        enable: true
        ignore-urls:
            - /admin-api/kuaidi/wxmp/access/**
            - /app-api/open/**
            - /app-api/mini/v2/order/open/choosecom3
            - /rpc-api/kuaidi/payCallBack
            - /rpc-api/kuaidi/callback
            - /rpc-api/kuaidi/refundCallBack
            - /rpc-api/kuaidi/workCallback
            - /app-api/kuaidi/third-params
            - /app-api/kuaidi/third
            - /app-api/mini/v2/api/getBackgroundFetchData/*
            - /admin-api/kuaidi/mkt/activities-share/getByTenantId
            - /admin-api/kuaidi/delivery/order/refundOrderBatch
            - /app-api/mini/v2/h5/auth/**
        ignore-tables:
            - delivery_order_callback_log
            - delivery_channel
            - delivery_channel_express
            - delivery_express
            - delivery_work_order_type
            - delivery_channel_price
            - system_tenant
            - delivery_product_pickup_rule
            - infra_data_source_config
            - delivery_product_type_data
            - mbr_address_freeze
            - kd_third_params
            - kd_third
            - kd_express
            - kd_line
            - kd_channel
            - kd_channel_code
            - kd_tenant_channel
            - kd_excel
            - kd_excel_item
            - kd_difference_log
            - delivery_order_picket
            - order_billing
            - infra_config
            - infra_file_config
            - infra_file
            - system_notice_group
            - api_limit_log
    sms-code: # 短信验证码相关的配置项
        expire-times: 10m
        send-frequency: 1m
        send-maximum-quantity-per-day: 10
        begin-code: 9999 # 这里配置 9999 的原因是，测试方便。
        end-code: 9999 # 这里配置 9999 的原因是，测试方便。

debug: false

--- #################### pay相关配置 ####################
pay:
    refundNotifyUrl: ''
    orderNotifyUrl: ''

--- ### leaf ###
leaf:
    name: leaf
    segment:
        enable: true
    jdbc:
        url: ${common.datasource.leaf.url}
        username: ${common.datasource.leaf.username}
        password: ${common.datasource.leaf.password}
#rocketmq:
#    name-server: ${common.rockermq.ip}:${common.rockermq.port}
#    producer:
#        group: order-product
#        sendMessageTimeout: 30000


rocketmq:
    name-server: ${common.rockermq.ip}:${common.rockermq.port}
    producer:
        group: log-group
rocketMq:
    destination: OP_LOG-TOPIC-08
#日志记录启用
logwatch:
    enable: true

# mqtt 配置文件移至nacos中去了

# 接口限制访问频率相关配置
rate:
  limit:
    white-list-ips:  #白名单IP
      - "************"
      - *************
    white-list-user-ids:    #白名单userId
      - 1
    limit-times: 5          #在limit-seconds秒内可以访问limit-times次
    limit-seconds: 10

spring:
  boot:
    admin:
      client:
        url: http://${ADMIN_SERVER_HOST:localhost}:${ADMIN_SERVER_PORT:48090}  # Spring Boot Admin Server 地址
        instance:
          prefer-ip: true  # 使用IP地址注册

# 开放监控端点
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 暴露所有端点
  endpoint:
    health:
      show-details: ALWAYS  # 显示详细的健康检查信息
    logfile:
      external-file: ${logging.file.name}  # 日志文件路径
