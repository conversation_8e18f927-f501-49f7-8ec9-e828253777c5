package com.hnyiti.kuaidi.module.pgsql.dal.mapper.notification;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通知记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NotificationRecordMapper extends BaseMapper<NotificationRecord> {

    /**
     * 根据订单ID和通知类型查询最新记录
     *
     * @param orderId 订单ID
     * @param tenantId 租户ID
     * @param notificationType 通知类型
     * @return 通知记录
     */
    NotificationRecord selectLatestByOrderIdAndType(@Param("orderId") String orderId, 
                                                    @Param("tenantId") Long tenantId,
                                                    @Param("notificationType") String notificationType);

    /**
     * 根据订单ID查询所有通知记录
     *
     * @param orderId 订单ID
     * @param tenantId 租户ID
     * @return 通知记录列表
     */
    List<NotificationRecord> selectByOrderId(@Param("orderId") String orderId, 
                                             @Param("tenantId") Long tenantId);

    /**
     * 统计订单的成功通知数量
     *
     * @param orderId 订单ID
     * @param tenantId 租户ID
     * @param notificationTypes 通知类型列表
     * @return 成功通知数量
     */
    int countSuccessNotifications(@Param("orderId") String orderId, 
                                  @Param("tenantId") Long tenantId,
                                  @Param("notificationTypes") List<String> notificationTypes);
}
