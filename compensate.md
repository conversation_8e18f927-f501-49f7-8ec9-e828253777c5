# 快递订单补差明细表 (delivery_order_compensate) 业务分析

## 📋 表结构概述

`delivery_order_compensate` 表是快递云系统中用于记录订单补差明细的核心业务表，主要处理快递订单在重量变化时产生的费用补差和退款业务。

### 表结构定义

```sql
CREATE TABLE delivery_order_compensate (
    tenant_id           BIGINT,                    -- 租户号
    deleted             BOOLEAN        DEFAULT FALSE, -- 逻辑删除
    creator             VARCHAR(32),               -- 创建人
    create_time         TIMESTAMP,                 -- 创建时间
    updater             VARCHAR(32),               -- 更新人
    update_time         TIMESTAMP,                 -- 更新时间
    id                  BIGSERIAL PRIMARY KEY,     -- 主键ID
    order_id            VARCHAR(64),               -- 订单ID
    compensating_state  VARCHAR(6)     DEFAULT '1', -- 补差状态
    compensating_result VARCHAR(6)     DEFAULT '1', -- 补差结果
    compensation_amount DECIMAL(24, 6),            -- 补差金额
    compensation_type   VARCHAR(32),               -- 补差方式
    compensation_paid   DECIMAL(24, 6) DEFAULT 0,  -- 已补差金额
    compensation_time   TIMESTAMP,                 -- 补差时间
    remark              VARCHAR(512)               -- 补差备注
);
```

## 🔍 核心业务场景

### 1. 超重补差场景

当快递公司称重后发现实际重量超过下单时的重量，需要用户补缴差价：

```mermaid
graph TD
    A[用户下单] --> B[快递公司称重]
    B --> C{实际重量 > 预估重量?}
    C -->|是| D[计算补差金额]
    C -->|否| E[正常流程]
    D --> F[创建补差记录]
    F --> G[推送补差通知]
    G --> H[用户支付补差]
    H --> I[更新补差状态]
    I --> J[完成补差]
```

### 2. 超轻退款场景

当实际重量小于预估重量时，系统自动退还多收的费用：

```mermaid
graph TD
    A[快递公司称重] --> B{实际重量 < 预估重量?}
    B -->|是| C[计算退款金额]
    B -->|否| D[正常流程]
    C --> E[创建退款记录]
    E --> F[自动退款处理]
    F --> G[更新退款状态]
```

## 📊 状态枚举定义

### 补差状态 (compensating_state)

| 状态码 | 状态名称 | 描述 |
|--------|----------|------|
| 1 | 正常 | 无需补差或退款 |
| 2 | 超重补差 | 需要用户补缴差价 |
| 3 | 超轻退款 | 需要退还多收费用 |

### 补差结果 (compensating_result)

| 状态码 | 状态名称 | 描述 |
|--------|----------|------|
| 1 | 无需补差 | 重量正常，无需处理 |
| 2 | 未完成 | 补差或退款处理中 |
| 3 | 已完成 | 补差或退款已完成 |
| 4 | 重推 | 重新推送补差信息 |
| 5 | 已取消 | 补差已取消 |

### 补差方式 (compensation_type)

| 类型码 | 支付方式 | 描述 |
|--------|----------|------|
| 1 | 微信 | 微信支付补差 |
| 2 | 余额 | 账户余额支付 |
| 3 | 后台 | 后台人工处理 |

## 🏗️ 核心业务逻辑

### 1. 补差记录创建

#### 触发条件
- 快递公司回调重量变更信息
- 实际重量与预估重量存在差异
- 差异金额超过设定阈值

#### 创建逻辑实现

**文件**: `OrderCompensateServiceImpl.java`
**方法**: `updateOrderCompensateState(String orderId)`
**行数**: 296-380

```java
@Override
public void updateOrderCompensateState(String orderId) {
    if (ObjectUtil.isEmpty(orderId)) {
        return;
    }
    OrderDO orderDO = orderService.getOrder(orderId);
    if (ObjectUtil.isEmpty(orderDO)) {
        return;
    }

    // 查询订单补差明细表：是否存在该订单Id、且补差状态不是重推
    OrderCompensateDO compensateDO = orderCompensateMapper.selectOne(
            new LambdaQueryWrapperX<OrderCompensateDO>()
                    .eq(OrderCompensateDO::getOrderId, orderDO.getId())
                    .orderByDesc(OrderCompensateDO::getCreateTime)
                    .last("limit 1")
    );

    // 查询delivery_order_compensate分布式的主键id
    Long compensateId = segmentService.getId("delivery_order_compensate").getId();

    // 业务规则处理逻辑（行数：314-380）
    // 1. 当推送不管是任何状态以及金额，当补差明细表中没有对应订单Id数据的时候都进行新增操作
    // 2. 当推送的状态与数据库中的状态金额一致，不进行操作
    // 3. 当推送的状态的状态一致，金额不一致时，则进行修改操作
    // 4. 当推送状态或金额发生改变，之前状态为超重、超轻已完成，正常，不做修改操作，新增数据
    // 5. 当推送状态或金额发生改变，之前状态为未完成。进行修改操作：修改记录的状态为重推，新增数据
}
```

**调用入口**: `OrderServiceImpl.updateOrderCallback()`
**行数**: 1710-1711

### 2. 补差完成处理

#### 超重补差完成实现

**文件**: `OrderCompensateServiceImpl.java`
**方法**: `compensationFinish(String orderId, BigDecimal amount, String payType)`
**行数**: 182-230

```java
@Override
public void compensationFinish(String orderId, BigDecimal amount, String payType) {
    // 判断当前订单的补差是否是历史订单
    Boolean isHistory = Boolean.FALSE;
    if (StrUtil.isEmpty(orderId)) {
        return;
    }

    // 先查询当前表
    OrderCompensateDO orderCompensateDO = orderCompensateMapper.selectOne(
        new LambdaQueryWrapperX<OrderCompensateDO>()
            .eq(OrderCompensateDO::getOrderId, orderId)
            .orderByDesc(OrderCompensateDO::getCreateTime)
            .last("limit 1")
    );

    // 如果当前表没有，查询历史表
    if (ObjectUtil.isEmpty(orderCompensateDO)) {
        orderCompensateDO = orderCompensateMapper.getOrderCompensateHis(orderId);
        isHistory = Boolean.TRUE;
    }

    if (ObjectUtil.isEmpty(orderCompensateDO)) {
        return;
    }

    // 更新补差信息
    orderCompensateDO.setCompensatingResult(OrderConstants.ORDER_COMPENSATING_RESULT_THREE); // 已完成
    orderCompensateDO.setCompensationPaid(amount);
    orderCompensateDO.setCompensationType(payType);
    orderCompensateDO.setCompensationTime(LocalDateTime.now());

    // 根据是否历史数据选择更新方式
    if (isHistory) {
        orderCompensateMapper.updateHisById(orderCompensateDO);
    } else {
        orderCompensateMapper.updateById(orderCompensateDO);
    }
}
```

**调用入口**: `OrderSubmitServiceImpl.handleCompensationDate()`
**行数**: 2622

#### 超轻退款处理实现

**文件**: `OrderCompensateServiceImpl.java`
**方法**: `refundAmount(String orderId, String payWay, BigDecimal refundAmount, Boolean isLessWeight)`
**行数**: 232-294

```java
@Override
public void refundAmount(String orderId, String payWay, BigDecimal refundAmount, Boolean isLessWeight) {
    if (StrUtil.isEmpty(orderId)) {
        return;
    }

    // 查询delivery_order_compensate分布式的主键id
    Long compensateId = segmentService.getId("delivery_order_compensate").getId();

    // 创建退款记录
    OrderCompensateDO orderCompensateDO = new OrderCompensateDO();
    orderCompensateDO.setId(compensateId);
    orderCompensateDO.setOrderId(orderId);
    orderCompensateDO.setCompensatingState(OrderConstants.ORDER_COMPENSATING_STATE_REFUND); // 超轻退款
    orderCompensateDO.setCompensatingResult(OrderConstants.ORDER_COMPENSATING_RESULT_THREE); // 已完成
    orderCompensateDO.setCompensationAmount(refundAmount);
    orderCompensateDO.setCompensationType(payWay);
    orderCompensateDO.setCompensationTime(LocalDateTime.now());

    // 设置备注信息
    if (isLessWeight != null && isLessWeight) {
        orderCompensateDO.setRemark("超轻退款");
    } else {
        orderCompensateDO.setRemark("退款处理");
    }

    // 保存退款记录
    orderCompensateMapper.insert(orderCompensateDO);
}
```

### 3. 订单取消处理

#### 取消补差记录实现

**文件**: `OrderCompensateServiceImpl.java`
**方法**: `orderCancel(OrderDO orderDO)`
**行数**: 130-180

```java
@Override
public void orderCancel(OrderDO orderDO) {
    if (ObjectUtil.isEmpty(orderDO) || StrUtil.isEmpty(orderDO.getId())) {
        return;
    }

    // 查询最新的超重补差未完成记录
    OrderCompensateDO orderCompensateDO = orderCompensateMapper.selectOne(
        new LambdaQueryWrapperX<OrderCompensateDO>()
            .eq(OrderCompensateDO::getOrderId, orderDO.getId())
            .eq(OrderCompensateDO::getCompensatingState, OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO) // 超重补差
            .eq(OrderCompensateDO::getCompensatingResult, OrderConstants.ORDER_COMPENSATING_RESULT_TWO) // 未完成
            .orderByDesc(OrderCompensateDO::getCreateTime)
            .last("limit 1")
    );

    if (ObjectUtil.isNotEmpty(orderCompensateDO)) {
        // 标记为已取消
        orderCompensateDO.setCompensatingResult(OrderConstants.ORDER_COMPENSATING_RESULT_FIVE); // 已取消
        orderCompensateDO.setRemark("订单取消，补差记录自动取消");
        orderCompensateMapper.updateById(orderCompensateDO);

        log.info("[orderCancel] 订单取消，补差记录已标记为取消，orderId: {}, compensateId: {}",
                orderDO.getId(), orderCompensateDO.getId());
    }
}
```

**调用入口**: 订单取消相关业务流程中调用

## 🔄 业务流程详解

### 1. 重量变更回调处理流程

#### 回调入口实现

**文件**: `OrderCallbackServiceImpl.java`
**方法**: `updateOrderCallback(OrderUpdateCallbackVo updateCallbackVo)`
**行数**: 1703-1712

```java
// 当推送的状态为超重超轻未完成时
// 1. 当推送不管是任何状态以及金额，当补差明细表中没有对应订单Id数据的时候都进行新增操作
// 2. 当推送的状态与数据库中的状态金额一致，不进行操作
// 3. 当推送的状态的状态一致，金额不一致时，则进行修改操作
// 4. 当推送状态或金额发生改变，之前状态为超重、超轻已完成，正常，不做修改操作，新增数据
// 5. 当推送状态或金额发生改变，之前状态为未完成。进行修改操作：修改记录的状态为重推，新增数据

// 1. 当推送不管是任何状态以及金额，当补差明细表中没有对应订单Id数据的时候都进行新增操作
orderCompensateService.updateOrderCompensateState(orderId);
```

#### 补差金额计算实现

**文件**: `OrderCallbackServiceImpl.java`
**方法**: `calculateCompensationAmount(BigDecimal oldCost, BigDecimal newCost, BigDecimal reliefAmount)`
**行数**: 1614-1629

### 2. 补差金额计算逻辑

```java
private BigDecimal calculateCompensationAmount(BigDecimal oldCost, BigDecimal newCost, BigDecimal reliefAmount) {
    BigDecimal compensationAmount = BigDecimal.ZERO;
    if (ObjectUtil.isEmpty(oldCost) || ObjectUtil.isEmpty(newCost)) {
        return compensationAmount;
    }

    // 如果有减免金额的话，需要加上减免金额
    if (ObjectUtil.isNotEmpty(reliefAmount)) {
        oldCost = oldCost.add(reliefAmount);
    }
    // 待补差金额 ，如果是负数的话，是补差金额，如果是正数，是退款金额。
    compensationAmount = newCost.subtract(oldCost);

    return compensationAmount;
}
```

#### 获取补差金额实现

**文件**: `OrderSubmitServiceImpl.java`
**方法**: `getCompensationAmount(OrderDO order)`
**行数**: 3073-3080

```java
public Integer getCompensationAmount(OrderDO order) {
    Integer price = 0;
    BigDecimal compensationAmount = ObjectUtil.isNotEmpty(order.getCompensationAmount()) ? order.getCompensationAmount() : BigDecimal.valueOf(0);
    BigDecimal compensationPaid0 = ObjectUtil.isNotEmpty(order.getCompensationPaid0()) ? order.getCompensationPaid0() : BigDecimal.valueOf(0);
    BigDecimal amount = (compensationAmount.subtract(compensationPaid0)).abs();
    price = amount.multiply(BigDecimal.valueOf(100)).intValue();
    return price;
}
```

### 3. 补差通知推送

#### 立即推送补差信息实现

**文件**: `OrderServiceImpl.java`
**方法**: `compensationPushNow(String orderId, String type)`
**行数**: 4305-4350

```java
@Override
public String compensationPushNow(String orderId, String type) {
    // 判断类型是否为空
    if (ObjectUtil.isEmpty(type) || ObjectUtil.isEmpty(orderId)) {
        throw new ServiceException(new ErrorCode(31501, "订单Id和推送方式不能为空"));
    }

    // 根据订单Id查询订单信息
    OrderDO orderDO = orderMapper.selectOne(OrderDO::getId, orderId);

    // 当订单为空或NULL，或者订单的状态不是超重补差未补时,补差金额为空不执行
    if (ObjectUtil.isEmpty(orderDO)
            || (!orderDO.getCompensatingState().equals(OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO)
                && !orderDO.getCompensatingResult().equals(OrderConstants.ORDER_COMPENSATING_RESULT_TWO))
            || ObjectUtil.isEmpty(orderDO.getCompensationAmount())) {
        throw new ServiceException(new ErrorCode(31501, "当前订单或状态不支持推送补差信息"));
    }

    // 根据推送类型处理不同的推送逻辑
    switch (type) {
        case "wechat":
            // 推送微信补差通知
            handleWechatCompensationPush(orderDO);
            break;
        case "sms":
            // 推送短信通知
            handleSmsCompensationPush(orderDO);
            break;
        default:
            throw new ServiceException(new ErrorCode(31501, "不支持的推送方式: " + type));
    }

    return "推送成功";
}
```

## 📈 数据查询与统计

### 1. 分页查询补差记录

#### 分页查询实现

**文件**: `OrderCompensateMapper.java`
**方法**: `selectPage(OrderCompensatePageReqVO reqVO)`
**行数**: 23-34

```java
default PageResult<OrderCompensateDO> selectPage(OrderCompensatePageReqVO reqVO) {
    return selectPage(reqVO, new LambdaQueryWrapperX<OrderCompensateDO>()
            .betweenIfPresent(OrderCompensateDO::getCreateTime, reqVO.getCreateTime())
            .eqIfPresent(OrderCompensateDO::getOrderId, reqVO.getOrderId())
            .eqIfPresent(OrderCompensateDO::getCompensatingState, reqVO.getCompensatingState())
            .eqIfPresent(OrderCompensateDO::getCompensatingResult, reqVO.getCompensatingResult())
            .eqIfPresent(OrderCompensateDO::getCompensationAmount, reqVO.getCompensationAmount())
            .eqIfPresent(OrderCompensateDO::getCompensationType, reqVO.getCompensationType())
            .eqIfPresent(OrderCompensateDO::getCompensationPaid, reqVO.getCompensationPaid())
            .betweenIfPresent(OrderCompensateDO::getCompensationTime, reqVO.getCompensationTime())
            .orderByDesc(OrderCompensateDO::getCreateTime));
}
```

#### 服务层分页查询

**文件**: `OrderCompensateServiceImpl.java`
**方法**: `getOrderCompensatePage(OrderCompensatePageReqVO pageReqVO)`
**行数**: 70-72

### 2. 批量查询（包含历史数据）

#### 批量查询实现

**文件**: `OrderCompensateServiceImpl.java`
**方法**: `getCompensateAllByOrderIds(List<String> orderIds)`
**行数**: 393-398

```java
@Override
public List<OrderCompensateDO> getCompensateAllByOrderIds(List<String> orderIds) {
    if (ObjectUtil.isEmpty(orderIds)) {
        return null;
    }
    return orderCompensateMapper.getCompensateAllByOrderIds(orderIds);
}
```

#### 重载方法（支持范围数据控制）

**文件**: `OrderCompensateServiceImpl.java`
**方法**: `getCompensateAllByOrderIds(List<String> orderIds, Boolean isInRangeData)`
**行数**: 407-419

#### Mapper层SQL实现

**文件**: `OrderCompensateMapper.xml`
**方法**: `getCompensateAllByOrderIds`
**行数**: 12-36

```xml
<select id="getCompensateAllByOrderIds" resultType="OrderCompensateDO">
    SELECT * FROM (
        select * from delivery_order_compensate t1
        <where>
            <if test="orderIdList != null and orderIdList.size > 0">
                AND t1.order_id in
                <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
                    #{orderId}
                </foreach>
            </if>
        </where>
        UNION ALL
        select * from delivery_order_compensate_history t1
        <where>
            <if test="orderIdList != null and orderIdList.size > 0">
                AND t1.order_id in
                <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
                    #{orderId}
                </foreach>
            </if>
        </where>
    ) t
    ORDER BY t.compensating_result DESC
</select>
```

## 🔧 技术实现特点

### 1. 多数据源支持

#### MySQL数据源（主要业务）
- **文件**: `OrderCompensateMapper.java`
- **注解**: `@TenantDS` (行数: 20)
- **功能**: 主要业务数据存储和查询

#### PostgreSQL数据源（数据同步）
- **文件**: `DeliveryOrderCompensateService.java`
- **功能**: 数据同步和备份存储
- **批量保存**: `saveCompensateBatch()` (行数: 207-226)

### 2. 历史数据管理

#### 当前表和历史表查询
- **当前表**: `delivery_order_compensate`
- **历史表**: `delivery_order_compensate_history`
- **查询历史**: `getOrderCompensateHis()` (OrderCompensateMapper.xml 行数: 54-68)
- **更新历史**: `updateHisById()` (OrderCompensateMapper.xml 行数: 70-95)

### 3. 分布式ID生成

#### ID生成实现
**文件**: `OrderCompensateServiceImpl.java`
**使用位置**: 多个方法中 (行数: 53, 206, 315)

```java
// 使用分段ID生成器
Long compensateId = segmentService.getId("delivery_order_compensate").getId();
orderCompensate.setId(compensateId);
```

### 4. 租户隔离

#### 数据隔离实现
- **Mapper注解**: `@TenantDS` 确保租户级别的数据源隔离
- **删除方法**: `deleteByOrderIdAndTenantId()` (DeliveryOrderCompensateMapper.java 行数: 14)
- **所有查询**: 自动基于 `tenant_id` 进行数据过滤

## 🚨 异常处理与监控

### 1. 补差异常处理

#### 支付回调异常处理
**文件**: `OrderServiceImpl.java`
**方法**: `passCallback()` (行数: 3438-3449)
```java
} else if ("compensation".equals(type)) {
    // 超重补差
    // 进入该判断则发送异常数据
    sendTipsMessage(orderId,"compensation","超重补差");
}
```

#### 支付补偿机制
**文件**: `OrderServiceImpl.java`
**方法**: `initiatePaymentCompensation()` (行数: 3143-3158)
- 订单状态检查
- MQ补偿消息发送

### 2. 数据一致性保证

#### 事务控制
- **注解**: `@Transactional(rollbackFor = Exception.class)`
- **文件**: `DeliveryOrderCompensateService.java` (行数: 59)
- **确保**: 补差记录的原子性操作

#### 补偿机制
**文件**: `OrderMqProducer.java`
**方法**: `sendOrderPayCompensation()` (行数: 101-107)
- 延时消息处理
- 自动重试机制

### 3. 业务监控指标

#### 关键常量定义
**文件**: `OrderConstants.java`
**补差状态常量** (行数: 179-183):
- `ORDER_COMPENSATING_STATE_NORMAL = "1"` (正常)
- `ORDER_COMPENSATING_STATE_OMPENSATIO = "2"` (超重补差)
- `ORDER_COMPENSATING_STATE_REFUND = "3"` (超轻退款)

**补差结果常量** (行数: 208-212):
- `ORDER_COMPENSATING_RESULT_ONE = "1"` (无需补差)
- `ORDER_COMPENSATING_RESULT_TWO = "2"` (未完成)
- `ORDER_COMPENSATING_RESULT_THREE = "3"` (已完成)

## 📝 核心代码文件汇总

### 主要业务实现类
1. **OrderCompensateServiceImpl.java** - 补差业务核心实现
   - 补差完成: `compensationFinish()` (182-230行)
   - 超轻退款: `refundAmount()` (232-294行)
   - 状态更新: `updateOrderCompensateState()` (296-380行)

2. **OrderServiceImpl.java** - 订单服务补差相关
   - 补差金额修改: `updateCompensationAmount()` (658-709行)
   - 立即推送: `compensationPushNow()` (4305-4350行)
   - 支付补偿: `initiatePaymentCompensation()` (3143-3158行)

3. **OrderSubmitServiceImpl.java** - 订单提交补差处理
   - 补差完成: `compensationFinish()` (2366-2635行)
   - 金额计算: `getCompensationAmount()` (3073-3080行)

4. **OrderCallbackServiceImpl.java** - 回调处理
   - 金额计算: `calculateCompensationAmount()` (1614-1629行)

### 数据访问层
1. **OrderCompensateMapper.java** - MySQL数据访问
   - 分页查询: `selectPage()` (23-34行)
   - 批量查询: `getCompensateAllByOrderIds()` (50行)

2. **DeliveryOrderCompensateMapper.java** - PostgreSQL数据访问
   - 删除操作: `deleteByOrderIdAndTenantId()` (14行)

### 配置和常量
1. **OrderConstants.java** - 业务常量定义
   - 补差状态: (179-183行)
   - 补差结果: (208-212行)

2. **DictTypeConstants.java** - 字典类型
   - 补差状态字典: `COMPENSATING_STATE` (60行)
   - 补差结果字典: `COMPENSATING_RESULT` (62行)

这个补差系统通过精确的代码实现和完善的异常处理机制，确保了快递订单费用的准确性和业务流程的稳定性。
