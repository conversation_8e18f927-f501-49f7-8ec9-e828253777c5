package com.hnyiti.kuaidi.module.pgsql.dal.mapper.notification;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 通知任务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NotificationTaskMapper extends BaseMapper<NotificationTask> {

    /**
     * 查询待执行的任务
     *
     * @param currentTime 当前时间
     * @param limit 限制数量
     * @return 待执行任务列表
     */
    List<NotificationTask> selectPendingTasks(@Param("currentTime") LocalDateTime currentTime, 
                                              @Param("limit") Integer limit);

    /**
     * 查询需要重试的任务
     *
     * @param currentTime 当前时间
     * @param limit 限制数量
     * @return 需要重试的任务列表
     */
    List<NotificationTask> selectRetryTasks(@Param("currentTime") LocalDateTime currentTime, 
                                            @Param("limit") Integer limit);

    /**
     * 根据订单ID查询任务
     *
     * @param orderId 订单ID
     * @param tenantId 租户ID
     * @return 任务列表
     */
    List<NotificationTask> selectByOrderId(@Param("orderId") String orderId, 
                                           @Param("tenantId") Long tenantId);

    /**
     * 取消订单的后续任务
     *
     * @param orderId 订单ID
     * @param tenantId 租户ID
     * @param currentTime 当前时间
     * @return 更新数量
     */
    int cancelSubsequentTasks(@Param("orderId") String orderId, 
                              @Param("tenantId") Long tenantId, 
                              @Param("currentTime") LocalDateTime currentTime);

    /**
     * 更新任务状态
     *
     * @param id 任务ID
     * @param status 新状态
     * @param executeTime 执行时间
     * @param resultData 结果数据
     * @return 更新数量
     */
    int updateTaskStatus(@Param("id") Long id, 
                         @Param("status") String status, 
                         @Param("executeTime") LocalDateTime executeTime, 
                         @Param("resultData") String resultData);

    /**
     * 更新重试信息
     *
     * @param id 任务ID
     * @param retryCount 重试次数
     * @param nextRetryTime 下次重试时间
     * @return 更新数量
     */
    int updateRetryInfo(@Param("id") Long id, 
                        @Param("retryCount") Integer retryCount, 
                        @Param("nextRetryTime") LocalDateTime nextRetryTime);
}
