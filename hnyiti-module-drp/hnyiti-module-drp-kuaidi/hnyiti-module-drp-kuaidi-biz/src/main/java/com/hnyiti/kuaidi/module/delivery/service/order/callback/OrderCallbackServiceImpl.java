package com.hnyiti.kuaidi.module.delivery.service.order.callback;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.aliyun.credentials.utils.StringUtils;
import com.hnyiti.kuaidi.constants.KuaiDi100Constants;
import com.hnyiti.kuaidi.framework.common.exception.ErrorCode;
import com.hnyiti.kuaidi.framework.common.exception.ServiceException;
import com.hnyiti.kuaidi.framework.common.pojo.PageResult;
import com.hnyiti.kuaidi.framework.common.util.json.JsonUtils;
import com.hnyiti.kuaidi.framework.dict.core.util.DictFrameworkUtils;
import com.hnyiti.kuaidi.framework.tenant.core.context.TenantContextHolder;
import com.hnyiti.kuaidi.module.consumer.OrderMqProducer;
import com.hnyiti.kuaidi.module.consumer.wxmsg.WxMiniMsgVo;
import com.hnyiti.kuaidi.module.consumer.wxmsg.WxMqProducer;
import com.hnyiti.kuaidi.module.consumer.wxmsg.WxMsgTypeConstants;
import com.hnyiti.kuaidi.module.delivery.api.brokerage.BrokerageApi;
import com.hnyiti.kuaidi.module.delivery.api.brokerage.vo.BrokerageRespVO;
import com.hnyiti.kuaidi.module.delivery.api.brokerage.vo.BrokerageUpdateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.billing.vo.PushMqttVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.feesuper.vo.FeeSuperUpdateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.feetenant.vo.FeeTenantUpdateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.feeuser.vo.FeeUserUpdateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.kd.channel.vo.KdChannelExportReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.order.OrderBigVo;
import com.hnyiti.kuaidi.module.delivery.controller.admin.order.OrderCallbackBigVo;
import com.hnyiti.kuaidi.module.delivery.controller.admin.order.OrderCallbackVo;
import com.hnyiti.kuaidi.module.delivery.controller.admin.orderaccountadjustment.vo.OrderAccountAdjustmentUpdateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.ordercallbacklog.vo.OrderCallbackLogUpdateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.ordercompensate.vo.OrderCompensateExportReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.orderfee.vo.OrderFeeBaseVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.orderoverweightcallback.vo.OrderOverweightCallbackCreateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.orderparcel.vo.OrderParcelUpdateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.userfreezelog.vo.UserFreezeLogCreateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.workorder.vo.WorkOrderPageReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.workorder.vo.WorkOrderRespVO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.channelprice.ChannelPriceDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.excelitem.ExcelItemDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.feesuper.FeeSuperDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.feetenant.FeeTenantDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.feeuser.FeeUserDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.kd.channel.KdChannelDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.order.OrderDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.orderaccountadjustment.OrderAccountAdjustmentDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.ordercompensate.OrderCompensateDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.ordercontacts.OrderContactsDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.orderfee.OrderFeeDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.orderoverweightcallback.OrderOverweightCallbackDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.orderparcel.OrderParcelDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.orderpolling.OrderPollingDO;
import com.hnyiti.kuaidi.module.delivery.enums.OrderCallbackConstants;
import com.hnyiti.kuaidi.module.delivery.enums.OrderConstants;
import com.hnyiti.kuaidi.module.delivery.enums.ProvinceMap;
import com.hnyiti.kuaidi.module.delivery.enums.UserFreezeLogEnum;
import com.hnyiti.kuaidi.module.delivery.enums.order.OrderDeliveryOverweightEnum;
import com.hnyiti.kuaidi.module.delivery.enums.order.OrderDeliverySwitchEnum;
import com.hnyiti.kuaidi.module.delivery.enums.thirdparams.ThirdParamsSourceTypeEnum;
import com.hnyiti.kuaidi.module.delivery.framework.dingding.DingTalkHelper;
import com.hnyiti.kuaidi.module.delivery.framework.mqtt.MqttSendClient;
import com.hnyiti.kuaidi.module.delivery.service.channelexpress.ChannelExpressService;
import com.hnyiti.kuaidi.module.delivery.service.channelprice.ChannelPriceService;
import com.hnyiti.kuaidi.module.delivery.service.excelitem.ExcelItemService;
import com.hnyiti.kuaidi.module.delivery.service.express.ExpressService;
import com.hnyiti.kuaidi.module.delivery.service.feesuper.FeeSuperService;
import com.hnyiti.kuaidi.module.delivery.service.feetenant.FeeTenantService;
import com.hnyiti.kuaidi.module.delivery.service.feeuser.FeeUserService;
import com.hnyiti.kuaidi.module.delivery.service.kd.channel.KdChannelService;
import com.hnyiti.kuaidi.module.delivery.service.notify.NotifyParamService;
import com.hnyiti.kuaidi.module.delivery.service.notify.vo.NotifyMsgVo;
import com.hnyiti.kuaidi.module.delivery.service.order.*;
import com.hnyiti.kuaidi.module.delivery.service.orderaccountadjustment.OrderAccountAdjustmentService;
import com.hnyiti.kuaidi.module.delivery.service.ordercallbacklog.OrderCallbackLogService;
import com.hnyiti.kuaidi.module.delivery.service.ordercompensate.OrderCompensateService;
import com.hnyiti.kuaidi.module.delivery.service.ordercontacts.OrderContactsService;
import com.hnyiti.kuaidi.module.delivery.service.orderfee.OrderFeeService;
import com.hnyiti.kuaidi.module.delivery.service.orderfilter.OrderFilterService;
import com.hnyiti.kuaidi.module.delivery.service.orderoverweightcallback.OrderOverweightCallbackService;
import com.hnyiti.kuaidi.module.delivery.service.orderparcel.OrderParcelService;
import com.hnyiti.kuaidi.module.delivery.service.userfreeze.UserFreezeService;
import com.hnyiti.kuaidi.module.delivery.service.workorder.WorkOrderService;
import com.hnyiti.kuaidi.module.member.MemberConstants;
import com.hnyiti.kuaidi.module.member.api.user.MemberUserApi;
import com.hnyiti.kuaidi.module.member.api.user.dto.MemberUserRespDTO;
import com.hnyiti.kuaidi.module.message.api.notice.NoticeGroupCodeEnum;
import com.hnyiti.kuaidi.module.system.api.param.ParamApi;
import com.hnyiti.kuaidi.module.system.api.param.ReasonParamApi;
import com.hnyiti.kuaidi.module.system.api.sms.SmsSendApi;
import com.hnyiti.kuaidi.module.system.api.sms.dto.send.SmsSendSingleOrderToUserReqDTO;
import com.hnyiti.kuaidi.module.system.api.tenant.TenantApi;
import com.hnyiti.kuaidi.module.system.api.tenant.dto.TenantRespVO;
import com.hnyiti.kuaidi.module.system.api.tenantAmount.TenantAmountApi;
import com.hnyiti.kuaidi.module.system.enums.DictTypeConstants;
import com.hnyiti.kuaidi.module.system.enums.TenantAmount.TenantAmountConstants;
import com.hnyiti.kuaidi.module.wxmini.service.notify.WxMiniNotify;
import com.hnyiti.kuaidi.module.wxmini.service.notify.WxMpNotifyImpl;
import com.hnyiti.kuaidi.module.wxmini.weCom.WeChatGroupMessageTypeEnum;
import com.hnyiti.kuaidi.module.wxmini.weCom.WeChatGroupNotifyReq;
import com.hnyiti.kuaidi.vo.UpdateFeesRepVO;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderCallbackServiceImpl implements OrderCallbackService {


    @Lazy
    @Resource
    private OrderService orderService;
    // TODO 2024-12-10 取消订单相关ES操作
//    @Lazy
//    @Resource
//    private KuaidiCallbackRepositoryServer kuaidiCallbackRepositoryServer;
    @Lazy
    @Resource
    private OrderCallbackCommonService orderCallbackCommonService;
    @Lazy
    @Resource
    private WxMpNotifyImpl wxMpNotify;
    @Lazy
    @Resource
    private ChannelExpressService channelExpressService;
    @Lazy
    @Resource
    private OrderFeeService orderFeeService;
    @Lazy
    @Resource
    private OrderEsService orderEsService;
    @Lazy
    @Resource
    private OrderCalcService orderCalcService;
    @Lazy
    @Resource
    private OrderParcelService orderParcelService;
    @Lazy
    @Resource
    private TenantAmountApi tenantAmountApi;
    @Lazy
    @Resource
    private TenantApi tenantApi;
    @Lazy
    @Resource
    private BrokerageApi brokerageApi;
    @Lazy
    @Resource
    private WxMiniNotify miniNotify;
    @Lazy
    @Resource
    private ParamApi paramApi;
    @Lazy
    @Resource
    private OrderContactsService orderContactsService;
    @Lazy
    @Resource
    private DingTalkHelper dingTalkHelper;
    @Lazy
    @Resource
    private OrderOverweightCallbackService overweightCallbackService;
    @Lazy
    @Resource
    private OrderSubmitService orderSubmitService;
    @Lazy
    @Resource
    private MemberUserApi memberUserApi;
    @Lazy
    @Resource
    private SmsSendApi smsSendApi;
    @Lazy
    @Resource
    private OrderCompensateService compensateService;
    @Lazy
    @Resource
    private WorkOrderService workOrderService;
    @Lazy
    @Resource
    private FeeUserService feeUserService;
    @Lazy
    @Resource
    private FeeTenantService feeTenantService;
    @Lazy
    @Resource
    private FeeSuperService feeSuperService;
    @Lazy
    @Resource
    private OrderProcessService orderProcessService;
    @Lazy
    @Resource
    private OrderCallbackLogService orderCallbackLogService;
    @Resource
    private WxMqProducer wxMqProducer;
    @Autowired
    private MqttSendClient mqttSendClient;
    @Resource
    private UserFreezeService userFreezeService;
    @Lazy
    @Resource
    private OrderMqProducer orderMqProducer;
    @Lazy
    @Resource
    private ExcelItemService excelItemService;
    @Lazy
    @Resource
    private ExpressService expressService;
    @Lazy
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Lazy
    @Resource
    private KdChannelService kdChannelService;
    @Lazy
    @Resource
    private ChannelPriceService channelPriceService;
    @Resource
    private OrderPollingService orderPollingService;
    @Lazy
    @Resource
    private NotifyParamService notifyParamService;
    @Lazy
    @Autowired
    private ReasonParamApi reasonParamApi;
    @Resource
    private OrderAccountAdjustmentService orderAccountAdjustmentService;

    /**
     * @param tenantId
     * @param userId
     * @param orderId
     * @param orderNo
     * @param status
     */
    public void sendMqtt(String tenantId, String userId, String orderId, String orderNo, String status, String oldStatus) {
        if (ObjectUtil.isEmpty(tenantId) || ObjectUtil.isEmpty(userId) || ObjectUtil.isEmpty(orderId) || ObjectUtil.isEmpty(status) || ObjectUtil.isEmpty(oldStatus)) {
            return;
        }
        if (status.equals(oldStatus)) {
            return;
        }
        PushMqttVO pushMqttVO = new PushMqttVO();
        pushMqttVO.setOrderId(orderId);
        pushMqttVO.setOrderNo(orderNo);
        pushMqttVO.setTenantId(tenantId);
        pushMqttVO.setUserId(userId);
        pushMqttVO.setStatus(status);
        String statusLabel = DictFrameworkUtils.getDictDataLabel(DictTypeConstants.KUAIDI_STATUS, status);
        pushMqttVO.setStatusStr(statusLabel);
        String topic = "msg/" + pushMqttVO.getTenantId() + "/" + pushMqttVO.getUserId();
        mqttSendClient.publish(false, topic, JsonUtils.toJsonString(pushMqttVO));
    }


    /**
     * 订单回调
     *
     * @param callback 处理过后的回调对象
     */
    @Override
    public void orderCallback(OrderCallbackVo callback) {

        // 数据校验
        validateCallback(callback);
        // 执行回调逻辑
        callbackExecute(callback);
    }


    public void updateOrderNo(OrderCallbackVo callback, OrderDO orderDO) {
        if(ObjectUtil.isEmpty(callback)){return;}
        if(ObjectUtil.isEmpty(callback.getOrderNo())){return;}
        if(ObjectUtil.isEmpty(orderDO)){return;}
//        if(ObjectUtil.isEmpty(orderDO.getOrderNo())){return;}

        OrderDO updateOrder = new OrderDO();
        // 如果运单号不一致的话，修改运单号
        if(!callback.getOrderNo().equals(orderDO.getOrderNo())){
            updateOrder.setOrderNo(callback.getOrderNo());
            updateOrder.setId(orderDO.getId());
            // 修改数据
            orderService.updateById(updateOrder);
        }
    }

    /**
     * 运费到付
     */
    @GlobalTransactional
    public void freightCollectPayment(OrderCallbackBigVo orderCallbackBigVo, OrderCallbackVo callback){
        if(ObjectUtil.isEmpty(callback.getPayModel())){
            return;
        }
        if(!OrderConstants.ORDER_PAY_MODEL_UPONDELIVERY.equals(callback.getPayModel())){
            return;
        }
        // 订单已取消不执行
        if(OrderCallbackConstants.CALLBACK_STATUS_CANCELLED.equals(callback.getStatus())){
            return;
        }
        if(ObjectUtil.isNotEmpty(orderCallbackBigVo.getOrderInfo()) && OrderConstants.ORDER_STATUS_CANCELLED.equals(orderCallbackBigVo.getOrderInfo().getStatus())){
            return;
        }
        if(ObjectUtil.isEmpty(ObjectUtil.isNotEmpty(orderCallbackBigVo.getOrderInfo()))){
            return;
        }
        if(!OrderConstants.ORDER_PAY_MODEL_PAYCASH.equals(orderCallbackBigVo.getOrderInfo().getPayModel())){
            return;
        }
        String orderId = orderCallbackBigVo.getOrderId();

        // 订单改到付退钱
        orderProcessService.refundUponDelivery(orderId);


    }

    public Boolean getIsProcessingArrivePay(OrderDO orderDO, OrderCallbackVo callback) {
        if(ObjectUtil.isEmpty(orderDO)){
            return false;
        }
        if(ObjectUtil.isEmpty(callback) && ObjectUtil.isEmpty(callback.getPayModel())){
            return false;
        }
        // 订单已取消不执行
        if(OrderCallbackConstants.CALLBACK_STATUS_CANCELLED.equals(callback.getStatus())) {
            return false;
        }
        if(OrderConstants.ORDER_STATUS_CANCELLED.equals(orderDO.getStatus())){
            return false;
        }
        String orderPayModel = orderDO.getPayModel();
        if(ObjectUtil.isEmpty(orderPayModel) || !OrderConstants.ORDER_PAY_MODEL_PAYCASH.equals(orderPayModel)){
            return false;
        }

        if(ObjectUtil.isEmpty(callback) || !OrderConstants.ORDER_PAY_MODEL_UPONDELIVERY.equals(callback.getPayModel())){
            return false;
        }

        return true;
    }

    /**
     * 回调执行
     *
     * @param callback
     */
    public void callbackExecute(OrderCallbackVo callback) {
        // 状态
        String status = callback.getStatus();
        // 订单id
        String orderId = callback.getOrderId();

        // 查询订单详情封装对象
        OrderCallbackBigVo orderCallbackBigVo = orderService.assembleCallbackBigVo(orderId);

        OrderDO orderDO = orderCallbackBigVo.getOrderInfo();
        Assert.notNull(orderDO, "订单数据为空");

        // 如果运单号不一致，更改运单号
        updateOrderNo(callback, orderDO);

        // 判断是否现付改到付
        callback.setIsProcessingArrivePay(getIsProcessingArrivePay(orderDO, callback));

        String errorMessage = "";
        // 执行成功
        String logCallbackStatus = OrderCallbackConstants.CALLBACK_EXECUTED_SUCCESS;
        try {
            // 修改订单数据
            callbackUpdateOrder(callback, orderDO, orderCallbackBigVo);
            // 是否现付改到付款
            if (callback.getIsProcessingArrivePay()) {
                try {
                    freightCollectPayment(orderCallbackBigVo, callback);
                } catch (Exception e) {
                    log.error("订单改到付失败，失败原因:{}", e.getMessage());
                }
            }
        } catch (Exception e) {
            logCallbackStatus = OrderCallbackConstants.CALLBACK_CODE_ERROR;
            log.error("回调修改订单数据失败", e);
            // 同时发送消息告诉技术回调异常
            callBackMsg(callback, orderDO, e);
            errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.length() > 1000) {
                errorMessage = errorMessage.substring(0, 1000);
            }
        } finally {
            try {
                // 发送订单同步到PostgreSQL消息
                orderMqProducer.sendOrderSyncPgsql(orderCallbackBigVo.getOrderId(), "update");
            } catch (Exception e) {
                log.error("订单回调发送订单同步到PostgreSQL消息失败，订单ID：{}，错误信息：{}", orderCallbackBigVo.getOrderId(), e.getMessage());
            }
        }
        if (ObjectUtil.isNotEmpty(callback.getCallbackLogId())) {
//            修改本地回调日志表状态
            OrderCallbackLogUpdateReqVO updateReqVO = new OrderCallbackLogUpdateReqVO();
            updateReqVO.setId(callback.getCallbackLogId());
            updateReqVO.setStatus(logCallbackStatus);
            orderCallbackLogService.updateOrderCallbackLog(updateReqVO);
        }
    }

    private void callBackMsg(OrderCallbackVo callback, OrderDO orderDO, Exception e) {
        WxMiniMsgVo miniMsgVo = new WxMiniMsgVo();
        miniMsgVo.setTenantId(TenantContextHolder.getTenantId());
        miniMsgVo.setBizId(orderDO.getId());
        miniMsgVo.setOrderNo(orderDO.getOrderNo());
        miniMsgVo.setCallBackJson(JacksonUtils.toJson(callback));
        String errorMsg = "错误原因:" + e.getMessage();
        try {
            // 获取详细的堆栈信息
            errorMsg = getDetailedExceptionInfo(e);
        } catch (Exception ex) {
            log.error("获取错误堆栈信息失败：", e.getMessage());
        }


        // 记录日志，包含完整的错误信息
        log.error("订单回调处理异常。订单ID:{}，订单号:{}，详细错误信息:\n{}",
                orderDO.getId(), orderDO.getOrderNo(), errorMsg);

        // 设置错误信息
        miniMsgVo.setExceptionMessage(errorMsg);
        miniMsgVo.setMsgType(WxMsgTypeConstants.ERROR);
        miniMsgVo.setBizType(WxMsgTypeConstants.CALLBACK_BIZ_TYPE);
        wxMqProducer.sendMiniMessage(miniMsgVo);
    }

    /**
     * 获取异常的详细信息，包括行号
     *
     * @param e 异常
     * @return 格式化的异常信息
     */
    private String getDetailedExceptionInfo(Exception e) {
        StringBuilder stackTraceStr = new StringBuilder();
        stackTraceStr.append("错误类型: ").append(e.getClass().getName()).append("\n");
        stackTraceStr.append("错误信息: ").append(e.getMessage()).append("\n");
        stackTraceStr.append("堆栈信息: \n");

        // 获取堆栈跟踪
        StackTraceElement[] stackTrace = e.getStackTrace();
        // 只取前10个堆栈元素，避免信息过长
        int maxElements = Math.min(10, stackTrace.length);
        for (int i = 0; i < maxElements; i++) {
            StackTraceElement element = stackTrace[i];
            // 如果是项目内的类，更详细展示
            if (element.getClassName().contains("com.hnyiti")) {
                stackTraceStr.append("    at ").append(element.getClassName())
                            .append(".").append(element.getMethodName())
                            .append("(").append(element.getFileName())
                            .append(":").append(element.getLineNumber())
                            .append(") [项目内部代码]\n");
            } else {
                stackTraceStr.append("    at ").append(element.getClassName())
                            .append(".").append(element.getMethodName())
                            .append("(").append(element.getFileName())
                            .append(":").append(element.getLineNumber())
                            .append(")\n");
            }
        }

        if (stackTrace.length > maxElements) {
            stackTraceStr.append("    ... ").append(stackTrace.length - maxElements).append(" more\n");
        }

        // 设置错误信息，但不超过最大长度限制
        int maxLength = 2000;
        String result = stackTraceStr.toString();
        if (result.length() > maxLength) {
            result = result.substring(0, maxLength) + "..."; // 超出部分截断并加省略号
        }

        return result;
    }

    public String orderCallbackTypeConvert(String thirdPushType) {
        String pushType = "";
        switch (thirdPushType) {
            // 已取件、运输中
            case OrderConstants.CAINIAO_CALLBACK_SIX:   //取件成功
            case OrderConstants.CAINIAO_CALLBACK_TEN: // 取消订单
            case OrderConstants.CAINIAO_CALLBACK_EIGHT: // 支付成功
            case OrderConstants.CAINIAO_CALLBACK_TWO: // 寻求运力成功
            case OrderConstants.CAINIAO_CALLBACK_ELEVEN: // 回单成功
            case OrderConstants.CAINIAO_CALLBACK_LOGISTICS_TWO: // 运输中
            case OrderConstants.CAINIAO_CALLBACK_LOGISTICS_FOUR: // 支付成功
            case OrderConstants.CAINIAO_CALLBACK_LOGISTICS_SIX: // 待取件
            case OrderConstants.CAINIAO_CALLBACK_LOGISTICS_EIGHT:   //已签收
                pushType = KuaiDi100Constants.PUSH_TYPE_STATUS; // 状态推送
                break;
            case OrderConstants.CAINIAO_CALLBACK_SEVEN: // 核价成功
            case OrderConstants.CAINIAO_CALLBACK_NINE:  // 订单完结
                pushType = KuaiDi100Constants.PUSH_TYPE_WEIGHT; // 重量更新
                break;
            case OrderConstants.CAINIAO_CALLBACK_LOGISTICS_ONE: // 已揽件
                pushType = KuaiDi100Constants.PUSH_TYPE_COURIER; // 揽收推送
                break;
            case OrderConstants.CAINIAO_CALLBACK_ONE: // 创建成功
            case OrderConstants.CAINIAO_CALLBACK_THREE: // 更新外部快递员真实信息
            case OrderConstants.CAINIAO_CALLBACK_FOUR: // 改派成功
            case OrderConstants.CAINIAO_CALLBACK_FIVE: // 修改期望揽收截止时间
            case OrderConstants.CAINIAO_CALLBACK_LOGISTICS_FIVE: // 拒签
            case OrderConstants.CAINIAO_CALLBACK_LOGISTICS_NINE:   //转单
            case OrderConstants.CAINIAO_CALLBACK_LOGISTICS_TEN:   // 退货返回
                pushType = KuaiDi100Constants.PUSH_TYPE_CHANGE; // 订单变更
                break;
            case OrderConstants.CAINIAO_CALLBACK_LOGISTICS_THREE: // 派送中
            case OrderConstants.CAINIAO_CALLBACK_LOGISTICS_SEVEN: // 驿站派送中
                pushType = KuaiDi100Constants.PUSH_TYPE_DISPATCH; // 订单派送
                break;
        }
        return pushType;
    }

    public void callbackUpdateOrder(OrderCallbackVo callback, OrderDO orderDO, OrderCallbackBigVo orderCallbackBigVo) {
        // 状态
        String status = callback.getStatus();
        switch (status) {
            case OrderCallbackConstants.CALLBACK_STATUS_IN_TRANSIT:
                // 运输中
                handleInTransit(callback, orderDO);
                break;
            case OrderCallbackConstants.CALLBACK_STATUS_SIGN_FOR:
                // 已签收
                handleSignedFor(callback, orderDO, orderCallbackBigVo);
                break;
            case OrderCallbackConstants.CALLBACK_STATUS_RECEIVED_ORDERS:
                // 已接单
                handleReceivedOrders(callback, orderDO, orderCallbackBigVo);
                break;
            case OrderCallbackConstants.CALLBACK_STATUS_PICKED_PIECE:
                // 已取件
                handleReceived(callback, orderDO, orderCallbackBigVo);
                break;
            case OrderCallbackConstants.CALLBACK_STATUS_PICKED_SIX:
                // 异常
                handleAbnormal(callback, orderDO);
                break;
            case OrderCallbackConstants.CALLBACK_STATUS_PICKED_SEVEN:
                // 订单复活
                handleResurrection(callback, orderDO, orderCallbackBigVo);
                break;
            case OrderCallbackConstants.CALLBACK_STATUS_RECEIVING:
                // 待取件
                handlePendingParts(callback, orderDO);
                break;
            case OrderCallbackConstants.CALLBACK_STATUS_CANCELLED:
                // 已取消
                handleCancelled(callback, orderCallbackBigVo);
                break;
            case OrderCallbackConstants.CALLBACK_STATUS_WEIGHT:
                // 重量推送
                handleWeight(callback, orderDO, orderCallbackBigVo);
                break;
            case OrderCallbackConstants.CALLBACK_STATUS_COURIER:
                // 更新快递员信息
                handleCourier(callback, orderCallbackBigVo);
                break;
            case OrderCallbackConstants.CALLBACK_STATUS_WAITING_ABNORMAL:
                // 下单异常
                orderException(callback, orderCallbackBigVo);
                break;
        }
    }

    private void orderException(OrderCallbackVo callback, OrderCallbackBigVo orderCallbackBigVo) {
        String errorMessage = "下单异常，请联系管理员。";
        if(ObjectUtil.isNotEmpty(callback.getErrorMessage())){
            errorMessage = callback.getErrorMessage();
        }
        orderCallbackCommonService.pushOrderExceptionV2(orderCallbackBigVo, errorMessage);
    }

    /**
     * 运输中
     *
     * @param callback
     */
    private void handleInTransit(OrderCallbackVo callback, OrderDO orderDO) {
        OrderDO updateDO = new OrderDO();
        updateDO.setId(orderDO.getId());
        updateDO.setStatus(OrderConstants.ORDER_STATUS_IN_TRANSIT);
        // 设置订单数量变更标记为已执行
        updateDO.setExecutedOrderNum(1);

        // 修改订单状态
        OrderCallbackBigVo updateCallbackVo = new OrderCallbackBigVo();
        updateCallbackVo.setOrderInfo(updateDO);
        // 修改订单状态
        orderService.callbackUpdateOrderV2(updateCallbackVo);
        try {
            sendMqtt(orderDO.getTenantId() + "", orderDO.getUserId() + "", orderDO.getId(), orderDO.getOrderNo(), updateDO.getStatus(), orderDO.getStatus());
        } catch (Exception e) {
            log.error("发送mqtt失败，{}", e.getMessage());
        }

        /**
         * 当用户首次下单，订单运输中时，改为有效用户
         */
        try {
            if (null != orderDO.getUserId()) {
                Long userId = orderDO.getUserId();
                MemberUserRespDTO user = memberUserApi.getUser(userId);
                if (null != user) {
                    // 用户是否首单
                    boolean isFirstOrder = orderService.isFirstOrder(userId, orderDO.getId());
                    if (isFirstOrder) {
                        MemberUserRespDTO userRespDTO = new MemberUserRespDTO();
                        userRespDTO.setId(userId);
                        userRespDTO.setValidUser(2);
                        memberUserApi.updateUser(userRespDTO);
                    }

                    if (null != orderDO.getExecutedOrderNum() && orderDO.getExecutedOrderNum() == 0) {
                        MemberUserRespDTO userRespDTO = new MemberUserRespDTO();
                        userRespDTO.setId(userId);
                        userRespDTO.setOrderNum(user.getOrderNum() + 1);
                        memberUserApi.updateUser(userRespDTO);
                    }
                }
            }
        } catch (Exception e) {
            log.error("修改用户有效用户失败，{}", e.getMessage());
        }
    }

    /** j
     * 已签收
     *
     * @param callback
     */
    private void handleSignedFor(OrderCallbackVo callback, OrderDO orderDO, OrderCallbackBigVo orderCallbackBigVo) {
        OrderDO updateDO = new OrderDO();
        updateDO.setId(orderDO.getId());
        updateDO.setStatus(OrderConstants.ORDER_STATUS_SIGN_FOR);

        OrderCallbackBigVo updateCallbackVo = new OrderCallbackBigVo();
        updateCallbackVo.setOrderInfo(updateDO);
        // 修改订单状态
        orderService.callbackUpdateOrderV2(updateCallbackVo);

        try {
            sendMqtt(orderDO.getTenantId() + "", orderDO.getUserId() + "", orderDO.getId(), orderDO.getOrderNo(), updateDO.getStatus(), orderDO.getStatus());
        } catch (Exception e) {
            log.error("发送mqtt失败，{}", e.getMessage());
        }

        if (ObjectUtil.isNotEmpty(orderDO.getUserId())) {

            // 进行分佣
            getCommissionReceived(orderDO, orderCallbackBigVo);

             // 拉新会员升级
             // 1.先查询当前代理商配置的会员等级管理信息（1.需要拉几个人 2.拉新会员的类型 3.时间范围）
             // 2.找到这个订单的上级
             // 3.满足条件升级上级会员
            try {
                // 用户是否首单
                boolean isFirstOrder = orderService.isFirstOrder(orderDO.getUserId(), orderDO.getId());
                if (isFirstOrder) {
                    // 如果当前分销模式是不是模式五
                    memberUserApi.callbackUserUpgrade(orderDO.getUserId(), 2);
                }
            } catch (Exception e) {
                log.error("有效会员拉新升级上级VIP错误");
            }

            // 发送已签收通知
            try {
                String expressCompany = getKdChannelName(orderDO);
                NotifyMsgVo msgVo = new NotifyMsgVo().createOrderSigned(orderDO.getId(), orderDO.getOrderNo(), orderDO.getTenantId(), orderDO.getUserId(), expressCompany, orderDO.getCreateTime(), LocalDateTime.now());
                notifyParamService.sendNotify(NoticeGroupCodeEnum.ORDER_SIGNED.getCode(), msgVo);
            } catch (Exception e) {
                log.error("发送已签收通知失败：{}", e.getMessage());
            }
        }

        /**
         * 查看这个订单是否在调账中，如果是，则修改调账状态为无需调账
         */
        try {
            OrderAccountAdjustmentDO adjustment = orderAccountAdjustmentService.getOrderAccountAdjustmentByOrderId(orderDO.getId());
            if (null != adjustment) {
                OrderAccountAdjustmentUpdateReqVO updateReqVO = new OrderAccountAdjustmentUpdateReqVO();
                updateReqVO.setId(adjustment.getId());
                updateReqVO.setStatus("5");
                updateReqVO.setRemark(String.format("订单已签收。调账状态由{%s} 变更成-> {%s}", adjustment.getStatus(), "5"));
                orderAccountAdjustmentService.updateOrderAccountAdjustment(updateReqVO);
            }
        } catch (Exception e) {
            log.error("完结调账订单失败：{}", e.getMessage());
        }
    }

    private void getCommissionReceived(OrderDO orderDO, OrderCallbackBigVo orderCallbackBigVo) {

        // 判断当前用户是否存在上级
        MemberUserRespDTO user = memberUserApi.getUser(orderDO.getUserId());
        if (ObjectUtil.isEmpty(user) || ObjectUtil.isEmpty(user.getPromoteId())) {
            return;
        }
        // 获取上级用户信息
        MemberUserRespDTO promoteUser = memberUserApi.getUser(user.getPromoteId());
        if (ObjectUtil.isEmpty(promoteUser) || !Objects.equals(promoteUser.getLoginMode(), MemberConstants.DISTRIBUTOR_USER)) {
            // 用户的上级是新用户，不进行分佣
            return;
        }

        // 查询当前代理商活动类型
        TenantRespVO tenantRespVO = tenantApi.getTenantInfo(orderDO.getTenantId()).getData();

        if(ObjectUtil.isNotEmpty(tenantRespVO)){
            if(MemberConstants.MBR_INVITE_MARKETING_TYPE_ONE.equals(tenantRespVO.getMktType())){
                if (!(OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO.equals(orderDO.getCompensatingState()) && OrderConstants.ORDER_COMPENSATING_RESULT_TWO.equals(orderDO.getCompensatingResult()))) {
                    // 推三返一 佣金入账
                    orderMqProducer.sendRebateReported(orderDO);
                }
            } else {
                // 佣金入账
                orderCallbackCommonService.calculateCommission(orderDO, orderCallbackBigVo.getFeeUser());
            }
        }
    }

    /**
     * 已接单
     *
     * @param callback
     * @param orderDO
     */
    private void handleReceivedOrders(OrderCallbackVo callback, OrderDO orderDO, OrderCallbackBigVo orderCallbackBigVo) {
        if (OrderConstants.ORDER_STATUS_CANCELLED.equals(orderDO.getStatus())) {
            return;
        }
        OrderDO updateDO = new OrderDO();
        updateDO.setId(orderDO.getId());
        updateDO.setStatus(OrderConstants.ORDER_STATUS_RECEIVED_ORDERS);

        OrderCallbackBigVo updateCallbackVo = new OrderCallbackBigVo();

        // 组装快递员等信息
        updateCallbackVo.setOrderParcel(assembledCourierInfo(callback, orderCallbackBigVo));

        updateCallbackVo.setOrderInfo(updateDO);
        // 修改订单状态
        orderService.callbackUpdateOrderV2(updateCallbackVo);

        // 发生快递员接单通知
        try {
            if (ObjectUtil.isNotEmpty(callback.getCourierPhone())) {
                String expressCompany = getKdChannelName(orderDO);
                NotifyMsgVo orderSuccess = new NotifyMsgVo().createCourierAccepted(orderDO.getId(), orderDO.getOrderNo(), orderDO.getTenantId(), orderDO.getUserId(), expressCompany, LocalDateTime.now(), callback.getCourierName(), callback.getCourierPhone());
                notifyParamService.sendNotify(NoticeGroupCodeEnum.COURIER_ACCEPTED.getCode(), orderSuccess);
            }
        } catch (Exception e) {
            log.error("发送快递员接单通知失败：{}", e.getMessage());
        }

        try {
            sendMqtt(orderDO.getTenantId() + "", orderDO.getUserId() + "", orderDO.getId(), orderDO.getOrderNo(), updateDO.getStatus(), orderDO.getStatus());
        } catch (Exception e) {
            log.error("发送mqtt失败，{}", e.getMessage());
        }
    }

    /**
     * 异常
     *
     * @param callback
     * @param orderDO
     */
    private void handleAbnormal(OrderCallbackVo callback, OrderDO orderDO) {
        OrderDO updateDO = new OrderDO();
        updateDO.setId(orderDO.getId());
        updateDO.setStatus(OrderConstants.ORDER_STATUS_PICKED_SIX);
        OrderCallbackBigVo updateCallbackVo = new OrderCallbackBigVo();

        updateCallbackVo.setOrderInfo(updateDO);
        // 修改订单状态
        orderService.callbackUpdateOrderV2(updateCallbackVo);

        // 发送异常通知
        if (ObjectUtil.isEmpty(orderDO.getUserId())) {
            return;
        }
        try {
            NotifyMsgVo signException = new NotifyMsgVo().createSignException(orderDO.getId(), orderDO.getOrderNo(), orderDO.getTenantId(), orderDO.getUserId(), "当前订单存在异常，请前往小程序查看或联系在线客服");
            notifyParamService.sendNotify(NoticeGroupCodeEnum.SIGN_EXCEPTION.getCode(), signException);
        } catch (Exception e) {
            log.error("发送异常通知失败：{}", e.getMessage());
        }

        try {
            sendMqtt(orderDO.getTenantId() + "", orderDO.getUserId() + "", orderDO.getId(), orderDO.getOrderNo(), updateDO.getStatus(), orderDO.getStatus());
        } catch (Exception e) {
            log.error("发送mqtt失败，{}", e.getMessage());
        }
    }

    /**
     * 订单复活
     *
     * @param callback
     * @param orderDO
     */
    private void handleResurrection(OrderCallbackVo callback, OrderDO orderDO, OrderCallbackBigVo orderCallbackBigVo) {
        // 推送了金额信息
        if (ObjectUtil.isNotEmpty(callback.getFeeList()) && callback.getFeeList().size() > 0) {
            BigDecimal calcFeeWeight = null;
            if (ObjectUtil.isNotEmpty(callback.getCalcFeeWeight())) {
                calcFeeWeight = callback.getCalcFeeWeight();
            }
            // 重量推送
            pushWeightProcessing(callback, orderDO, calcFeeWeight, orderCallbackBigVo);
        } else {
            // 未推送金额信息
            orderRechargeV2(orderCallbackBigVo, OrderConstants.ORDER_STATUS_PICKED_SEVEN, OrderConstants.MODIFY_REVIVET);
        }

        if (OrderConstants.ORDER_STATUS_CANCELLED.equals(orderDO.getStatus())) {
            OrderDO updateOrderDO = new OrderDO();
            updateOrderDO.setStatus(OrderConstants.ORDER_STATUS_PICKED_SEVEN);
            updateOrderDO.setId(orderDO.getId());
            updateOrderDO.setRefundFlag(OrderConstants.ORDER_REFUND_FLAG_NO_REFUND);
            // 修改数据
            orderService.updateById(updateOrderDO);
        }
// TODO 2025-5-19 coreData服务删除，相关代码删除
//        try {
//            // 订单信息上报mq
//            orderMqProducer.sendOrderReported(orderDO, OrderConstants.ORDER_REPORTED_ADD);
//        } catch (Exception e) {
//            log.error("订单复活,上报coredata服务失败,{}", e.getMessage());
//        }

        try {
            NotifyMsgVo notifyMsgVo = new NotifyMsgVo().createSignException(orderDO.getId(),
                    orderDO.getOrderNo(), orderDO.getTenantId(), orderDO.getUserId(), "您的订单已复活");
            notifyParamService.sendNotify(NoticeGroupCodeEnum.OVERWEIGHT_SURCHARGE.getCode(), notifyMsgVo);
        } catch (Exception e) {
            log.error("发送订单复活通知失败：{}", e.getMessage());
        }

        // 然后将这条订单记录在order_polling表中删除
        try {
            OrderPollingDO orderPollingDO = new OrderPollingDO();
            orderPollingDO.setOrderId(orderDO.getId());
            orderPollingDO.setStatus(OrderConstants.ORDER_STATUS_PICKED_SEVEN);
            orderPollingService.insert(orderPollingDO);
        } catch (Exception e) {
            log.error("订单复活,删除订单轮询表数据失败,{}", e.getMessage());
        }
    }


    public Boolean orderRechargeV2(OrderCallbackBigVo orderCallbackBigVo, String status, String deliveryStatus) {
        String orderId = orderCallbackBigVo.getOrderId();
        OrderDO order = orderService.getOrder(orderId);
        OrderDO updateOrder = new OrderDO();

        FeeUserDO feeUser = orderCallbackBigVo.getFeeUser();
        if (ObjectUtil.isEmpty(feeUser) || ObjectUtil.isEmpty(feeUser.getPayAmount()) || ObjectUtil.isEmpty(feeUser.getLastPayAmount())) {
            return false;
        }

        updateOrder.setId(orderId);

        // 查询费用明细
        List<OrderFeeDO> orderFeeDOList = orderFeeService.byOrderIdQuery(orderId);
        Boolean isOverweight = false;
        if (ObjectUtil.isNotEmpty(orderFeeDOList)) {
            BigDecimal money = BigDecimal.ZERO;
            for (OrderFeeDO orderFeeDO : orderFeeDOList) {
                money = money.add(ObjectUtil.isNotEmpty(orderFeeDO.getAmount()) ? orderFeeDO.getAmount() : BigDecimal.ZERO);
            }
            if (feeUser.getPayAmount().compareTo(BigDecimal.ZERO) > 0) {
                money = money.subtract(feeUser.getPayAmount());
                updateOrder.setCompensationPaid0(BigDecimal.ZERO);
            }
            if (money.compareTo(BigDecimal.ZERO) > 0) {
                updateOrder.setCompensationAmount(money);
                updateOrder.setCompensatingResult(OrderConstants.ORDER_COMPENSATING_RESULT_TWO);
                updateOrder.setCompensatingState(OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO);
                isOverweight = true;
            } else {
                updateOrder.setCompensationAmount(money);
                isOverweight = false;
            }
        } else {
            BigDecimal compensationAmount = (ObjectUtil.isNotEmpty(feeUser.getLastPayAmount()) && feeUser.getLastPayAmount().compareTo(BigDecimal.ZERO) > 0)
                    ? feeUser.getLastPayAmount()
                    : feeUser.getLastPrice();

            updateOrder.setCompensationAmount(compensationAmount);
            updateOrder.setCompensatingResult(OrderConstants.ORDER_COMPENSATING_RESULT_TWO);
            updateOrder.setCompensatingState(OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO);
            isOverweight = true;
        }
        // 修改总对象
        OrderCallbackBigVo updateCallbackBig = new OrderCallbackBigVo();

        updateCallbackBig.setOrderInfo(updateOrder);

        // 更新相关信息
        orderService.callbackUpdateOrderV2(updateCallbackBig);

        // 扣除加盟商钱
        if (ThirdParamsSourceTypeEnum.HEADQUARTERS.getValue().equals(order.getSourceType())) {
            if (ObjectUtil.isNotEmpty(orderCallbackBigVo) && ObjectUtil.isNotEmpty(orderCallbackBigVo.getFeeTenant().getCost())) {
                // 修改了order.getTenantPayAmount()传参，订单复活取该值为正数，实际应该取相反的负数
                tenantAmountApi.deductBalance(orderCallbackBigVo.getOrderInfo().getTenantId(), orderCallbackBigVo.getFeeTenant().getCost().negate(), Long.parseLong(orderId), TenantAmountConstants.TENANT_AMOUNT_MODE_FOUR, "订单复活重新计费-" + orderId);
            }
        }
        return isOverweight;
    }

    /**
     * 待取件
     *
     * @param callback
     * @param orderDO
     */
    private void handlePendingParts(OrderCallbackVo callback, OrderDO orderDO) {
        OrderDO updateDO = new OrderDO();
        updateDO.setId(orderDO.getId());
        updateDO.setStatus(OrderConstants.ORDER_STATUS_RECEIVING);
        // 修改订单状态
//        orderService.callbackUpdateOrder(updateDO);

        OrderCallbackBigVo updateCallbackVo = new OrderCallbackBigVo();

        updateCallbackVo.setOrderInfo(updateDO);
//        // 修改订单状态
        orderService.callbackUpdateOrderV2(updateCallbackVo);

        try {
            sendMqtt(orderDO.getTenantId() + "", orderDO.getUserId() + "", orderDO.getId(), orderDO.getOrderNo(), updateDO.getStatus(), orderDO.getStatus());
        } catch (Exception e) {
            log.error("发送mqtt失败，{}", e.getMessage());
        }
    }

    /**
     * 已取消
     *
     * @param callback
     * @param orderCallbackBigVo
     */
    private void handleCancelled(OrderCallbackVo callback, OrderCallbackBigVo orderCallbackBigVo) {
//        if (!"1".equals(orderCallbackBigVo.getOrderInfo().getRefundFlag())) {
//            orderCallbackCommonService.pushOrderCancelV2(orderCallbackBigVo);
//        }
        orderCallbackCommonService.pushOrderCancelV3(orderCallbackBigVo);

        // 第三方推送订单"已取消"，需要将这条订单记录到order_polling(轮询表)中
        OrderDO orderDO = orderCallbackBigVo.getOrderInfo();
        /*try {
            orderPollingService.insert(new OrderPollingDO().setOrderId(orderDO.getId()).setStatus(OrderConstants.ORDER_STATUS_CANCELLED));
        } catch (Exception e) {
            log.error("记录订单：{} 数据到轮询表失败", JsonUtils.toJsonString(orderCallbackBigVo));
        }*/

        // 发送订单取消消息
        if (ObjectUtil.isEmpty(orderDO.getUserId())) {
            return;
        }
        try {
            OrderParcelDO orderParcel = orderCallbackBigVo.getOrderParcel();
            if (ObjectUtil.isNotEmpty(orderDO) && ObjectUtil.isNotEmpty(orderParcel) && ObjectUtil.isNotEmpty(orderDO.getUserId())) {
                String reason = "取消订单";
                if (ObjectUtil.isNotEmpty(orderParcel.getCancelFlag()) && orderParcel.getCancelFlag() && ObjectUtil.isNotEmpty(orderParcel.getReasonId())) {
                    reason = reasonParamApi.queryParamNameByCode(orderParcel.getReasonId());
                }
                String expressCompany = getKdChannelName(orderDO);
                NotifyMsgVo orderCanceled = new NotifyMsgVo().createOrderCanceled(orderDO.getId(), orderDO.getOrderNo(), orderDO.getTenantId(), orderDO.getUserId(), orderDO.getCreateTime(), orderParcel.getCancelTime(), reason, expressCompany);
                notifyParamService.sendNotify(NoticeGroupCodeEnum.ORDER_CANCELED.getCode(), orderCanceled);
            }
        } catch (Exception e) {
            log.error("发送订单取消通知发送失败：{}", e.getMessage());
        }
    }

    /**
     * 更新重量信息
     *
     * @param callback
     * @param orderDO
     */
    private void handleWeight(OrderCallbackVo callback, OrderDO orderDO, OrderCallbackBigVo orderCallbackBigVo) {
        /**
         * 第三方现在存在一个订单一个类型多次推送的情况
         * 这里增加一段逻辑，加了一个表标记一个订单是否不处理他的第三方回调
         * 加在这里就只不处理订单的超重回调
         */
        Long count = orderFilterService.orderIfFilter(orderCallbackBigVo.getOrderId());
        if (count == 0L) {
            // 重量推送
            pushWeightProcessing(callback, orderDO, callback.getCalcFeeWeight(), orderCallbackBigVo);
            // 更新订单相关信息
//            syncOrderFee(orderDO.getId(), callback, callback.getFeeList());
        }
    }


    public void queryExlPriceOld(Long excelItemId, BigDecimal calcFeeWeight, List<OrderFeeBaseVO> orderFeeList) {
        if (ObjectUtil.isEmpty(calcFeeWeight)) {
            return;
        }
        if (ObjectUtil.isEmpty(orderFeeList)) {
            return;
        }
        if (ObjectUtil.isEmpty(excelItemId)) {
            return;
        }
        // 查询渠道定价
        ExcelItemDO excelItemDO = excelItemService.getExeclItem(excelItemId);

        for (OrderFeeBaseVO bean : orderFeeList) {
            if (ObjectUtil.isEmpty(bean.getType())) {
                continue;
            }
            if (bean.getType().equals(OrderConstants.ORDER_FEE_TYPE_FREIGHT)) {
                BigDecimal price = bean.getAmount();
                // 计算价格
                if (calcFeeWeight.compareTo(BigDecimal.ZERO) > 0) {
                    // 续重
                    BigDecimal continueWeight = calcFeeWeight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                    price = excelItemDO.getFirstPrice();
                    if (continueWeight.compareTo(BigDecimal.ZERO) > 0) {
                        price = price.add(continueWeight.multiply(excelItemDO.getContinuedPrice())).setScale(2, RoundingMode.UP);
                    }
                }
                bean.setAmount(price);
            }
        }
    }


    public boolean isWeightInRange(ExcelItemDO item, BigDecimal calcFeeWeight) {
        // 如果 minWeight 或 maxWeight 为空，则无法判断
        if (item.getMinWeight() == null || item.getMaxWeight() == null) {
            return true;
        }
        // 判断 minWeight <= calcFeeWeight <= maxWeight
        return calcFeeWeight.compareTo(item.getMinWeight()) >= 0 && calcFeeWeight.compareTo(item.getMaxWeight()) <= 0;
    }

    public String removeCity(String area) {
        return ProvinceMap.findProvince(area);
    }


    /**
     * 支付exl 不同重量不同续重，加重
     *
     * @param excelItemId
     * @param weight
     * @param orderFeeList
     * @param orderId
     */
    public void queryExlPrice(Long excelItemId, BigDecimal weight, List<OrderFeeBaseVO> orderFeeList, OrderDO updateOrder, String orderId) {
        if (ObjectUtil.isEmpty(weight)) {
            return;
        }
        if (ObjectUtil.isEmpty(orderFeeList)) {
            return;
        }
        if (ObjectUtil.isEmpty(excelItemId)) {
            return;
        }
        BigDecimal calcFeeWeight = weight.setScale(0, RoundingMode.CEILING);


        // 查询渠道定价
        ExcelItemDO excelItemDO = excelItemService.getExeclItem(excelItemId);

        // 判断是否在当前exl定价的区间
        if (!isWeightInRange(excelItemDO, calcFeeWeight)) {
            // 查询寄收信息
            OrderContactsDO senderContacts = orderContactsService.getOrderContactsByOrderId(orderId, "sender");
            OrderContactsDO recevierContacts = orderContactsService.getOrderContactsByOrderId(orderId, "receive");
            // 查询快递exl价格
            ExcelItemDO nowExcelItemDO = excelItemService.getExeclItemOne(removeCity(senderContacts.getProvince()), removeCity(recevierContacts.getProvince()), calcFeeWeight, excelItemDO.getExeclId());
            if (ObjectUtil.isNotEmpty(nowExcelItemDO) && ObjectUtil.isNotEmpty(updateOrder)) {
                excelItemDO = nowExcelItemDO;
                updateOrder.setExcelItemId(excelItemDO.getId());
            }
        }


        for (OrderFeeBaseVO bean : orderFeeList) {
            if (ObjectUtil.isEmpty(bean.getType())) {
                continue;
            }
            if (bean.getType().equals(OrderConstants.ORDER_FEE_TYPE_FREIGHT)) {
                BigDecimal price = bean.getAmount();
                // 计算价格
                if (calcFeeWeight.compareTo(BigDecimal.ZERO) > 0) {
                    // 续重
                    BigDecimal continueWeight = calcFeeWeight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                    price = excelItemDO.getFirstPrice();
                    if (continueWeight.compareTo(BigDecimal.ZERO) > 0) {
                        price = price.add(continueWeight.multiply(excelItemDO.getContinuedPrice())).setScale(2, RoundingMode.UP);
                    }
                }
                bean.setAmount(price);
            }
        }


    }

    public void calculateSurcharge(OrderDO orderDO, OrderCallbackVo callbackVo, BigDecimal calcFeeWeight) {
        if (ObjectUtil.isEmpty(orderDO)) {
            return;
        }

        // 查询当前渠道是否配置加价
        KdChannelDO kdChannelDO = kdChannelService.getChannel(orderDO.getChannelExpressId());
        if (ObjectUtil.isEmpty(kdChannelDO) || ObjectUtil.isEmpty(kdChannelDO.getSurchargePriceId())) {
            return;
        }
        if (ObjectUtil.isEmpty(callbackVo)) {
            return;
        }
        if (ObjectUtil.isEmpty(callbackVo.getFeeList())) {
            return;
        }
        ChannelPriceDO channelPrice = channelPriceService.getChannelPrice(kdChannelDO.getSurchargePriceId());
        if (ObjectUtil.isEmpty(channelPrice)) {
            return;
        }
        if (ObjectUtil.isEmpty(channelPrice.getFirstPrice()) || ObjectUtil.isEmpty(channelPrice.getContinuePrice())) {
            return;
        }
        // 0 不加价 1 渠道加价 2 官方折扣 -- 都不需要进行加价计算
        if (null != channelPrice.getContinuePrice() && kdChannelDO.getSurchargePriceId() > 2) {

            List<OrderFeeBaseVO> orderFeeBaseList = callbackVo.getFeeList();

            String ResourceFeeSkipSwitch = paramApi.getResourceFeeSkipSwitch(orderDO.getTenantId());
            if (ObjectUtil.isEmpty(ResourceFeeSkipSwitch) || "0".equals(paramApi.getResourceFeeSkipSwitch(orderDO.getTenantId()))
                    && OrderConstants.ORDER_CHANNEL_KUAIDI100.equals(orderDO.getChannel())) {
                // 把附加费去除
                if (ObjectUtil.isNotEmpty(orderFeeBaseList)) {
                    orderFeeBaseList.removeIf(orderFeeBase -> orderFeeBase.getType().equals("11"));
                }
            }

            // 计算附加费
            BigDecimal lastPrice = channelPrice.getFirstPrice();
            if (calcFeeWeight.compareTo(BigDecimal.ONE) > 0) {
                // 续重
                BigDecimal continueWeight = calcFeeWeight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
                lastPrice = lastPrice.add(continueWeight.multiply(channelPrice.getContinuePrice()));
            }

            OrderFeeBaseVO otherFee = new OrderFeeBaseVO();
            otherFee.setType("11");
            otherFee.setAmount(lastPrice);
            otherFee.setName("附加费");
            orderFeeBaseList.add(otherFee);

            callbackVo.setFeeList(orderFeeBaseList);
        }
    }

    /**
     * 推送重量处理
     *
     * @param callbackVo
     * @param orderDO
     * @param calcFeeWeight
     * @return
     */
    public void pushWeightProcessing(OrderCallbackVo callbackVo, OrderDO orderDO, BigDecimal calcFeeWeight, OrderCallbackBigVo orderCallbackBigVo) {
        try {
            List<OrderFeeBaseVO> orderFeeList = callbackVo.getFeeList();

            // 修改总对象
            OrderCallbackBigVo updateCallbackBig = new OrderCallbackBigVo();


            String orderId = orderDO.getId();
            OrderDO updateOrder = new OrderDO();
            updateOrder.setId(orderId);

            // 如果用的exl查价，快递运费重新计算
            if (ObjectUtil.isNotEmpty(orderDO.getExcelItemId())) {
                queryExlPrice(orderDO.getExcelItemId(), calcFeeWeight, orderFeeList, updateOrder, orderDO.getId());
            }

            // 如果费用明细里面包含附加费的话，单独处理
            if (ObjectUtil.isNotEmpty(orderDO.getChannelExpressId())) {
                try {
                    // 计算附加费
                    calculateSurcharge(orderDO, callbackVo, calcFeeWeight);
                } catch (Exception e) {
                    log.error("附加费计算失败:{}", e.getMessage());
                }

            }


            // 计算费用价格
            BigDecimal deliveryPrice = null;

            UpdateFeesRepVO updateFeesRepVO = new UpdateFeesRepVO();
            // 查询是老数据，还是新数据
            if (ObjectUtil.isNotEmpty(orderDO.getTenantChannelId())) {
                // 新接口
                deliveryPrice = orderCalcService.calcCallbackFeeNewV2(orderFeeList, updateFeesRepVO, orderDO, calcFeeWeight);
            } else {
                // 老接口
                deliveryPrice = orderCalcService.calcCallbackFeeV2(orderFeeList, updateFeesRepVO, orderDO, calcFeeWeight);
            }
            if (ObjectUtil.isEmpty(deliveryPrice)) {
                return;
            }
            // 应收金额
//        updateOrder.setReceivableAmount(deliveryPrice);
            // 计算超轻，还是超重
            String status = this.weightChange(orderId, deliveryPrice, orderCallbackBigVo.getFeeUser(), updateOrder);
            // 计算加盟商补差金额
            updateFeesRepVO.setCompensationAmount(updateOrder.getCompensationAmount());
            updateFeesRepVO.setCompensatingState(status);
            BigDecimal differenceAmount = countTenantAmount(orderCallbackBigVo, updateFeesRepVO, orderId);

            if (ThirdParamsSourceTypeEnum.HEADQUARTERS.getValue().equals(orderDO.getSourceType())
                    || ObjectUtil.isEmpty(orderDO.getSourceType())) {
                if (BigDecimal.ZERO.compareTo(differenceAmount) > 0) {
                    orderProcessService.orderTenantDeduct(orderId,Long.parseLong(orderId),differenceAmount.abs(),TenantAmountConstants.TENANT_AMOUNT_MODE_FIVE,"订单超重补差-" + orderId);
//            tenantAmountApi.deductBalance(tenantId, differenceAmount, Long.parseLong(orderId), TenantAmountConstants.TENANT_AMOUNT_MODE_FIVE, "订单超重补差-" + orderId);
                }
                if (BigDecimal.ZERO.compareTo(differenceAmount) < 0) {
                    orderProcessService.orderTenantRefund(orderId,Long.parseLong(orderId),differenceAmount.abs(),TenantAmountConstants.TENANT_AMOUNT_MODE_SIX,"订单超轻退款-" + orderId);
//            tenantAmountApi.deductBalance(tenantId, differenceAmount, Long.parseLong(orderId), TenantAmountConstants.TENANT_AMOUNT_MODE_SIX, "订单超轻退款-" + orderId);
                }
            }

            // 20241113 新的修改方法
            updateCallbackBig.setOrderInfo(updateOrder);
            //超重补差逻辑处理
            if (OrderConstants.ORDER_STATUS_OVERWEIGHT_COMPENSATION.equals(status)) {
                // 超重补差逻辑
                BigDecimal realWeight = callbackVo.getRealWeight();
                overweightCompensation(orderCallbackBigVo, updateCallbackBig, updateFeesRepVO,realWeight);
            } else if (OrderConstants.ORDER_STATUS_REFUND.equals(status)) {
                // 超轻退款逻辑
                ultraLight(orderCallbackBigVo, updateCallbackBig, updateFeesRepVO, calcFeeWeight);
            } else {
                // 传递正常价格逻辑
                assembledUpdateFeesInfo(orderCallbackBigVo, updateFeesRepVO, updateCallbackBig);
            }
            // 组装订单相关信息
            syncOrderFee(callbackVo, orderCallbackBigVo, updateCallbackBig);

            // 更新相关信息
            orderService.callbackUpdateOrderV2(updateCallbackBig);


            // 当前状态是超重未补，推送后是超轻或者正常
            if (!OrderConstants.ORDER_STATUS_OVERWEIGHT_COMPENSATION.equals(status)
                    && OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO.equals(orderDO.getCompensatingState())
                    && OrderConstants.ORDER_COMPENSATING_RESULT_TWO.equals(orderDO.getCompensatingResult())) {
                removeBlacklist(orderDO);
            }
        } catch (Exception e) {
            log.error("*******重量推送执行异常*******",e);
            throw new ServiceException(new ErrorCode(404,e.toString()));
        }
    }


    /**
     * 超重未补的订单
     * 回调过来去校验这个订单是否黑名单，如果这个订单更新成正常订单那么就要去取消黑名单拉黑
     *
     * @param order
     */
    private void removeBlacklist(OrderDO order) {
        // 开启了自动解除拉黑
        try {
            Integer mParam = paramApi.getBlacklistMParam(order.getTenantId());
            if (mParam != null && mParam == 0) {
                // 创建一个日志对象
                UserFreezeLogCreateReqVO logCreateReqVO = new UserFreezeLogCreateReqVO();
                logCreateReqVO.setOrderNo(order.getOrderNo());
                logCreateReqVO.setOrderId(order.getId());
                logCreateReqVO.setUserId(order.getUserId());
                logCreateReqVO.setRemark(UserFreezeLogEnum.CHECK_NOT_OVERWEIGHT.getRemark());
                logCreateReqVO.setType(UserFreezeLogEnum.CHECK_NOT_OVERWEIGHT.getValue());

                userFreezeService.differenceDelete(order.getOrderNo(), logCreateReqVO);
            }
        } catch (Exception e) {
            log.error("超重未补订单新重量推送解除黑名单绑定失败。订单信息：{}，错误信息：{}", JsonUtils.toJsonString(order), e.getMessage());
        }
    }

    @Resource
    private OrderFilterService orderFilterService;

    /**
     * 超重补差逻辑
     */
    private void overweightCompensation(OrderCallbackBigVo orderCallbackBigVo, OrderCallbackBigVo updateCallbackBig, UpdateFeesRepVO updateFeesRepVO, BigDecimal realWeight) {
        String orderId = orderCallbackBigVo.getOrderId();

        // 组装费用信息
        assembledUpdateFeesInfo(orderCallbackBigVo, updateFeesRepVO, updateCallbackBig);

        // 当订单的状态为超重、未补，且订单分佣的状态不是已分佣或订单取消的状态
        if (OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO.equals(updateFeesRepVO.getCompensatingState()) && OrderConstants.ORDER_COMPENSATING_RESULT_TWO.equals(updateFeesRepVO.getCompensatingState())) {
            // 修改分佣数据
            updateSubCommission(orderId);
        }

        // 获取数据
        OrderDO order = orderCallbackBigVo.getOrderInfo();
        BigDecimal payAmount = orderCallbackBigVo.getFeeUser().getPayAmount(); // 已付运费
        BigDecimal lastPayAmount = updateCallbackBig.getFeeUser().getLastPayAmount(); // 实际运费
        BigDecimal overweightAmount = updateCallbackBig.getFeeUser().getOverweightAmount(); // 补差费用

        if (ObjectUtil.isEmpty(order.getUserId())) {
            return;
        }

        MemberUserRespDTO user = memberUserApi.getUser(order.getUserId());
        try {
            // 发送补差消息
            String kdChannelName = getKdChannelName(order);
            NotifyMsgVo overweightSurcharge = new NotifyMsgVo().createOverweightSurcharge(orderId, order.getOrderNo(), order.getTenantId(), order.getUserId(), kdChannelName, realWeight, lastPayAmount, payAmount, overweightAmount, user.getMobile());
            notifyParamService.sendNotify(NoticeGroupCodeEnum.OVERWEIGHT_SURCHARGE.getCode(), overweightSurcharge);
        } catch (Exception e) {
            log.error("发送补差通知失败：{}", e.getMessage());
        }


        // 发送企微通知

        try {
            TenantRespVO tenantRespVO = tenantApi.getTenantInfo(order.getTenantId()).getData();
            String pushContent = String.format(
                    "运单号：[%s]超重待补差金额：%s元",
                    order.getOrderNo(), overweightAmount
            );
            // 改用发送mq消息处理超重企业微信群通知
            WeChatGroupNotifyReq weChatGroupNotifyReq = new WeChatGroupNotifyReq();
            weChatGroupNotifyReq.setAppName(tenantRespVO.getName());
            weChatGroupNotifyReq.setPushType(WeChatGroupMessageTypeEnum.WEIGHT_NOTIFY.getCode());
            weChatGroupNotifyReq.setExecuteMethod(WeChatGroupMessageTypeEnum.WEIGHT_NOTIFY.getCode());
            weChatGroupNotifyReq.setPushContent(pushContent);
            weChatGroupNotifyReq.setTenantId(order.getTenantId());
            orderMqProducer.orderOverweightNotifyOutput(weChatGroupNotifyReq);
        } catch (Exception e) {
            log.error("发送补差企微通知失败：{}", e.getMessage());
        }

    }

    /**
     * 组装修改费用信息
     */
    private void assembledUpdateFeesInfo(OrderCallbackBigVo orderCallbackBigVo, UpdateFeesRepVO updateFeesRepVO, OrderCallbackBigVo updateBigVo) {
        String orderId = updateFeesRepVO.getOrderId();
//        OrderBigVo bigVo = new OrderBigVo();

        // 集团费用
        assembleUpdateFeeSuper(orderCallbackBigVo, updateFeesRepVO, updateBigVo);

        // 组装加盟商费用
        assembleUpdateFeeTenant(orderCallbackBigVo, updateFeesRepVO, updateBigVo);

        // 组装用户费用信息
        assembleUpdateFeeUser(orderCallbackBigVo, updateFeesRepVO, updateBigVo);

        // 修改数据
//        updateTotalFees(bigVo);
    }

    /**
     * 修改费用信息
     */
    public void updateTotalFees(OrderBigVo bigVo) {
        if (ObjectUtil.isEmpty(bigVo)) {
            return;
        }

        if (ObjectUtil.isNotEmpty(bigVo.getFeeUser())) {
            // 修改用户费用信息
            FeeUserUpdateReqVO updateReqVO = new FeeUserUpdateReqVO();
            BeanUtil.copyProperties(bigVo.getFeeUser(), updateReqVO);
            feeUserService.updateFeeUser(updateReqVO);
        }

        if (ObjectUtil.isNotEmpty(bigVo.getFeeTenant())) {
            // 保存代理费用
            FeeTenantUpdateReqVO updateReqVO = new FeeTenantUpdateReqVO();
            BeanUtil.copyProperties(bigVo.getFeeTenant(), updateReqVO);
            feeTenantService.updateFeeTenant(updateReqVO);
        }

        if (ObjectUtil.isNotEmpty(bigVo.getFeeSuper())) {
            // 保存总部费用
            FeeSuperUpdateReqVO updateReqVO = new FeeSuperUpdateReqVO();
            BeanUtil.copyProperties(bigVo.getFeeSuper(), updateReqVO);

            // 封装两个ES需要的参数
            updateReqVO.setOrderNo(bigVo.getOrderInfo().getOrderNo());
            updateReqVO.setChannel(bigVo.getOrderInfo().getChannel());

            feeSuperService.updateFeeSuper(updateReqVO);
        }
    }


    // 组装集团商费用信息
    private void assembleUpdateFeeSuper(OrderCallbackBigVo orderCallbackBigVo, UpdateFeesRepVO updateFeesRepVO, OrderCallbackBigVo updateBigVo) {
        String orderId = orderCallbackBigVo.getOrderId();
//        bigVo.setOrderInfo(order);

        // 查询是否存在
        FeeSuperDO feeSuperDO = orderCallbackBigVo.getFeeSuper();
        if (ObjectUtil.isEmpty(feeSuperDO)) {
            return;
        }
        Boolean isUpdate = false;
        FeeSuperDO updateFeeSuperDO = new FeeSuperDO();
        updateFeeSuperDO.setOrderId(orderId);

//        updateFeeSuperDO.setId(feeSuperDO.getId());

        // 加盟商支付金额
        BigDecimal tenantDueAmount = updateFeesRepVO.getTenantPayAmount();
        // 成本
        BigDecimal price = updateFeesRepVO.getPrice();

        // 计算补差金额
        BigDecimal compensationAmount = calculateCompensationAmount(feeSuperDO.getCost(), updateFeesRepVO.getPrice());

        if (compensationAmount.compareTo(BigDecimal.ZERO) > 0) {
            System.out.println("补偿金额大于0");
            isUpdate = true;
            updateFeeSuperDO.setLightweightAmount(BigDecimal.ZERO);
            updateFeeSuperDO.setOverweightAmount(compensationAmount);
        } else if (compensationAmount.compareTo(BigDecimal.ZERO) == 0) {
//            updateFeeSuperDO.setLightweightAmount(BigDecimal.ZERO);
//            updateFeeSuperDO.setOverweightAmount(BigDecimal.ZERO);
        } else {
            System.out.println("补偿金额小于0");
            isUpdate = true;
            updateFeeSuperDO.setLightweightAmount(compensationAmount.abs());
            updateFeeSuperDO.setOverweightAmount(BigDecimal.ZERO);
        }

        // 集团费用
        if (ObjectUtil.isNotEmpty(tenantDueAmount)) {
            isUpdate = true;
            updateFeeSuperDO.setDueAmount(tenantDueAmount);
        }
        if (ObjectUtil.isNotEmpty(price)) {
            isUpdate = true;
            updateFeeSuperDO.setCost(price);
        }
        // 计算加盟商利润
        if (ObjectUtil.isNotEmpty(price) && ObjectUtil.isNotEmpty(tenantDueAmount)) {
            isUpdate = true;
            updateFeeSuperDO.setProfitAmount(tenantDueAmount.subtract(price));
        }
        if (isUpdate) {
            updateBigVo.setFeeSuper(updateFeeSuperDO);
        }
    }


    /**
     * 计算费用补差金额
     *
     * @param oldCost 老的成本价
     * @param newCost 新的成本价
     * @return
     */
    private BigDecimal calculateCompensationAmount(BigDecimal oldCost, BigDecimal newCost) {
        BigDecimal compensationAmount = BigDecimal.ZERO;
        if (ObjectUtil.isEmpty(oldCost) || ObjectUtil.isEmpty(newCost)) {
            return compensationAmount;
        }
        // 待补差金额 ，如果是负数的话，是补差金额，如果是正数，是退款金额。
        compensationAmount = newCost.subtract(oldCost);

        return compensationAmount;
    }


    // 组装修改加盟商费用信息
    private void assembleUpdateFeeTenant(OrderCallbackBigVo orderCallbackBigVo, UpdateFeesRepVO updateFeesRepVO, OrderCallbackBigVo updateBigVo) {
        // 查询是否存在
        FeeTenantDO feeTenantDO = orderCallbackBigVo.getFeeTenant();
        if (ObjectUtil.isEmpty(feeTenantDO)) {
            return;
        }

        String orderId = orderCallbackBigVo.getOrderId();

        Boolean isUpdate = false;

        FeeTenantDO updateFeeTenantDO = new FeeTenantDO();

        updateFeeTenantDO.setOrderId(orderId);

//        updateFeeTenantDO.setId(feeTenantDO.getId());
        // 加盟商支付金额
        BigDecimal tenantDueAmount = updateFeesRepVO.getTenantPayAmount();
        // 用户最终价格
        BigDecimal lastPrice = updateFeesRepVO.getLastPrice();
        // 成本
        BigDecimal price = updateFeesRepVO.getPrice();

        // 计算补差金额
        BigDecimal compensationAmount = calculateCompensationAmount(feeTenantDO.getCost(), updateFeesRepVO.getPrice());

        if (compensationAmount.compareTo(BigDecimal.ZERO) > 0) {
            isUpdate = true;
            System.out.println("补偿金额大于0");
            updateFeeTenantDO.setLightweightAmount(BigDecimal.ZERO);
            updateFeeTenantDO.setOverweightAmount(compensationAmount);
        } else if (compensationAmount.compareTo(BigDecimal.ZERO) == 0) {
//            feeTenantDO.setLightweightAmount(BigDecimal.ZERO);
//            feeTenantDO.setOverweightAmount(BigDecimal.ZERO);
        } else {
            System.out.println("补偿金额小于0");
            isUpdate = true;
            updateFeeTenantDO.setLightweightAmount(compensationAmount.abs());
            updateFeeTenantDO.setOverweightAmount(BigDecimal.ZERO);
        }

        // 加盟商费用
        if (ObjectUtil.isNotEmpty(lastPrice)) {
            isUpdate = true;
            updateFeeTenantDO.setDueAmount(lastPrice);
        }
        if (ObjectUtil.isNotEmpty(tenantDueAmount)) {
            isUpdate = true;
            // 疑点：此处存放的是加盟商的成本价还是总部的？
            updateFeeTenantDO.setCost(tenantDueAmount);
        }

        // 计算加盟商利润
        if (ObjectUtil.isNotEmpty(tenantDueAmount) && ObjectUtil.isNotEmpty(lastPrice)) {
            isUpdate = true;
//            updateFeeTenantDO.setProfitAmount(lastPrice.subtract(tenantDueAmount));
            BigDecimal payAmount = lastPrice;
            if (ObjectUtil.isNotEmpty(orderCallbackBigVo.getFeeUser()) && ObjectUtil.isNotEmpty(orderCallbackBigVo.getFeeUser().getPayAmount())) {
                payAmount = orderCallbackBigVo.getFeeUser().getPayAmount();
            }
            updateFeeTenantDO.setProfitAmount(getProfitAmount(payAmount, tenantDueAmount
                    , ObjectUtil.isNotEmpty(orderCallbackBigVo.getFeeUser()) ? orderCallbackBigVo.getFeeUser().getCouponCost() : null));
        }

        if (isUpdate) {
            updateBigVo.setFeeTenant(updateFeeTenantDO);
        }
    }


    /**
     * @param payAmount  支付金额
     * @param orderCost  订单成本
     * @param couponCost 优惠券成本
     * @return
     */
    public BigDecimal getProfitAmount(BigDecimal payAmount, BigDecimal orderCost, BigDecimal couponCost) {

        BigDecimal profitAmount = payAmount.subtract(orderCost);

        // 加上优惠券成本
        if (ObjectUtil.isNotEmpty(couponCost)) {
            profitAmount = profitAmount.add(couponCost);
        }

        return profitAmount;
    }


    /**
     * 计算费用补差金额
     *
     * @param oldCost      老的成本价
     * @param newCost      新的成本价
     * @param reliefAmount 减免金额
     * @return
     */
    private BigDecimal calculateCompensationAmount(BigDecimal oldCost, BigDecimal newCost, BigDecimal reliefAmount) {
        BigDecimal compensationAmount = BigDecimal.ZERO;
        if (ObjectUtil.isEmpty(oldCost) || ObjectUtil.isEmpty(newCost)) {
            return compensationAmount;
        }

        // 如果有减免金额的话，需要加上减免金额
        if (ObjectUtil.isNotEmpty(reliefAmount)) {
            oldCost = oldCost.add(reliefAmount);
        }
        // 待补差金额 ，如果是负数的话，是补差金额，如果是正数，是退款金额。
        compensationAmount = newCost.subtract(oldCost);


        return compensationAmount;
    }


    // 组装修改用户费用信息
    private void assembleUpdateFeeUser(OrderCallbackBigVo orderCallbackBigVo, UpdateFeesRepVO updateFeesRepVO, OrderCallbackBigVo updateBigVo) {
        // 查询是否存在
        FeeUserDO feeUserDO = orderCallbackBigVo.getFeeUser();
        if (ObjectUtil.isEmpty(feeUserDO)) {
            return;
        }
        Boolean isUpdate = false;

        FeeUserDO updateFeeUser = new FeeUserDO();
        updateFeeUser.setOrderId(orderCallbackBigVo.getOrderId());

        BigDecimal lastPrice = updateFeesRepVO.getLastPrice();
        // 首重价格
        String defFirstPrice = updateFeesRepVO.getDefFirstPrice();
        // 续重价格
        String defOverPrice = updateFeesRepVO.getDefOverPrice();
        // 计算补差金额
        BigDecimal compensationAmount = calculateCompensationAmount(feeUserDO.getPayAmount(), updateFeesRepVO.getLastPrice(), feeUserDO.getReliefAmount());

        if (compensationAmount.compareTo(BigDecimal.ZERO) > 0) {
            System.out.println("补偿金额大于0");
            isUpdate = true;
            updateFeeUser.setLightweightAmount(BigDecimal.ZERO);
            updateFeeUser.setOverweightAmount(compensationAmount);
        } else if (compensationAmount.compareTo(BigDecimal.ZERO) == 0) {
//            feeTenantDO.setLightweightAmount(BigDecimal.ZERO);
//            feeTenantDO.setOverweightAmount(BigDecimal.ZERO);
        } else {
            // 如果已支付金额小于 超轻金额，则只退  已支付的钱
            BigDecimal lightweightAmount = compensationAmount.abs();
            if (ObjectUtil.isNotEmpty(feeUserDO.getPayAmount()) && lightweightAmount.compareTo(feeUserDO.getPayAmount()) > 0) {
                lightweightAmount = feeUserDO.getPayAmount();
            }

            System.out.println("补偿金额小于0");
            isUpdate = true;
            updateFeeUser.setLightweightAmount(lightweightAmount);
            updateFeeUser.setOverweightAmount(BigDecimal.ZERO);
        }

        // 用户费用信息
        if (ObjectUtil.isNotEmpty(updateFeesRepVO.getWeight())) {
            isUpdate = true;
            updateFeeUser.setWeight(updateFeesRepVO.getWeight());
        }
        if (ObjectUtil.isNotEmpty(lastPrice)) {
            isUpdate = true;
            updateFeeUser.setLastPayAmount(lastPrice);
        }
        if (ObjectUtil.isNotEmpty(defFirstPrice)) {
            isUpdate = true;
            updateFeeUser.setFirstAmount(new BigDecimal(defFirstPrice));
        }
        if (ObjectUtil.isNotEmpty(defOverPrice)) {
            isUpdate = true;
            updateFeeUser.setRenewalAmount(new BigDecimal(defOverPrice));
        }
        if (isUpdate) {
            updateBigVo.setFeeUser(updateFeeUser);
        }
    }

//    /**
//     * 费用数据转换
//     * @return
//     */
//    public UpdateFeesRepVO conversionUpdateFees(OrderDO updateOrder){
//        // 构建费用明细数据
//        UpdateFeesRepVO updateFeesRepVO = new UpdateFeesRepVO();
//        updateFeesRepVO.setOrderId(updateOrder.getId());
//        updateFeesRepVO.setPrice(updateOrder.getReallyDiscountPrice());
//        updateFeesRepVO.setLastPrice(updateOrder.getReallyLastPrice());
//        updateFeesRepVO.setTenantPayAmount(updateOrder.getTenantPayAmount());
//        updateFeesRepVO.setCompensationAmount(updateOrder.getCompensationAmount());
//        updateFeesRepVO.setCompensatingState(updateOrder.getCompensatingState());
//        return updateFeesRepVO;
//    }

    /**
     * 修改分佣数据
     *
     * @param orderId
     */
    private void updateSubCommission(String orderId) {
        List<BrokerageRespVO> brokerage = brokerageApi.byOrderIdGetData(Long.valueOf(orderId));
        // 当订单的状态为超重、未补，且订单分佣的状态不是已分佣或订单取消的状态
        if (CollUtil.isNotEmpty(brokerage)) {
            // 如果是超重没有补钱、不返佣 设置remark 为等待返佣
            List<BrokerageUpdateReqVO> updateReqVOs = new ArrayList<>();
            for (BrokerageRespVO brokerageRespVO : brokerage) {
                BrokerageUpdateReqVO updateReqVO = new BrokerageUpdateReqVO();
                updateReqVO.setId(brokerageRespVO.getId());
                if (brokerageRespVO.getStatus().equals("2")) {
                    updateReqVO.setStatus("4");
                }
                updateReqVOs.add(updateReqVO);
            }
            brokerageApi.updateBrokerageList(updateReqVOs);
        }
    }

    /**
     * 超重补差提醒
     */
    private void makeUpReminder(UpdateFeesRepVO updateOrder, OrderDO orderDO, BigDecimal payAmount, BigDecimal calcFeeWeight) {
        Long tenantId = orderDO.getTenantId();
        String orderId = orderDO.getId();
        try {
            // ==================== 新增：启动多阶段通知流程 ====================
            startMultiStageNotification(orderDO, updateOrder);

            // ==================== 保留原有逻辑（兼容性） ====================
            // 发送超重补差的提醒
            // miniNotify.sendDifferencePrice(orderDO.getUserId(), orderDO.getOrderNo(), updateOrder.getCompensationAmount(), "请尽快完成补款,否则快递会被拦截");
            //写入小程序消息队列处理  add by yyzz
            WxMiniMsgVo wxMiniMsgVo = new WxMiniMsgVo().createDifferencePriceMsgVo(orderDO.getTenantId(), orderDO.getUserId(), orderDO.getOrderNo(), updateOrder.getCompensationAmount(), "请尽快完成补款,否则快递会被拦截", orderId);
            wxMqProducer.sendMiniMessage(wxMiniMsgVo);
            //end

            // 查询订单详情
//                OrderParcelDO orderParcelDo = orderParcelService.getOrderParcelByOrderId(orderId);
            // 发送公众号超重补差提醒
            wxMpNotify.sendOverweightCompensation(orderDO.getUserId(), orderDO.getOrderNo(), calcFeeWeight, updateOrder.getLastPrice(), payAmount, updateOrder.getCompensationAmount());

            // 发送短信提醒，获取是否启动超重补差短信发送开关
            String smsSwitchStatus = paramApi.getSmsSwitchStatus(tenantId);
            if (smsSwitchStatus != null && !smsSwitchStatus.equals("")
                    && !smsSwitchStatus.equals("null")
                    && OrderDeliverySwitchEnum.ACTIVE.getStatus().equals(smsSwitchStatus)) {

                // 获取配置，根据配置判断短信是否要立即发送
                Boolean nowSend = false;
                String smsNowSendPrice = paramApi.getSmsNowSendPrice(tenantId);
                if (!StringUtils.isEmpty(smsNowSendPrice) && !"null".equals(smsNowSendPrice)) {
                    BigDecimal nowSendPrice = new BigDecimal(smsNowSendPrice);
                    nowSend = updateOrder.getCompensationAmount().compareTo(nowSendPrice) > 0;
                }

                // 是否同步发送寄件人
                Boolean sender = false;
                String smsSendSender = paramApi.getSmsSendSender(tenantId);
                if (OrderDeliverySwitchEnum.ACTIVE.getStatus().equals(smsSendSender)) {
                    sender = true;
                }

                // 获取单笔订单最多推送次数（默认：3）
                Integer smsPushCount = paramApi.getSmsPushCount(tenantId);
                String key = "sms::weigh_notify_count::" + tenantId + "_" + orderId;
                String count = stringRedisTemplate.opsForValue().get(key);
                if (StringUtils.isEmpty(count)) {
                    count = "1";
                    stringRedisTemplate.opsForValue().set(key, count);
                } else {
                    int old = Integer.valueOf(count);
                    stringRedisTemplate.opsForValue().set(key, String.valueOf(old + 1));
                }

                // 检查短信推送次数
                boolean canSendSms = ObjectUtil.isEmpty(smsPushCount) || (Integer.valueOf(count) <= smsPushCount);
                // 如果可以发送，则调用发送短信方法
                if (canSendSms) {
                    sendSms(orderDO, nowSend, sender);
                }
            }
            // 发送钉钉群通知

        } catch (Exception e) {
            log.info("渠道{},发送补差消息失败:{}", orderDO.getChannel(), e.getMessage());
        }

        if (OrderDeliverySwitchEnum.ACTIVE.getStatus().equals(paramApi.getDindinAppStatus(tenantId))) {
            /**
             * 超重补差发送钉钉群通知
             * 1.根据配置接口来获取当前商户是否开启 超重立即推送开关
             * 2.开启超重立即推送则执行原逻辑
             * 3.未开启超重，则将当前时间记录成 超重补差时间。后续定时任务根据此时间去判断何时推送数据
             */
            // 查询寄件人信息
            OrderContactsDO senderContacts = orderContactsService.getOrderContactsByOrderId(orderId, "sender");
            String senderMobile = ObjectUtil.isNotEmpty(senderContacts) ? senderContacts.getMobile() : "";
            if (paramApi.getDindinAppOverweightStatus(tenantId).equals(OrderDeliverySwitchEnum.ACTIVE.getStatus())) {
                // 立即推送
                dingTalkHelper.sendDinDingOverweight(tenantId, updateOrder.getCompensationAmount(), orderDO.getOrderNo(), orderDO.getUserId(), senderMobile);
                overweightCallbackService.deleteDataByOrderNo(orderDO.getOrderNo(), tenantId);
            } else {
                // 记录未推送钉钉的超重补差数据
                OrderOverweightCallbackCreateReqVO createReqVO = new OrderOverweightCallbackCreateReqVO();
                createReqVO.setMobile(senderMobile);
                createReqVO.setUserId(orderDO.getUserId());
                createReqVO.setDingdingFlag(OrderDeliveryOverweightEnum.NOT_SEND.getStatus());
                createReqVO.setCallbackTime(LocalDateTime.now());
                createReqVO.setOrderNo(orderDO.getOrderNo());
                createReqVO.setCompensationAmount(updateOrder.getCompensationAmount());
                createReqVO.setTenantId(tenantId);
                overweightCallbackService.createOrderOverweightCallback(createReqVO);
            }
        }
    }


    private void sendSms(OrderDO orderDO, Boolean nowSend, Boolean sender) {
        MemberUserRespDTO user = memberUserApi.getUser(orderDO.getUserId());
        // 如果未获取用户信息，则不发短信
        if (ObjectUtil.isEmpty(user)) {
            return;
        }

        Long tenantId = ObjectUtil.isNotEmpty(orderDO.getTenantId())
                ? orderDO.getTenantId()
                : (ObjectUtil.isEmpty(TenantContextHolder.getTenantId()) ? 1L : TenantContextHolder.getTenantId());
        SmsSendSingleOrderToUserReqDTO reqDTO = new SmsSendSingleOrderToUserReqDTO();
        reqDTO.setUser(user);
        reqDTO.setUserId(orderDO.getUserId());
        reqDTO.setNowSend(nowSend);
        reqDTO.setTenantId(tenantId);
        reqDTO.setOrderId(orderDO.getId());

        if (sender) {
            // 查询寄件人手机号
            OrderContactsDO senderInfo = orderContactsService.getOrderContactsByOrderId(orderDO.getId(), "sender");
            if (null != senderInfo) {
                String mobile = senderInfo.getMobile();
                if (user.getMobile().equals(mobile)) {
                    sender = false;
                } else {
                    reqDTO.setSenderMobile(mobile);
                }
            }
        }

        reqDTO.setSender(sender);
        try {
            smsSendApi.sendSingleSmsToMemberBefore(reqDTO);
        } catch (Exception exception) {
            log.error("发送超轻超重短信失败: {}", exception.getMessage());
        }
    }

    /**
     * ==================== 新增：启动多阶段通知流程 ====================
     * 启动多阶段通知流程
     *
     * @param orderDO     订单信息
     * @param updateOrder 更新的费用信息
     */
    private void startMultiStageNotification(OrderDO orderDO, UpdateFeesRepVO updateOrder) {
        try {
            log.info("[startMultiStageNotification] 启动多阶段通知流程，订单ID: {}, 订单号: {}, 补差金额: {}",
                    orderDO.getId(), orderDO.getOrderNo(), updateOrder.getCompensationAmount());

            // 构造用户信息
            Map<String, Object> userInfo = buildUserInfo(orderDO);

            // ==================== 集成多阶段通知服务 ====================
            // TODO 该用消息队列来处理

//            compensationNotificationService.startCompensationNotification(
//                orderDO.getId(),
//                orderDO.getTenantId(),
//                updateOrder.getCompensationAmount(),
//                userInfo
//            );

            // 临时日志记录，表示多阶段通知已触发
            log.info("[startMultiStageNotification] 多阶段通知流程已触发（待集成），订单ID: {}, 用户ID: {}, 补差金额: {}",
                    orderDO.getId(), orderDO.getUserId(), updateOrder.getCompensationAmount());

        } catch (Exception e) {
            log.error("[startMultiStageNotification] 启动多阶段通知流程异常，订单ID: {}", orderDO.getId(), e);
            // 异常不影响主流程
        }
    }

    /**
     * 构造用户信息
     */
    private Map<String, Object> buildUserInfo(OrderDO orderDO) {
        Map<String, Object> userInfo = new HashMap<>();

        try {
            // 获取用户信息
            MemberUserRespDTO user = memberUserApi.getUser(orderDO.getUserId());
            if (user != null) {
                // 使用 unionId 作为微信标识，如果没有则使用空字符串
                userInfo.put("openid", user.getUnionId() != null ? user.getUnionId() : "");
                userInfo.put("mobile", user.getMobile());
            }

            // 获取寄件人信息
            OrderContactsDO senderContacts = orderContactsService.getOrderContactsByOrderId(orderDO.getId(), "sender");
            if (senderContacts != null) {
                userInfo.put("senderMobile", senderContacts.getMobile());
                userInfo.put("senderName", senderContacts.getName());
            }

            // 添加订单基本信息
            userInfo.put("orderId", orderDO.getId());
            userInfo.put("orderNo", orderDO.getOrderNo());
            userInfo.put("tenantId", orderDO.getTenantId());

        } catch (Exception e) {
            log.error("[buildUserInfo] 构造用户信息异常，订单ID: {}", orderDO.getId(), e);
        }

        return userInfo;
    }

    /**
     * 超轻退款逻辑
     */
    private void ultraLight(OrderCallbackBigVo orderCallbackBigVo, OrderCallbackBigVo updateCallbackBig, UpdateFeesRepVO updateFeesRepVO, BigDecimal calcFeeWeight) {
//        Long userId = orderDO.getUserId();
//        // 构建订单修改数据
//        OrderUpdateReqVO updateDO = new OrderUpdateReqVO();
//        BeanUtil.copyProperties(updateOrder, updateDO);
//        updateDO.setId(orderDO.getId());
//        orderService.callbackUpdateOrder(updateDO);

//        orderSubmitService.updateTotalFees(conversionUpdateFees(updateOrder));
        // 组装费用信息
        assembledUpdateFeesInfo(orderCallbackBigVo, updateFeesRepVO, updateCallbackBig);

        if (ObjectUtil.isNotEmpty(orderCallbackBigVo.getOrderInfo().getUserId())) {
            ultraLightDeleteMakeup(orderCallbackBigVo.getOrderInfo());
        }
    }

    private void ultraLightDeleteMakeup(OrderDO updateOrder) {
        String orderId = updateOrder.getId();
        if (updateOrder.getCompensatingState().equals(OrderConstants.ORDER_COMPENSATING_STATE_REFUND)) {
            String orderNo = updateOrder.getOrderNo();
            OrderOverweightCallbackDO callbackDO = overweightCallbackService.getOrderOverweightCallbackByOrderNo(orderNo);
            if (ObjectUtil.isNotEmpty(callbackDO)) {
                overweightCallbackService.deleteDataByOrderNo(orderNo, callbackDO.getTenantId());
            }
        }

        /**
         * 当订单的状态是超轻的时候：判断之前是否有进行超重补差，并且有存在重量异常工单。满足条件进行超轻退款
         */
        if (updateOrder.getCompensatingState().equals(OrderConstants.ORDER_COMPENSATING_STATE_REFUND)) {
            // 更具订单ID查询补差明细表和工单表
            List<OrderCompensateDO> compensateList = compensateService.getOrderCompensateList(new OrderCompensateExportReqVO().setCompensatingState("2").setCompensatingResult("3").setOrderId(orderId));

            // 查询工单列表是否存在该订单的重量异常工单
            String typeId = "-1";
            String channel = updateOrder.getChannel();
            switch (channel) {
                case "3": typeId = "31"; break;
                case "4": typeId = "24"; break;
                case "5": typeId = "37"; break;
                default: break;
            }
            /**
             * 说明一下这里为什么要这么写，因为需要查询该订单的重量异常工单需要传入typeId，但是typeId的值不一样，所以这里需要根据channel的值来判断typeId的值
             * typeId的值是 = delivery_work_order_type.id
             */
            if (!typeId.equals("-1")) {
                PageResult<WorkOrderRespVO> pageResult = workOrderService.getWorkOrderPage2(new WorkOrderPageReqVO().setOrderId(Long.valueOf(orderId)).setTypeId(typeId));
                if (CollUtil.isNotEmpty(compensateList) && CollUtil.isNotEmpty(pageResult.getList())) {
                    // 调用超轻退款
                    orderProcessService.ultraLightRefund(orderId);
                }
            }
        }
    }

    /**
     * 计算加盟商订单金额
     *
     * @param updateOrder
     * @param orderId
     * @return
     */
    private BigDecimal countTenantAmount(OrderCallbackBigVo orderCallbackBigVo, UpdateFeesRepVO updateOrder, String orderId) {
        if (ObjectUtil.isEmpty(updateOrder)) {
            return BigDecimal.ZERO;
        }
        OrderDO orderDO = orderCallbackBigVo.getOrderInfo();
        if (ObjectUtil.isEmpty(orderCallbackBigVo.getFeeTenant()) || ObjectUtil.isEmpty(orderCallbackBigVo.getFeeTenant().getCost())) {
            return BigDecimal.ZERO;
        }
        BigDecimal tenantPayAmount = NumberUtil.isGreater(updateOrder.getTenantPayAmount(), BigDecimal.ZERO) ? updateOrder.getTenantPayAmount() : BigDecimal.ZERO;

        BigDecimal repairAmount = orderCallbackBigVo.getFeeTenant().getCost().subtract(tenantPayAmount);
        // 如果订单已退款的话，分销商需补全部金额
        if ("1".equals(orderDO.getRefundFlag())) {
            repairAmount = updateOrder.getTenantPayAmount().negate();
        }
        return repairAmount;
    }


    /**
     * 计算订单是否超重
     *
     * @param orderId
     * @param freight
     * @param feeUser     用户费用信息
     * @param updateOrder
     * @return
     */
    public String weightChange(String orderId, BigDecimal freight, FeeUserDO feeUser, OrderDO updateOrder) {

        BigDecimal paidAmount = feeUser.getPayAmount();
        if (ObjectUtil.isNotEmpty(feeUser.getCouponId()) && ObjectUtil.isNotEmpty(feeUser.getReliefAmount())) {
            paidAmount = paidAmount.add(feeUser.getReliefAmount());
        }

        // 超重计算完成之后，用户提交工单实际没超重，易达回调，如果已补差不管，未补差把状态改为正常。
        OrderDO orderDO = orderService.getOrder(orderId);
        // 计算是 "超重补差"还是"超轻退款"
        BigDecimal deliveryPrice = freight; // 折后运费
//        BigDecimal paidAmount = orderDO.getPayAmount(); // 最终价格 =》 实际支付价格
        BigDecimal compensationPaid = (ObjectUtil.isEmpty(orderDO.getCompensationPaid0()) || orderDO.getCompensationPaid0().compareTo(BigDecimal.ZERO) <= 0) ? BigDecimal.ZERO : orderDO.getCompensationPaid0();
//        updateOrder.setCompensationPaid1(ObjectUtil.isNotEmpty(orderDO.getCompensationPaid1()) ? orderDO.getCompensationPaid1() : BigDecimal.ZERO);
//        updateOrder.setCompensationPaid2(ObjectUtil.isNotEmpty(orderDO.getCompensationPaid2()) ? orderDO.getCompensationPaid2() : BigDecimal.ZERO);

        String status = "";

        if (ObjectUtil.isNotEmpty(paidAmount) && deliveryPrice.compareTo(paidAmount) == 1) {
            // 计算超重补差金额
            updateOrder.setCompensationAmount(countCompensationAmount(paidAmount, deliveryPrice, "1"));

            status = OrderConstants.ORDER_STATUS_OVERWEIGHT_COMPENSATION;

//            updateOrder.setStatus(OrderConstants.ORDER_STATUS_OVERWEIGHT_COMPENSATION);
            updateOrder.setCompensatingState(OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO);
            updateOrder.setCompensatingResult(OrderConstants.ORDER_COMPENSATING_RESULT_TWO);//未完成
        }

        if (ObjectUtil.isNotEmpty(paidAmount) && deliveryPrice.compareTo(paidAmount) == -1) {
            BigDecimal compensationAmount = countCompensationAmount(paidAmount, deliveryPrice, "2");
            // 退款金额大于支付金额的话，只退支付金额。
            if (compensationAmount.compareTo(feeUser.getPayAmount()) > 0) {
                compensationAmount = feeUser.getPayAmount();
            }
            // 计算超轻补差金额
            updateOrder.setCompensationAmount(compensationAmount);

            status = OrderConstants.ORDER_STATUS_REFUND;
//            updateOrder.setStatus(OrderConstants.ORDER_STATUS_REFUND);
            updateOrder.setCompensatingState(OrderConstants.ORDER_COMPENSATING_STATE_REFUND);
            updateOrder.setCompensatingResult(OrderConstants.ORDER_COMPENSATING_RESULT_TWO);
        }

        if (ObjectUtil.isNotEmpty(paidAmount) && deliveryPrice.compareTo(paidAmount) == 0) {
            //超轻或者超重未补，结果是未完成的
            if ((OrderConstants.ORDER_COMPENSATING_STATE_REFUND.equals(orderDO.getCompensatingState()) || OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO.equals(orderDO.getCompensatingState())) && OrderConstants.ORDER_COMPENSATING_RESULT_TWO.equals(orderDO.getCompensatingResult())) {

                status = orderDO.getStatus();
//                updateOrder.setStatus(orderDO.getStatus());
                updateOrder.setCompensatingState(OrderConstants.ORDER_COMPENSATING_STATE_NORMAL);
                updateOrder.setCompensatingResult(OrderConstants.ORDER_COMPENSATING_RESULT_ONE);
                updateOrder.setCompensationAmount(BigDecimal.ZERO);
            } else {
                status = orderDO.getStatus();
//                updateOrder.setStatus(orderDO.getStatus());
                updateOrder.setCompensatingState(orderDO.getCompensatingState());
                updateOrder.setCompensatingResult(orderDO.getCompensatingResult());

                updateOrder.setCompensationAmount(orderDO.getCompensationAmount());
                updateOrder.setRefundFlag("0");
            }
        }
        // 如果当前订单是到付，并且已退款的情况
        if (OrderConstants.ORDER_PAY_MODEL_UPONDELIVERY.equals(orderDO.getPayModel()) && "1".equals(orderDO.getRefundFlag())) {
            status = orderDO.getStatus();
            updateOrder.setCompensatingState(OrderConstants.ORDER_COMPENSATING_STATE_NORMAL);
            updateOrder.setCompensatingResult(OrderConstants.ORDER_COMPENSATING_RESULT_ONE);
            updateOrder.setCompensationAmount(BigDecimal.ZERO);
        }

        return status;
    }

    /**
     * @param payAmount     已付金额
     * @param deliveryPrice 实际最终成交价格
     * @param type          类型 1，超重补差 2，超轻退款
     */
    private BigDecimal countCompensationAmount(BigDecimal payAmount, BigDecimal deliveryPrice, String type) {
        BigDecimal compensationAmount = null;
        if (ObjectUtil.isNotEmpty(payAmount)) {
            // 计算金额差价
            compensationAmount = "1".equals(type) ? deliveryPrice.subtract(payAmount) : payAmount.subtract(deliveryPrice);
        } else {
            compensationAmount = deliveryPrice;
        }
        return compensationAmount;
    }


    /**
     * 更新快递员信息
     *
     * @param callback
     * @param orderCallbackBigVo
     */
    private void handleCourier(OrderCallbackVo callback, OrderCallbackBigVo orderCallbackBigVo) {

        OrderCallbackBigVo updateCallbackVo = new OrderCallbackBigVo();
        // 组装快递员等信息
        updateCallbackVo.setOrderParcel(assembledCourierInfo(callback, orderCallbackBigVo));
        updateCallbackVo.setOrderId(orderCallbackBigVo.getOrderId());



        OrderParcelUpdateReqVO reqVO = new OrderParcelUpdateReqVO();
        BeanUtil.copyProperties(updateCallbackVo.getOrderParcel(), reqVO);
        // 赋值预约开始，结束时间
        if(ObjectUtil.isNotEmpty(callback.getAppointStartTime()) && ObjectUtil.isNotEmpty(callback.getAppointEndTime())){
            reqVO.setAppointStartTime(callback.getAppointStartTime());
            reqVO.setAppointEndTime(callback.getAppointEndTime());
            reqVO.setId(orderCallbackBigVo.getOrderParcel().getId());
        }
        orderParcelService.updateOrderParcel(reqVO);
//        // 修改订单状态
//        orderService.callbackUpdateOrderV2(updateCallbackVo);
    }


    private void syncOrderFee(OrderCallbackVo callbackVo, OrderCallbackBigVo orderCallbackBigVo, OrderCallbackBigVo updateCallbackBig) {
        if (StrUtil.isEmpty(orderCallbackBigVo.getOrderId())) {
            return;
        }
        BigDecimal calcFeeWeight = callbackVo.getCalcFeeWeight();
        BigDecimal realWeight = callbackVo.getRealWeight();
        BigDecimal realVolume = callbackVo.getRealVolume();
        Integer packageCount = null;
        String needRebate = null;

        List<OrderFeeBaseVO> feeList = callbackVo.getFeeList();

        BigDecimal weight = ObjectUtil.isNotEmpty(calcFeeWeight) ? calcFeeWeight : ObjectUtil.isNotEmpty(realWeight) ? realWeight : calcFeeWeight;

        //组装费用详情
        updateCallbackBig.setOrderFeeDOList(updateFeeInfoV2(orderCallbackBigVo, feeList, weight));
        //组装包裹信息
        updateCallbackBig.setOrderParcel(updateParcelInfo(orderCallbackBigVo, calcFeeWeight, realWeight, realVolume, packageCount, needRebate));
    }


    /**
     * 更新费用
     *
     * @param orderId 订单id
     * @param feeList 费用列表
     */
    public void updateFeeInfo(String orderId, List<OrderFeeBaseVO> feeList, BigDecimal weight) {
        OrderDO orderDO = orderService.getOrder(orderId);
        if (ObjectUtil.isEmpty(orderDO)) {
            return;
        }
//        BigDecimal userLevelDiscount = getUserDiscount(orderDO.getUserId());


        if (ObjectUtil.isNotEmpty(feeList)) {
            BigDecimal totalAmount = BigDecimal.ZERO;
            // 查询快递配置--快递增加比列
            for (OrderFeeBaseVO fee : feeList) {
                totalAmount = totalAmount.add(ObjectUtil.isNotEmpty(fee.getAmount()) ? fee.getAmount() : BigDecimal.ZERO);
            }
            OrderFeeBaseVO feeBaseVO = new OrderFeeBaseVO();
            feeBaseVO.setAmount(totalAmount);
            BigDecimal finalPrice = orderCalcService.calcFeeInfo(feeBaseVO, orderDO, weight);
            for (OrderFeeBaseVO fee : feeList) {
                if (!fee.getType().equals("0")) {
                    finalPrice = finalPrice.subtract(fee.getAmount());
                }
            }
            for (OrderFeeBaseVO fee : feeList) {
                if (fee.getType().equals("0")) {
                    fee.setAmount(finalPrice);
                }
            }
        }

        if (CollUtil.isNotEmpty(feeList)) {

            // 保存费用信息
            List<OrderFeeDO> feeBatchList = feeList.stream().map(item -> new OrderFeeDO(null, orderId, item.getAmount(), item.getName(), item.getType() + "")).collect(Collectors.toList());

            // 先删后增加
            orderFeeService.byOrderIdDelete(orderId);
            orderFeeService.insertBatch(feeBatchList);

            // TODO 2024-12-10 取消订单相关ES操作
            // 保存到es
//            orderEsService.updateOrderES(new ESOrderDO().setId(TenantContextHolder.getTenantId() + "_" + orderId).setFeesInfo(feeBatchList));
        }
    }


    /**
     * 更新费用
     *
     * @param feeList 费用列表
     */
    public List<OrderFeeDO> updateFeeInfoV2(OrderCallbackBigVo orderCallbackBigVo ,List<OrderFeeBaseVO> feeList, BigDecimal weight) {
        OrderDO orderDO = orderCallbackBigVo.getOrderInfo();
        String orderId = orderCallbackBigVo.getOrderId();
        if (ObjectUtil.isEmpty(orderDO)) {
            return null;
        }
//        BigDecimal userLevelDiscount = getUserDiscount(orderDO.getUserId());
        // 如果用的exl查价，快递运费重新计算
        if (ObjectUtil.isNotEmpty(orderDO.getExcelItemId())) {
            queryExlPrice(orderDO.getExcelItemId(), weight, feeList, null, orderId);
        }

        if (ObjectUtil.isNotEmpty(feeList)) {
            BigDecimal totalAmount = BigDecimal.ZERO;
            // 查询快递配置--快递增加比列
            for (OrderFeeBaseVO fee : feeList) {
                totalAmount = totalAmount.add(ObjectUtil.isNotEmpty(fee.getAmount()) ? fee.getAmount() : BigDecimal.ZERO);
            }
            OrderFeeBaseVO feeBaseVO = new OrderFeeBaseVO();
            feeBaseVO.setAmount(totalAmount);

            BigDecimal finalPrice = BigDecimal.ZERO;
            if (ObjectUtil.isNotEmpty(orderDO.getTenantChannelId())) {
                finalPrice = orderCalcService.calcFeeInfoNewV2(feeBaseVO, orderDO, weight);
            } else {
                finalPrice = orderCalcService.calcFeeInfoV2(feeBaseVO, orderDO, weight);
            }
            for (OrderFeeBaseVO fee : feeList) {
                if (!fee.getType().equals("0")) {
                    finalPrice = finalPrice.subtract(fee.getAmount());
                }
            }
            for (OrderFeeBaseVO fee : feeList) {
                if (fee.getType().equals("0")) {
                    fee.setAmount(finalPrice);
                }
            }
        }

        if (CollUtil.isNotEmpty(feeList)) {

            // 保存费用信息
            List<OrderFeeDO> feeBatchList = feeList.stream().map(item -> new OrderFeeDO(null, orderId, item.getAmount(), item.getName(), item.getType() + "")).collect(Collectors.toList());

//            // 先删后增加
//            orderFeeService.byOrderIdDelete(orderId);
//            orderFeeService.insertBatch(feeBatchList);


            // TODO 2024-12-10 取消订单相关ES操作
//            try {
//                // 保存到es
//                orderEsService.updateOrderES(new ESOrderDO().setId(TenantContextHolder.getTenantId() + "_" + orderId).setFeesInfo(feeBatchList));
//            } catch (Exception e){
//                log.error("保存es失败，{}",e.getMessage());
//            }
            return feeBatchList;
        }
        return null;
    }

    /**
     * 更新包裹信息
     *
     * @param calcFeeWeight 计费重量（合计抛）
     * @param realWeight    实际重量 (KG)
     * @param realVolume    实际体积 (CM³)
     * @param packageCount  包裹数
     * @param needRebate    是否返佣 Y/N
     */
    public OrderParcelDO updateParcelInfo(OrderCallbackBigVo orderCallbackBigVo, BigDecimal calcFeeWeight, BigDecimal realWeight, BigDecimal realVolume, Integer packageCount, String needRebate) {
        OrderParcelDO orderParcelDo = orderCallbackBigVo.getOrderParcel();

        if (null == orderParcelDo) {
            return null;
        }

        OrderParcelDO orderParcelUpdateVo = new OrderParcelDO();
        orderParcelUpdateVo.setId(orderParcelDo.getId());
        orderParcelUpdateVo.setRealVolume(realVolume);
        orderParcelUpdateVo.setRealPackageCount(packageCount);
        orderParcelUpdateVo.setRealWeight(realWeight);
        orderParcelUpdateVo.setCalcFeeWeight(calcFeeWeight);
        orderParcelUpdateVo.setNeedRebate(needRebate);

        return orderParcelUpdateVo;
    }

    /**
     * 已取件
     *
     * @param callback
     * @param orderDO
     */
    private void handleReceived(OrderCallbackVo callback, OrderDO orderDO, OrderCallbackBigVo orderCallbackBigVo) {
        OrderCallbackBigVo updateBigVo = new OrderCallbackBigVo();

        OrderDO updateDO = new OrderDO();


        updateDO.setId(orderDO.getId());
        updateDO.setStatus(OrderConstants.ORDER_STATUS_PICKED_PIECE);

        // 判空
        if (ObjectUtil.isNotEmpty(orderCallbackBigVo) && ObjectUtil.isNotEmpty(orderCallbackBigVo.getOrderParcel())) {
            Boolean isUpdateParcel = false;
            OrderParcelDO updateParcel = new OrderParcelDO();
            if (ObjectUtil.isEmpty(orderCallbackBigVo.getOrderParcel().getThirdExpressType()) && ObjectUtil.isNotEmpty(callback.getThirdExpressType())) {
                isUpdateParcel = true;
                updateParcel.setThirdExpressType(callback.getThirdExpressType());
            }
            if (ObjectUtil.isEmpty(orderCallbackBigVo.getOrderParcel().getThirdExpressName()) && ObjectUtil.isNotEmpty(callback.getThirdExpressName())) {
                isUpdateParcel = true;
                updateParcel.setThirdExpressName(callback.getThirdExpressName());
            }

            if (isUpdateParcel) {
                updateParcel.setId(orderCallbackBigVo.getOrderParcel().getId());
                updateBigVo.setOrderParcel(updateParcel);
            }
        }

        updateBigVo.setOrderInfo(updateDO);


        orderService.callbackUpdateOrderV2(updateBigVo);

        // 发送快递员已取件通知
        if (ObjectUtil.isEmpty(orderDO.getUserId())) {
            return;
        }

        try {
            String expressCompany = getKdChannelName(orderDO);
            NotifyMsgVo notifyMsgVo = new NotifyMsgVo().createCourierPickedUp(orderDO.getId(), orderDO.getOrderNo(), orderDO.getTenantId(), orderDO.getUserId(), expressCompany, LocalDateTime.now());
            notifyParamService.sendNotify(NoticeGroupCodeEnum.COURIER_PICKED_UP.getCode(), notifyMsgVo);
        } catch (Exception e) {
            log.error("发送快递员已取件通知失败，{}", e.getMessage());
        }

        try {
            sendMqtt(orderDO.getTenantId() + "", orderDO.getUserId() + "", orderDO.getId(), orderDO.getOrderNo(), updateDO.getStatus(), orderDO.getStatus());
        } catch (Exception e) {
            log.error("发送mqtt失败，{}", e.getMessage());
        }

    }

    private String getKdChannelName(OrderDO orderDO) {
        if (ObjectUtil.isEmpty(orderDO)) {
            return null;
        }

        Long channelExpressId = orderDO.getChannelExpressId();
        if (ObjectUtil.isNotEmpty(channelExpressId)) {
            KdChannelExportReqVO kdChannelExportReqVO = kdChannelService.getChannelAndExpressName(channelExpressId);
            if (ObjectUtil.isNotEmpty(kdChannelExportReqVO)) {
                return kdChannelExportReqVO.getExpressName();
            }
        }

        return ObjectUtil.isNotEmpty(orderDO.getDeliveryType()) ? orderDO.getDeliveryType() : null;
    }

    /**
     * 组转快递员信息
     */
    private OrderParcelDO assembledCourierInfo(OrderCallbackVo callback, OrderCallbackBigVo orderCallbackBigVo) {
        if (ObjectUtil.isEmpty(callback) && ObjectUtil.isEmpty(orderCallbackBigVo) && ObjectUtil.isEmpty(orderCallbackBigVo.getOrderParcel())) {
            return null;
        }

        if (ObjectUtil.isNotEmpty(callback.getSiteName()) || ObjectUtil.isNotEmpty(callback.getPickUpCode()) ||
                ObjectUtil.isNotEmpty(callback.getCourierName()) || ObjectUtil.isNotEmpty(callback.getCourierPhone())) {

            String siteName = "";
            String pickUpCode = "";
            String courierName = "";
            String courierPhone = "";
            if (ObjectUtil.isNotEmpty(orderCallbackBigVo.getOrderParcel().getPickUpInfo())) {
                String[] items = orderCallbackBigVo.getOrderParcel().getPickUpInfo().split(",");
                if (items.length > 3) {
                    siteName = items[0];
                    pickUpCode = items[1];
                    courierName = items[2];
                    courierPhone = items[3];
                }
            }

            // 站点,取件码,取件人,取件号码
            String pickUpInfo = "";
            if (ObjectUtil.isNotEmpty(callback.getSiteName())) {
                siteName = callback.getSiteName();
            }
            pickUpInfo = siteName + ",";

            if (ObjectUtil.isNotEmpty(callback.getPickUpCode())) {
                pickUpCode = callback.getPickUpCode();
            }
            pickUpInfo = pickUpInfo + pickUpCode + ",";


            if (ObjectUtil.isNotEmpty(callback.getCourierName())) {
                courierName = callback.getCourierName();
            }
            pickUpInfo = pickUpInfo + courierName + ",";


            if (ObjectUtil.isNotEmpty(callback.getCourierPhone())) {
                courierPhone = callback.getCourierPhone();
            }
            pickUpInfo = pickUpInfo + courierPhone;

            OrderParcelDO orderParcel = new OrderParcelDO();
            orderParcel.setPickUpInfo(pickUpInfo);
            orderParcel.setId(orderCallbackBigVo.getOrderParcel().getId());

            return orderParcel;
        }
        return null;
    }

    private void validateCallback(OrderCallbackVo callback) {
        // 回调信息不能为空
        Assert.notNull(callback, "回调参数为空");
        // 订单id不能为空
        Assert.notNull(callback.getOrderId(), "订单id为空");
        // 订单状态不能为空
        Assert.notNull(callback.getStatus(), "状态为空");
    }
// TODO 2024-12-10 取消订单相关ES操作
//    /**
//     * 修改es回调数据状态
//     *
//     * @param callbackId
//     * @param logCallbackStatus
//     */
//    @Override
//    public void updateEsCallbackLog(String callbackId, String logCallbackStatus) {
//        // 20240429改用es
//        if (ObjectUtil.isNotEmpty(callbackId)) {
//            kuaidiCallbackRepositoryServer.byIdUpdateStatus(callbackId, logCallbackStatus);
//        }
//    }
//
//    /**
//     * 执行完回调修改es数据
//     */
//    private void executionUpdateCallbackLog(String callbackId, String status, String orderId, String orderNo, String thirdNo, String pushType, Date executionTime, String errorMessage) {
//        if (ObjectUtil.isNotEmpty(callbackId)) {
//            kuaidiCallbackRepositoryServer.executionCompleteUpdate(callbackId, status, orderId, orderNo, thirdNo, pushType, executionTime, errorMessage);
//        }
//    }
}
