# ==================== 多阶段消息推送系统配置 ====================

# 大汉三通语音通知配置
dahan:
  voice:
    # 基本配置
    account: ${DAHAN_VOICE_ACCOUNT:dh8528}
    raw-password: ${DAHAN_VOICE_PASSWORD:your_password_here}

    # 接口地址
    submit-url: ${DAHAN_VOICE_SUBMIT_URL:http://voice.3tong.net/json/voiceSms/SubmitVoc}
    report-url: ${DAHAN_VOICE_REPORT_URL:http://voice.3tong.net/json/voiceSms/GetReport}

    # 超时配置
    connect-timeout: ${DAHAN_VOICE_CONNECT_TIMEOUT:30000}
    read-timeout: ${DAHAN_VOICE_READ_TIMEOUT:60000}

    # 功能开关
    enabled: ${DAHAN_VOICE_ENABLED:true}

    # 语音配置
    default-speaker: ${DAHAN_VOICE_SPEAKER:B0}  # B0=汉小美, B1=汉小宇, B3=汉逍遥, B4=汉丫丫
    default-play-times: ${DAHAN_VOICE_PLAY_TIMES:1}
    max-retry-times: ${DAHAN_VOICE_MAX_RETRY:3}

# 多阶段通知配置
notification:
  compensation:
    # 通知延时配置
    delays:
      sms: 2h        # 短信延时2小时
      phone: 6h      # 电话在短信后6小时

    # 工作时间配置
    work-time:
      start: "09:00"
      end: "23:59"

    # 重试配置
    retry:
      max-attempts: 3
      interval: 30m

    # 通知渠道开关
    channels:
      mini-program: true
      wechat-mp: true
      sms: true
      phone-call: true

    # 通知模板配置
    templates:
      phone-call:
        compensation: "您好，您的快递订单{orderNo}需要补差{amount}元，请及时处理。如需帮助请联系客服。"
        verify-code: "您的验证码是{code}，请在5分钟内输入。"
      sms:
        compensation: "【快递补差】您的订单{orderNo}需要补差{amount}元，请及时处理。回复TD退订"
      mini-program:
        compensation: "订单补差通知"
      wechat-mp:
        compensation: "快递订单补差提醒"

# 日志配置
logging:
  level:
    com.hnyiti.kuaidi.module.pgsql.service.notification: INFO
    com.hnyiti.kuaidi.module.pgsql.service.notification.voice: DEBUG
    com.hnyiti.kuaidi.module.pgsql.service.notification.scheduler: DEBUG

