package com.hnyiti.kuaidi.module.pgsql.service.notification;

import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知任务服务接口
 *
 * <AUTHOR>
 */
public interface NotificationTaskService {

    /**
     * 创建通知任务
     *
     * @param orderId       订单ID
     * @param tenantId      租户ID
     * @param taskType      任务类型
     * @param scheduledTime 计划执行时间
     * @param taskData      任务数据
     * @return 任务ID
     */
    Long createNotificationTask(String orderId, Long tenantId, String taskType,
                                LocalDateTime scheduledTime, Map<String, Object> taskData);

    /**
     * 根据ID获取通知任务
     *
     * @param id 任务ID
     * @return 通知任务
     */
    NotificationTask getNotificationTaskById(Long id);

    /**
     * 更新任务状态
     *
     * @param id         任务ID
     * @param taskStatus 任务状态
     */
    void updateTaskStatus(Long id, String taskStatus);
}
