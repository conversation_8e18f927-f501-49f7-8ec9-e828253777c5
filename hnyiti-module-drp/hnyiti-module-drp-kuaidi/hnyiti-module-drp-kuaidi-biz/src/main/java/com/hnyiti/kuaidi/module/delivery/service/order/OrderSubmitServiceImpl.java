package com.hnyiti.kuaidi.module.delivery.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hnyiti.kuaidi.framework.common.enums.CommonStatusEnum;
import com.hnyiti.kuaidi.framework.common.exception.ErrorCode;
import com.hnyiti.kuaidi.framework.common.exception.ServiceException;
import com.hnyiti.kuaidi.framework.common.pojo.CommonResult;
import com.hnyiti.kuaidi.framework.common.util.json.JsonUtils;
import com.hnyiti.kuaidi.framework.common.util.servlet.ServletUtils;
import com.hnyiti.kuaidi.framework.dict.core.util.DictFrameworkUtils;
import com.hnyiti.kuaidi.framework.env.config.EnvProperties;
import com.hnyiti.kuaidi.framework.pay.core.enums.channel.PayChannelEnum;
import com.hnyiti.kuaidi.framework.pay.core.enums.order.PayOrderDisplayModeEnum;
import com.hnyiti.kuaidi.framework.redis.util.RedisUtils;
import com.hnyiti.kuaidi.framework.redis.util.RedissonUtils;
import com.hnyiti.kuaidi.framework.security.core.util.SecurityFrameworkUtils;
import com.hnyiti.kuaidi.framework.tenant.core.context.TenantContextHolder;
import com.hnyiti.kuaidi.framework.tenant.core.util.TenantUtils;
import com.hnyiti.kuaidi.module.PayLogConstants;
import com.hnyiti.kuaidi.module.consumer.MQOrderKickbackVo;
import com.hnyiti.kuaidi.module.consumer.MQOrderRefundVo;
import com.hnyiti.kuaidi.module.consumer.MQOrderRiskVo;
import com.hnyiti.kuaidi.module.consumer.OrderMqProducer;
import com.hnyiti.kuaidi.module.consumer.wxmsg.WxMiniMsgVo;
import com.hnyiti.kuaidi.module.consumer.wxmsg.WxMqProducer;
import com.hnyiti.kuaidi.module.consumer.wxmsg.WxMsgTypeConstants;
import com.hnyiti.kuaidi.module.delivery.api.brokerage.BrokerageApi;
import com.hnyiti.kuaidi.module.delivery.api.brokerage.vo.BrokerageRespVO;
import com.hnyiti.kuaidi.module.delivery.api.brokerage.vo.BrokerageUpdateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.billing.vo.PushMqttVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.coupon.vo.records.CouponRecordRespVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.expressproduct.vo.ExpressProductRespVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.feeuser.vo.FeeUserUpdateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.kd.channel.vo.KdChannelExportReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.order.OrderBigVo;
import com.hnyiti.kuaidi.module.delivery.controller.admin.order.vo.OrderUpdateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.orderparcel.vo.OrderParcelUpdateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.orderpayment.vo.OrderPaymentCreateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.userfreezelog.vo.UserFreezeLogCreateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.app.vo.MiniCreateOrderReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.app.vo.MiniCreateOrderResVO;
import com.hnyiti.kuaidi.module.delivery.controller.app.vo.MiniOrderPayReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.app.vo.MiniUpdateOrderReqVO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.address.AddressDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.coupon.CouponRecordDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.feesnapshot.FeeSnapshotDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.feesuper.FeeSuperDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.feetenant.FeeTenantDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.feeuser.FeeUserDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.kd.channel.KdChannelDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.order.OrderDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.ordercontacts.OrderContactsDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.orderoverweightcallback.OrderOverweightCallbackDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.orderparcel.OrderParcelDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.productpickuprule.ProductPickupRuleDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.third.ThirdDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.thirdparams.ThirdParamsDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.userfreeze.UserFreezeDO;
import com.hnyiti.kuaidi.module.delivery.enums.CouponConstants;
import com.hnyiti.kuaidi.module.delivery.enums.OrderConstants;
import com.hnyiti.kuaidi.module.delivery.enums.UserFreezeLogEnum;
import com.hnyiti.kuaidi.module.delivery.enums.thirdparams.ThirdParamsSourceTypeEnum;
import com.hnyiti.kuaidi.module.delivery.framework.mqtt.MqttSendClient;
import com.hnyiti.kuaidi.module.delivery.framework.runner.TenantBeanUtils;
import com.hnyiti.kuaidi.module.delivery.service.address.AddressService;
import com.hnyiti.kuaidi.module.delivery.service.channel.ChannelService;
import com.hnyiti.kuaidi.module.delivery.service.channelcode.ChannelCodeService;
import com.hnyiti.kuaidi.module.delivery.service.channelexpress.ChannelExpressService;
import com.hnyiti.kuaidi.module.delivery.service.commissionrecords.CommissionRecordsService;
import com.hnyiti.kuaidi.module.delivery.service.coupon.CouponRecordsService;
import com.hnyiti.kuaidi.module.delivery.service.coupon.CouponRiskControlService;
import com.hnyiti.kuaidi.module.delivery.service.coupon.CouponService;
import com.hnyiti.kuaidi.module.delivery.service.coupon.CouponTemplateService;
import com.hnyiti.kuaidi.module.delivery.service.express.ExpressService;
import com.hnyiti.kuaidi.module.delivery.service.expressproduct.ExpressProductService;
import com.hnyiti.kuaidi.module.delivery.service.feesnapshot.FeeSnapshotService;
import com.hnyiti.kuaidi.module.delivery.service.feesuper.FeeSuperService;
import com.hnyiti.kuaidi.module.delivery.service.feetenant.FeeTenantService;
import com.hnyiti.kuaidi.module.delivery.service.feeuser.FeeUserService;
import com.hnyiti.kuaidi.module.delivery.service.kd.channel.KdChannelService;
import com.hnyiti.kuaidi.module.delivery.service.kd.tenantChannel.TenantChannelService;
import com.hnyiti.kuaidi.module.delivery.service.notify.NotifyParamService;
import com.hnyiti.kuaidi.module.delivery.service.notify.vo.NotifyMsgVo;
import com.hnyiti.kuaidi.module.delivery.service.order.pay.PayStrategy;
import com.hnyiti.kuaidi.module.delivery.service.order.pay.PayStrategyFactory;
import com.hnyiti.kuaidi.module.delivery.service.order.pay.vo.PayRequest;
import com.hnyiti.kuaidi.module.delivery.service.ordercompensate.OrderCompensateService;
import com.hnyiti.kuaidi.module.delivery.service.ordercontacts.OrderContactsService;
import com.hnyiti.kuaidi.module.delivery.service.orderoverweightcallback.OrderOverweightCallbackService;
import com.hnyiti.kuaidi.module.delivery.service.orderparcel.OrderParcelService;
import com.hnyiti.kuaidi.module.delivery.service.orderpayment.OrderPaymentService;
import com.hnyiti.kuaidi.module.delivery.service.productpickuprule.ProductPickupRuleService;
import com.hnyiti.kuaidi.module.delivery.service.third.ThirdService;
import com.hnyiti.kuaidi.module.delivery.service.thirdparams.ThirdParamsService;
import com.hnyiti.kuaidi.module.delivery.service.userfreeze.UserFreezeService;
import com.hnyiti.kuaidi.module.member.MemberConstants;
import com.hnyiti.kuaidi.module.member.api.balance.vo.BalanceRespVO;
import com.hnyiti.kuaidi.module.member.api.balance.vo.api.BalanceApi;
import com.hnyiti.kuaidi.module.member.api.user.MemberUserApi;
import com.hnyiti.kuaidi.module.member.api.user.dto.MemberUserRespDTO;
import com.hnyiti.kuaidi.module.member.api.user.dto.MemberUserRespVO;
import com.hnyiti.kuaidi.module.member.controller.admin.wechatpaylog.vo.WechatPayLogCreateReqVO;
import com.hnyiti.kuaidi.module.member.enums.BalanceConstants;
import com.hnyiti.kuaidi.module.member.enums.FeeConstants;
import com.hnyiti.kuaidi.module.member.service.wechatpaylog.WechatPayLogService;
import com.hnyiti.kuaidi.module.message.api.MessageSendApi;
import com.hnyiti.kuaidi.module.message.api.notice.NoticeGroupCodeEnum;
import com.hnyiti.kuaidi.module.pay.api.order.dto.PayOrderCreateReqDTO;
import com.hnyiti.kuaidi.module.pay.controller.admin.order.vo.PayOrderSubmitReqVO;
import com.hnyiti.kuaidi.module.pay.controller.admin.order.vo.PayOrderSubmitRespVO;
import com.hnyiti.kuaidi.module.pay.dal.dataobject.app.PayAppDO;
import com.hnyiti.kuaidi.module.pay.service.app.PayAppService;
import com.hnyiti.kuaidi.module.pay.service.order.PayOrderService;
import com.hnyiti.kuaidi.module.system.api.param.ParamApi;
import com.hnyiti.kuaidi.module.system.api.social.SocialUserApi;
import com.hnyiti.kuaidi.module.system.api.tenant.TenantApi;
import com.hnyiti.kuaidi.module.system.api.tenant.dto.TenantRespVO;
import com.hnyiti.kuaidi.module.system.enums.DictTypeConstants;
import com.hnyiti.kuaidi.module.system.enums.TenantAmount.TenantAmountConstants;
import com.hnyiti.kuaidi.module.system.enums.TenantConstants;
import com.hnyiti.kuaidi.module.system.enums.social.SocialTypeEnum;
import com.hnyiti.kuaidi.module.wxmini.controller.admin.wecomconfig.vo.WecomConfigRespVO;
import com.hnyiti.kuaidi.module.wxmini.service.wecomconfig.WecomConfigService;
import com.hnyiti.kuaidi.module.wxmini.weCom.WeChatGroupLevelEnum;
import com.hnyiti.kuaidi.module.wxmini.weCom.WeChatGroupMessageTypeEnum;
import com.hnyiti.kuaidi.module.wxmini.weCom.WeChatGroupNotifyReq;
import com.hnyiti.kuaidi.vo.*;
import com.hnyiti.kuaidi.yidai.vo.ChannelExpressDetailVO;
import com.sankuai.inf.leaf.service.SegmentService;
import com.taobao.pac.sdk.cp.dataobject.response.GUOGUO_QUERY_SEND_SERVICE_DETAIL.TdAppointTimeDTO;
import io.seata.spring.annotation.GlobalTransactional;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.hnyiti.kuaidi.framework.common.util.date.LocalDateTimeUtils.addTime;
import static com.hnyiti.kuaidi.framework.common.util.servlet.ServletUtils.getClientIP;

@Slf4j
@Service
public class OrderSubmitServiceImpl implements OrderSubmitService {
    @Resource
    private OrderService orderService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private TenantApi tenantApi;
    @Resource
    private ParamApi paramApi;
    @Resource
    private RedissonUtils redissonUtils;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private AddressService addressService;
    @Resource
    private SegmentService segmentService;
    @Resource
    private UserFreezeService userFreezeService;
    @Resource
    private MemberUserApi memberUserApi;
    @Resource
    private OrderParcelService orderParcelService;
    @Resource
    private OrderContactsService contactsService;
    @Resource
    private FeeUserService feeUserService;
    @Resource
    private FeeTenantService feeTenantService;
    @Resource
    private FeeSuperService feeSuperService;
    @Resource
    private SocialUserApi socialUserApi;
    @Resource
    private ExpressProductService expressProductService;
    @Resource
    private EnvProperties envProperties;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ChannelExpressService channelExpressService;
    @Resource
    private ChannelService channelService;
    @Resource
    private OrderMqProducer producer;
    @Resource
    private PayAppService payAppService;
    @Resource
    private PayOrderService payOrderService;
    @Resource
    private WechatPayLogService wechatPayLogService;
    @Lazy
    @Resource
    private OrderCompensateService orderCompensateService;
    @Resource
    private OrderPaymentService orderPaymentService;
    @Lazy
    @Resource
    private BrokerageApi brokerageApi;
    @Resource
    private OrderOverweightCallbackService overweightCallbackService;
    @Lazy
    @Resource
    private OrderProcessService orderProcessService;
    @Lazy
    @Resource
    private FeeSnapshotService feeSnapshotService;
    @Resource
    private MqttSendClient mqttSendClient;
    @Lazy
    @Resource
    private OrderEsService orderEsService;

    @Resource
    private TenantChannelService tenantChannelService;
    @Resource
    private KdChannelService kdChannelService;
    @Resource
    private ThirdParamsService thirdParamsService;
    @Lazy
    @Resource
    private OrderMqProducer orderMqProducer;
    @Resource
    @Lazy
    private ExpressService expressService;
    @Resource
    private WxMqProducer wxMqProducer;
    @Resource
    private CouponService couponService;
    @Resource
    private CouponRecordsService couponRecordsService;
    @Lazy
    @Resource
    private CouponRiskControlService couponRiskControlService;
    @Lazy
    @Resource
    private CouponTemplateService couponTemplateService;
    @Resource
    private WecomConfigService wecomConfigService;

    @Lazy
    @Resource
    private RedisTemplate redisTemplate;

    @Lazy
    @Resource
    private NotifyParamService notifyParamService;
    @Resource
    private MessageSendApi messageSendApi;

    @Lazy
    @Resource
    private BalanceApi balanceApi;

    @Resource
    private ChannelCodeService channelCodeService;

    @Resource
    private ProductPickupRuleService productPickupRuleService;

    @Override
    @GlobalTransactional
    @DSTransactional
    public MiniCreateOrderResVO createOrder(MiniCreateOrderReqVO miniCreateOrderReqVO) {
        log.info("创建订单, {}", miniCreateOrderReqVO);

        MiniCreateOrderResVO res = new MiniCreateOrderResVO();

        // 数据校验
        validateCreate(miniCreateOrderReqVO);
        try {
            // 构建总线对象
            OrderBigVo bigVo = assembleBigVo(miniCreateOrderReqVO);

            // 费用计算
            calculateTotalFees(miniCreateOrderReqVO.getFeeTagId(), bigVo);

            // 保存数据
            saveOrder(bigVo);

            // 构建返回结果
            assembleResult(res, bigVo);
        } catch (Exception e) {
            log.info("订单创建失败：{}", e.getMessage());
            throw new ServiceException(new ErrorCode(500, "创建订单失败，请联系在线客服反馈处理"));
        }

        return res;
    }

    private void assembleResult(MiniCreateOrderResVO res, OrderBigVo bigVo) {
        // 获取订单模版消息
        res.setTempIds(getMiniTemplate());

        res.setDeliveryType(bigVo.getOrderInfo().getDeliveryType());
        res.setLastPrice(bigVo.getFeeUser().getLastPrice());
        res.setOrderId(bigVo.getOrderId());
    }

    /**
     * 批量寄件单个组装订单
     *
     * @param miniCreateOrderReqVO
     * @return
     */
    private OrderBigVo assembleBackgroundBigVo(MiniCreateOrderReqVO miniCreateOrderReqVO) {
        OrderBigVo bigVo = new OrderBigVo();

        Long deliveryOrderId = segmentService.getId("delivery_order").getId();
        bigVo.setOrderId(deliveryOrderId.toString());

        Long userId = SecurityFrameworkUtils.getLoginUserId();
        bigVo.setUserId(userId);

        // 组装订单信息
        assembleOrder(bigVo, miniCreateOrderReqVO);

        // 组装寄件人信息
        assembleSenderV2(bigVo, miniCreateOrderReqVO);

        // 组装收件人信息
        assembleReceiveV2(bigVo, miniCreateOrderReqVO);

        // 组装订单包裹信息
        assembleParcel(bigVo, miniCreateOrderReqVO);

        // 预约时间处理
        adjustAppointmentTimes(bigVo, miniCreateOrderReqVO);

        return bigVo;
    }

    private OrderBigVo assembleBigVo(MiniCreateOrderReqVO miniCreateOrderReqVO) {
        OrderBigVo bigVo = new OrderBigVo();

        Long deliveryOrderId = segmentService.getId("delivery_order").getId();
        bigVo.setOrderId(deliveryOrderId.toString());

//        后台寄件与手动下单共用一个接口：order_type 判断订单类型 1：用户订单、 2：系统订单
        String orderType = miniCreateOrderReqVO.getOrderType();
        if (ObjectUtil.isNotEmpty(orderType) && orderType.equals(OrderConstants.ORDER_TYPE_USER)) {
            bigVo.setUserId(SecurityFrameworkUtils.getLoginUserId());
        }

        if (ObjectUtil.isNotEmpty(miniCreateOrderReqVO.getCouponUserId())) {
            bigVo.setCouponRecordsId(miniCreateOrderReqVO.getCouponUserId());
        }


        // 组装订单信息
        assembleOrder(bigVo, miniCreateOrderReqVO);

        // 组装寄件人信息
        assembleSender(bigVo, miniCreateOrderReqVO);

        // 组装收件人信息
        assembleReceive(bigVo, miniCreateOrderReqVO);

        // 组装订单包裹信息
        assembleParcel(bigVo, miniCreateOrderReqVO);

        // 预约时间处理
        adjustAppointmentTimes(bigVo, miniCreateOrderReqVO);

        return bigVo;
    }

    /**
     * 组装包裹信息
     *
     * @param bigVo
     * @param miniCreateOrderReqVO
     */
    private void assembleParcel(OrderBigVo bigVo, MiniCreateOrderReqVO miniCreateOrderReqVO) {
        OrderParcelDO orderParcelDO = new OrderParcelDO();
        BeanUtil.copyProperties(miniCreateOrderReqVO, orderParcelDO);
        // 用户下单，记录用户下单等级，用于统计
        Long userId = bigVo.getUserId();
        if (null != userId && bigVo.getOrderInfo().getOrderType().equals(OrderConstants.ORDER_TYPE_USER)) {
            MemberUserRespVO userInfo = socialUserApi.getMemberInfo(userId).getData();
            if (userInfo.getFreezeFlag() == 1) {
                throw new ServiceException(new ErrorCode(31501, "账号状态异常"));
            }
            orderParcelDO.setOrderLevel(ObjectUtil.isNotEmpty(userInfo.getUserLevel()) ? userInfo.getUserLevel() : 1L);
        }
        // 需要处理的数据封装
        orderParcelDO.setOrderId(bigVo.getOrderId());

        // 根据渠道获取取件预约规则
//        ExpressProductRespVO productAndPickup = expressProductService.getExpressProductAndPickup(miniCreateOrderReqVO.getExpressProductId());
//        Date pickUpEndTime = miniCreateOrderReqVO.getPickUpEndTime();
//        Date pickUpStartTime = miniCreateOrderReqVO.getPickUpStartTime();
//        int hour = LocalDateTime.now().getHour();
//        if (null != pickUpEndTime && null != pickUpStartTime) {
//            orderParcelDO.setAppointEndTime(pickUpEndTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
//            orderParcelDO.setAppointStartTime(pickUpStartTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
//            // 根据配置判断是否要加一个小时（只有主流快递走这个逻辑，菜鸟裹裹走自己的逻辑）
//            if (ObjectUtil.isNotEmpty(productAndPickup) && "2".equals(productAndPickup.getType())) {
//                if (LocalDate.now().equals(pickUpStartTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())) {
//                    /**
//                     * 配置了最晚预约时间，并且当前时间大于最晚预约时间，分配到第二天。
//                     * 理论上不会出现这种情况，在配置的可预约时间段中，如果当前时间大于则这个时间段不可选
//                     */
//                    if (com.alibaba.nacos.common.utils.StringUtils.isNotEmpty(productAndPickup.getLatestOrderTime())) {
//                        String latestOrderTime = productAndPickup.getLatestOrderTime();
//                        if (Integer.valueOf(latestOrderTime.substring(0, 2)) < hour) {
//                            orderParcelDO.setAppointEndTime(pickUpEndTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusDays(1).withHour(11).withMinute(0).withSecond(0));
//                            orderParcelDO.setAppointStartTime(pickUpEndTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusDays(1).withHour(9).withMinute(0).withSecond(0));
//                        }
//                    }
//                }
//            }
//        }
        bigVo.setOrderParcel(orderParcelDO);
    }

    /**
     * 构建收件人信息
     *
     * @param bigVo
     * @param miniCreateOrderReqVO
     */
    private void assembleReceive(OrderBigVo bigVo, MiniCreateOrderReqVO miniCreateOrderReqVO) {
        String orderType = miniCreateOrderReqVO.getOrderType();
        AddressDO receiveAddress = new AddressDO();
        if (ObjectUtil.isNotEmpty(orderType) && orderType.equals(OrderConstants.ORDER_TYPE_SYS)) {
            receiveAddress.setProvince(miniCreateOrderReqVO.getReceiveProvince()).setCity(miniCreateOrderReqVO.getReceiveCity()).setDistrict(miniCreateOrderReqVO.getReceiveDistrict()).setAddress(miniCreateOrderReqVO.getReceiveAddress()).setContactPerson(miniCreateOrderReqVO.getReceiveName()).setMobileNumber(miniCreateOrderReqVO.getReceiveMobile()).setLandlineNumber(miniCreateOrderReqVO.getReceiveTel()).setIsDefault(false).setType(OrderConstants.ADDRESS_TYPE_RECEIVE);
        } else {
            receiveAddress = addressService.getAddress(miniCreateOrderReqVO.getReceiveAddrId());
        }
        Assert.notNull(receiveAddress, "收件人信息缺失");
        Assert.isTrue(receiveAddress.getAddress().length() >= 4, "收件详细地址不得少于四个字，请补充再下单");
        bigVo.setReceiveContacts(assembleAddress(receiveAddress, bigVo.getOrderId(),OrderConstants.ADDRESS_TYPE_RECEIVE));
    }

    /**
     * 构建收件人信息
     *
     * @param bigVo
     * @param miniCreateOrderReqVO
     */
    private void assembleReceiveV2(OrderBigVo bigVo, MiniCreateOrderReqVO miniCreateOrderReqVO) {
        if (ObjectUtil.isNotEmpty(miniCreateOrderReqVO.getReceiveAddrId())) {
            AddressDO sendAddress = addressService.getAddress(miniCreateOrderReqVO.getReceiveAddrId());
            Assert.notNull(sendAddress, "收件人信息缺失");
            Assert.isTrue(sendAddress.getAddress().length() >= 4, "收件详细地址不得少于四个字，请补充再下单");
            bigVo.setReceiveContacts(assembleAddress(sendAddress, bigVo.getOrderId(),OrderConstants.ADDRESS_TYPE_RECEIVE));
            return;
        }
        Assert.notNull(miniCreateOrderReqVO.getReceiveProvince(), "收件人省份信息缺失");
        Assert.notNull(miniCreateOrderReqVO.getReceiveCity(), "收件人城市信息缺失");
        Assert.notNull(miniCreateOrderReqVO.getReceiveDistrict(), "收件人城区信息缺失");
        Assert.notNull(miniCreateOrderReqVO.getReceiveAddress(), "收件人详细地址信息缺失");
        Assert.isTrue(miniCreateOrderReqVO.getReceiveAddress().length() >= 4, "收件详细地址不得少于四个字，请补充再下单");
        bigVo.setReceiveContacts(assembleAddressV2(miniCreateOrderReqVO, bigVo.getOrderId(), OrderConstants.ADDRESS_TYPE_RECEIVE));
//        AddressDO receiveAddress = addressService.getAddress(miniCreateOrderReqVO.getReceiveAddrId());
//        Assert.notNull(receiveAddress, "收件人信息缺失");
//        Assert.isTrue(receiveAddress.getAddress().length() >= 4, "收件详细地址不得少于四个字，请补充再下单");
//        bigVo.setReceiveContacts(assembleAddress(receiveAddress, bigVo.getOrderId()));
    }

    /**
     * 构建收件、寄件地址信息
     *
     * @param address
     * @return
     */
    private OrderContactsDO assembleAddress(AddressDO address, String orderId, String type) {
        OrderContactsDO contactsDO = new OrderContactsDO();
        contactsDO.setAddress(address.getAddress());
        contactsDO.setCity(address.getCity());
        contactsDO.setDistrict(address.getDistrict());
        contactsDO.setProvince(address.getProvince());
        contactsDO.setMobile(address.getMobileNumber());
        contactsDO.setName(address.getContactPerson());
        contactsDO.setTelephone(address.getLandlineNumber());
        contactsDO.setOrderId(orderId);
        if (OrderConstants.ADDRESS_TYPE_SENDER.equals(type)) {
            contactsDO.setType(OrderConstants.CONTACTS_TYPE_SENDER);
        } else if (OrderConstants.ADDRESS_TYPE_RECEIVE.equals(type)) {
            contactsDO.setType(OrderConstants.CONTACTS_TYPE_RECEIVE);
        }

        return contactsDO;
    }

    /**
     * 构建收件、寄件地址信息
     *
     * @param address
     * @return
     */
    private OrderContactsDO assembleAddressV2(MiniCreateOrderReqVO address, String orderId, String type) {
        OrderContactsDO contactsDO = new OrderContactsDO();
        if (OrderConstants.ADDRESS_TYPE_SENDER.equals(type)) {
            contactsDO.setType(OrderConstants.CONTACTS_TYPE_SENDER);
            contactsDO.setAddress(address.getSenderAddress());
            contactsDO.setCity(address.getSenderCity());
            contactsDO.setDistrict(address.getSenderDistrict());
            contactsDO.setProvince(address.getSenderProvince());
            contactsDO.setMobile(address.getSenderMobile());
            contactsDO.setName(address.getSenderName());
            contactsDO.setTelephone(address.getSenderTel());
//            contactsDO.setTelephone(address.());
            contactsDO.setOrderId(orderId);
        } else if (OrderConstants.ADDRESS_TYPE_RECEIVE.equals(type)) {
            contactsDO.setType(OrderConstants.CONTACTS_TYPE_RECEIVE);
            contactsDO.setAddress(address.getReceiveAddress());
            contactsDO.setCity(address.getReceiveCity());
            contactsDO.setDistrict(address.getReceiveDistrict());
            contactsDO.setProvince(address.getReceiveProvince());
            contactsDO.setMobile(address.getReceiveMobile());
            contactsDO.setTelephone(address.getReceiveTel());
            contactsDO.setName(address.getReceiveName());
            contactsDO.setOrderId(orderId);
        }
        return contactsDO;
    }

    /**
     * 构建寄件人信息
     *
     * @param bigVo
     * @param miniCreateOrderReqVO
     */
    private void assembleSender(OrderBigVo bigVo, MiniCreateOrderReqVO miniCreateOrderReqVO) {
        String orderType = miniCreateOrderReqVO.getOrderType();
        AddressDO sendAddress = new AddressDO();
        if (ObjectUtil.isNotEmpty(orderType) && orderType.equals(OrderConstants.ORDER_TYPE_SYS)) {
            sendAddress.setProvince(miniCreateOrderReqVO.getSenderProvince()).setCity(miniCreateOrderReqVO.getSenderCity()).setDistrict(miniCreateOrderReqVO.getSenderDistrict()).setAddress(miniCreateOrderReqVO.getSenderAddress()).setContactPerson(miniCreateOrderReqVO.getSenderName()).setMobileNumber(miniCreateOrderReqVO.getSenderMobile()).setLandlineNumber(miniCreateOrderReqVO.getSenderTel()).setIsDefault(false).setType(OrderConstants.ADDRESS_TYPE_SENDER);
        } else {
            sendAddress = addressService.getAddress(miniCreateOrderReqVO.getSenderAddrId());
        }
        Assert.notNull(sendAddress, "寄件人信息缺失");
        Assert.isTrue(sendAddress.getAddress().length() >= 4, "寄件详细地址不得少于四个字，请补充再下单");
        bigVo.setSendContacts(assembleAddress(sendAddress, bigVo.getOrderId(),OrderConstants.ADDRESS_TYPE_SENDER));
    }

    /**
     * 构建寄件人信息
     *
     * @param bigVo
     * @param miniCreateOrderReqVO
     */
    private void assembleSenderV2(OrderBigVo bigVo, MiniCreateOrderReqVO miniCreateOrderReqVO) {
        if (ObjectUtil.isNotEmpty(miniCreateOrderReqVO.getSenderAddrId())) {
            AddressDO sendAddress = addressService.getAddress(miniCreateOrderReqVO.getSenderAddrId());
            Assert.notNull(sendAddress, "寄件人信息缺失");
            Assert.isTrue(sendAddress.getAddress().length() >= 4, "寄件详细地址不得少于四个字，请补充再下单");
            bigVo.setSendContacts(assembleAddress(sendAddress, bigVo.getOrderId(),OrderConstants.ADDRESS_TYPE_SENDER));
            return;
        }
        Assert.notNull(miniCreateOrderReqVO.getSenderProvince(), "寄件人省份信息缺失");
        Assert.notNull(miniCreateOrderReqVO.getSenderCity(), "寄件人城市信息缺失");
        Assert.notNull(miniCreateOrderReqVO.getSenderDistrict(), "寄件人城区信息缺失");
        Assert.notNull(miniCreateOrderReqVO.getSenderAddress(), "寄件人详细地址信息缺失");
        Assert.isTrue(miniCreateOrderReqVO.getSenderAddress().length() >= 4, "寄件详细地址不得少于四个字，请补充再下单");
        bigVo.setSendContacts(assembleAddressV2(miniCreateOrderReqVO, bigVo.getOrderId(), OrderConstants.ADDRESS_TYPE_SENDER));
    }

    /**
     * 组装订单信息
     *
     * @param bigVo
     * @param miniCreateOrderReqVO
     */
    private void assembleOrder(OrderBigVo bigVo, MiniCreateOrderReqVO miniCreateOrderReqVO) {
        OrderDO orderDO = new OrderDO();
        BeanUtil.copyProperties(miniCreateOrderReqVO, orderDO);
        orderDO.setId(bigVo.getOrderId());

        // 用户下单，记录用户下单等级，用于统计
        Long userId = bigVo.getUserId();
        if (null != userId && orderDO.getOrderType().equals(OrderConstants.ORDER_TYPE_USER)) {
//            MemberUserRespVO userInfo = socialUserApi.getMemberInfo(userId).getData();
//            if (userInfo.getFreezeFlag() == 1) {
//                throw new ServiceException(new ErrorCode(31501, "账号状态异常"));
//            }
//            orderDO.setOrderLevel(ObjectUtil.isNotEmpty(userInfo.getUserLevel()) ? userInfo.getUserLevel() : 1L);
            orderDO.setUserId(userId);
        }
        orderDO.setStatus(OrderConstants.ORDER_STATUS_TREAT_PAYMENT);
        orderDO.setThirdStatus(OrderConstants.THIRD_STATUS_NOT_CALLED);

//        // 创建订单原价、折扣价、提价金额
//        String defPrice = miniCreateOrderReqVO.getDefPrice();
//        if (null != defPrice) {
//            orderDO.setOriginalPrice(new BigDecimal(defPrice));
//        }
//
//        String price = miniCreateOrderReqVO.getPrice();
//        if (null != price) {
//            orderDO.setDiscountPrice(new BigDecimal(price));
//        }
//
//        BigDecimal lastPrice = orderDO.getLastPrice();
//        BigDecimal discountPrice = orderDO.getDiscountPrice();
//        if (null != lastPrice && null != discountPrice) {
//            orderDO.setRaiseAmount(lastPrice.subtract(discountPrice));
//            orderDO.setReceivableAmount(lastPrice);
//        }

        // 保存渠道编号
        if (ObjectUtil.isNotEmpty(miniCreateOrderReqVO.getExpressProductId())) {
            orderDO.setTenantChannelId(miniCreateOrderReqVO.getExpressProductId());
        }

        bigVo.setOrderInfo(orderDO);
    }

    private void calculateTotalFees(String feeTagId, OrderBigVo bigVo) {
//        Long userId = bigVo.getUserId();
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String orderId = bigVo.getOrderInfo().getId();
        String orderType = bigVo.getOrderInfo().getOrderType();

        String key = "checkPrice" + "::" + userId + "::" + feeTagId;
        if (ObjectUtil.isNotEmpty(orderType) && orderType.equals(OrderConstants.ORDER_TYPE_SYS)) {
            key = "checkPrice" + "::" + null + "::" + feeTagId;
        }
        String priceRes = redisUtils.get(key, String.class);
        log.info("Redis中获取解析后的查价信息：{}", JSONUtil.toJsonStr(priceRes));

        List<QueryPriceResVO> list = JSONUtil.toBean(priceRes, new TypeReference<List<QueryPriceResVO>>() {
        }, true);

        // 查询出当前选择的渠道费用
        QueryPriceResVO queryPriceResVO = list.stream().filter(item -> item.getProductId().equals(bigVo.getOrderInfo().getExpressProductId())).findFirst().orElse(null);

        if (ObjectUtil.isNotEmpty(queryPriceResVO) && ObjectUtil.isNotEmpty(queryPriceResVO.getSourceType())) {
            bigVo.getOrderInfo().setSourceType(queryPriceResVO.getSourceType());
        }

        // 20250311 保存渠道CODE类型——订单类型 （1-普通，2-电商）
        if (ObjectUtil.isNotEmpty(queryPriceResVO) && ObjectUtil.isNotEmpty(queryPriceResVO.getChannelType())) {
            bigVo.getOrderInfo().setChannelType(queryPriceResVO.getChannelType());
        }

        // 组装用户费用信息
        assembleFeeUser(orderId, bigVo, queryPriceResVO);

        // 组装加盟商费用
        assembleFeeTenant(orderId, bigVo, queryPriceResVO);

        // 集团费用
        assembleFeeSuper(orderId, bigVo, queryPriceResVO);

        // 构建定价快照
        assembleFeeSnapshot(bigVo, queryPriceResVO);
    }

    public void updateTotalFees(UpdateFeesRepVO updateFeesRepVO) {
        String orderId = updateFeesRepVO.getOrderId();
        OrderBigVo bigVo = new OrderBigVo();
        // 组装用户费用信息
        assembleUpdateFeeUser(orderId, bigVo, updateFeesRepVO);

        // 组装加盟商费用
        assembleUpdateFeeTenant(orderId, bigVo, updateFeesRepVO);

        // 集团费用
        assembleUpdateFeeSuper(orderId, bigVo, updateFeesRepVO);

        // 修改数据
        updateTotalFees(bigVo);
    }

    /**
     * 修改费用信息
     */
    public void updateTotalFees(OrderBigVo bigVo) {
        if (ObjectUtil.isEmpty(bigVo)) {
            return;
        }

        if (ObjectUtil.isNotEmpty(bigVo.getFeeUser())) {
            // 修改用户费用信息
            feeUserService.updateById(bigVo.getFeeUser());
        }

        if (ObjectUtil.isNotEmpty(bigVo.getFeeTenant())) {
            // 保存代理费用
            feeTenantService.updateById(bigVo.getFeeTenant());
        }

        if (ObjectUtil.isNotEmpty(bigVo.getFeeSuper())) {
            // ���存总部费用
            feeSuperService.updateById(bigVo.getFeeSuper());
        }
    }

    private void assembleFeeSnapshot(OrderBigVo bigVo, QueryPriceResVO queryPriceResVO) {
        FeeSnapshotDO feeSnapshot = new FeeSnapshotDO().setOrderId(bigVo.getOrderId()).setTenantRaiseVersion(queryPriceResVO.getTenantVersion()).setSystemRaiseVersion(queryPriceResVO.getSysVersion()).setVipDiscount(queryPriceResVO.getUserDiscounts());

        // 订单费用快照保存渠道编号
        if (ObjectUtil.isNotEmpty(queryPriceResVO.getProductId())) {
            feeSnapshot.setTenantChannelId(queryPriceResVO.getProductId());
        }

        // 订单费用快照保存-快递保价信息
        if (ObjectUtil.isNotEmpty(queryPriceResVO.getExcelId())) {
            feeSnapshot.setExcelId(queryPriceResVO.getExcelId());
        }

        // 订单费用快照保存-快递渠道价格信息
        if (ObjectUtil.isNotEmpty(queryPriceResVO.getExcelItemId())) {
            feeSnapshot.setExcelItemId(queryPriceResVO.getExcelItemId());
            bigVo.getOrderInfo().setExcelItemId(queryPriceResVO.getExcelItemId());
        }

        if (ObjectUtil.isNotEmpty(queryPriceResVO.getTenantPriceId())) {
            feeSnapshot.setTenantPriceId(queryPriceResVO.getTenantPriceId());
        }


        if (ObjectUtil.isNotEmpty(queryPriceResVO.getSystemPriceId())) {
            feeSnapshot.setSystemPriceId(queryPriceResVO.getSystemPriceId());
        }

        // 模式5下的单，保存的会员折扣率特殊取值
        if (!StringUtil.isBlank(queryPriceResVO.getModeFiveVipDiscount())) {
            feeSnapshot.setModeFiveVipDiscount(queryPriceResVO.getModeFiveVipDiscount());
        }

        // 折扣类型
        if (ObjectUtil.isNotEmpty(queryPriceResVO.getDiscountType())) {
            feeSnapshot.setDiscountType(queryPriceResVO.getDiscountType());
        }

        // 首重优惠
        if (ObjectUtil.isNotEmpty(queryPriceResVO.getFirstWeightDiscount())) {
            feeSnapshot.setFirstWeightDiscount(queryPriceResVO.getFirstWeightDiscount());
        }

        // 续重优惠
        if (ObjectUtil.isNotEmpty(queryPriceResVO.getContinuedWeightDiscount())) {
            feeSnapshot.setContinuedWeightDiscount(queryPriceResVO.getContinuedWeightDiscount());
        }

        // 额外加价
        if (ObjectUtil.isNotEmpty(queryPriceResVO.getExtraCharge())) {
            feeSnapshot.setExtraCharge(queryPriceResVO.getExtraCharge());
        }

        bigVo.setFeeSnapshot(feeSnapshot);
    }

    // 组装集团商费用信息
    private void assembleFeeSuper(String orderId, OrderBigVo bigVo, QueryPriceResVO queryPriceResVO) {
        // 加盟商支付金额
        BigDecimal tenantDueAmount = queryPriceResVO.getTenantPayAmount();
        // 成本
        BigDecimal price = queryPriceResVO.getPrice();

        // 集团费用
        FeeSuperDO feeSuper = new FeeSuperDO();
        feeSuper.setDueAmount(tenantDueAmount);
        feeSuper.setCost(price);
        feeSuper.setFirstCost(price);
        feeSuper.setOrderId(orderId);
        // 计算加盟商利润
        if (ObjectUtil.isNotEmpty(price) && ObjectUtil.isNotEmpty(tenantDueAmount)) {
            feeSuper.setProfitAmount(tenantDueAmount.subtract(price));
        }

        // 记录第三方首续重价格
        if (ObjectUtil.isNotEmpty(queryPriceResVO.getThirdContinuedWeightPrice()) && ObjectUtil.isNotEmpty(queryPriceResVO.getThirdFirstWeightPrice())) {
            feeSuper.setFirstAmount(queryPriceResVO.getThirdFirstWeightPrice());
            feeSuper.setRenewalAmount(queryPriceResVO.getThirdContinuedWeightPrice());
        }

        bigVo.setFeeSuper(feeSuper);
    }

    // 组装集团商费用信息
    private void assembleUpdateFeeSuper(String orderId, OrderBigVo bigVo, UpdateFeesRepVO updateFeesRepVO) {
        // 查询是否舒服存在
        FeeSuperDO feeSuperDO = feeSuperService.getFeeSuperByOrderId(orderId);
        if (ObjectUtil.isEmpty(feeSuperDO)) {
            return;
        }
        Boolean isUpdate = false;
        FeeSuperDO updateFeeSuperDO = new FeeSuperDO();

//        updateFeeSuperDO.setId(feeSuperDO.getId());

        // 加盟商支付金额
        BigDecimal tenantDueAmount = updateFeesRepVO.getTenantPayAmount();
        // 成本
        BigDecimal price = updateFeesRepVO.getPrice();

        // 集团费用
        if (ObjectUtil.isNotEmpty(tenantDueAmount)) {
            isUpdate = true;
            updateFeeSuperDO.setDueAmount(tenantDueAmount);
        }
        if (ObjectUtil.isNotEmpty(price)) {
            isUpdate = true;
            updateFeeSuperDO.setCost(price);
        }
        // 计算加盟商利润
        if (ObjectUtil.isNotEmpty(price) && ObjectUtil.isNotEmpty(tenantDueAmount)) {
            isUpdate = true;
            updateFeeSuperDO.setProfitAmount(tenantDueAmount.subtract(price));
        }
        if (isUpdate) {
            bigVo.setFeeSuper(updateFeeSuperDO);
        }
    }


    // 组装用户费用信息
    private void assembleFeeUser(String orderId, OrderBigVo bigVo, QueryPriceResVO queryPriceResVO) {


        BigDecimal lastPrice = queryPriceResVO.getLastPrice();
        // 首重价格
        BigDecimal defFirstPrice = queryPriceResVO.getFirstWeightPrice();
        // 续重价格
        BigDecimal defOverPrice = queryPriceResVO.getAddPrice();
        // 折扣率
        String userDiscounts = queryPriceResVO.getUserDiscounts();
        // 用户费用信息
        FeeUserDO feeUser = new FeeUserDO();
        feeUser.setOrderId(orderId);
        feeUser.setWeight(bigVo.getOrderParcel().getWeight());
        feeUser.setLastPayAmount(lastPrice);
        feeUser.setLastPrice(lastPrice);
        if (ObjectUtil.isNotEmpty(defFirstPrice)) {
            feeUser.setFirstAmount(defFirstPrice);
        }
        if (ObjectUtil.isNotEmpty(defOverPrice)) {
            feeUser.setRenewalAmount(defOverPrice);
        }


        // 判断用户等级
        String orderType = bigVo.getOrderInfo().getOrderType();
        if (ObjectUtil.isNotEmpty(orderType) && orderType.equals(OrderConstants.ORDER_TYPE_USER)) {
            Long userId = bigVo.getUserId();
            MemberUserRespDTO user = memberUserApi.getUser(userId);
            if (null != user && user.getUserLevel() != 1) {
                feeUser.setDiscountRate(userDiscounts);
            }
        }


        if (ObjectUtil.isNotEmpty(bigVo.getCouponRecordsId())) {
            // 优惠券减免金额
            BigDecimal reliefAmount = couponRecordsService.getCouponReliefAmount(lastPrice, bigVo.getCouponRecordsId());
            // 查询优惠券模板信息
            CouponRecordRespVO couponRecordRespVO = couponRecordsService.getUserCouponDetails(bigVo.getCouponRecordsId());
            String couponAmount = "";

            if (ObjectUtil.isNotEmpty(couponRecordRespVO)) {
                if (2 == couponRecordRespVO.getType()) {
                    couponAmount = couponRecordRespVO.getDiscountValue() + "%";
                } else {
                    couponAmount = couponRecordRespVO.getDiscountValue() + "";
                }
            }
            feeUser.setCouponId(bigVo.getCouponRecordsId());
            feeUser.setReliefAmount(reliefAmount);
            feeUser.setCouponCost(ObjectUtil.isNotEmpty(couponRecordRespVO) ? couponRecordRespVO.getCost() : null);
            feeUser.setCouponAmount(couponAmount);
            // 重新赋值最终支付金额
            feeUser.setLastPayAmount(lastPrice.subtract(reliefAmount));

            // 修改优惠券相关记录
            CouponRecordDO updateCouponRecordDO = new CouponRecordDO();
            updateCouponRecordDO.setId(bigVo.getCouponRecordsId());
            updateCouponRecordDO.setStatus(CouponConstants.COUPON_STATUS_RECORDS_USED);
            updateCouponRecordDO.setUsedAmount(reliefAmount);
            updateCouponRecordDO.setUsedTime(new Date());

            bigVo.setUpdateCouponRecordDO(updateCouponRecordDO);
        }

        bigVo.setFeeUser(feeUser);
    }

    // 组装修改用户费用信息
    private void assembleUpdateFeeUser(String orderId, OrderBigVo bigVo, UpdateFeesRepVO updateFeesRepVO) {
        // 查询是否存在
        FeeUserDO feeUserDO = feeUserService.getFeeUserByOrderId(orderId);
        if (ObjectUtil.isEmpty(feeUserDO)) {
            return;
        }
        Boolean isUpdate = false;

        FeeUserDO updateFeeUser = new FeeUserDO();
//        updateFeeUser.setId(feeUserDO.getId());

        BigDecimal lastPrice = updateFeesRepVO.getLastPrice();
        // 首重价格
        String defFirstPrice = updateFeesRepVO.getDefFirstPrice();
        // 续重价格
        String defOverPrice = updateFeesRepVO.getDefOverPrice();
        // 用户费用信息
        if (ObjectUtil.isNotEmpty(updateFeesRepVO.getWeight())) {
            isUpdate = true;
            updateFeeUser.setWeight(updateFeesRepVO.getWeight());
        }
        if (ObjectUtil.isNotEmpty(lastPrice)) {
            isUpdate = true;
            updateFeeUser.setPayAmount(lastPrice);
        }
        if (ObjectUtil.isNotEmpty(defFirstPrice)) {
            isUpdate = true;
            updateFeeUser.setFirstAmount(new BigDecimal(defFirstPrice));
        }
        if (ObjectUtil.isNotEmpty(defOverPrice)) {
            isUpdate = true;
            updateFeeUser.setRenewalAmount(new BigDecimal(defOverPrice));
        }
        if (isUpdate) {
            bigVo.setFeeUser(updateFeeUser);
        }
    }


    // 组装加盟商费用信息
    private void assembleFeeTenant(String orderId, OrderBigVo bigVo, QueryPriceResVO queryPriceResVO) {
        // 加盟商支付金额
        BigDecimal tenantDueAmount = queryPriceResVO.getTenantPayAmount();
        // 用户最终价格
        BigDecimal lastPrice = queryPriceResVO.getLastPrice();
        // 成本
        BigDecimal price = queryPriceResVO.getPrice();

        // 加盟商费用
        FeeTenantDO feeTenant = new FeeTenantDO();
        feeTenant.setOrderId(orderId);
        feeTenant.setDueAmount(lastPrice);
        // 疑点：此处存放的是加盟商的成本价还是总部的？
        feeTenant.setCost(tenantDueAmount);

        // 计算加盟商利润
        if (ObjectUtil.isNotEmpty(tenantDueAmount) && ObjectUtil.isNotEmpty(lastPrice)) {
//            feeTenant.setProfitAmount(lastPrice.subtract(tenantDueAmount));
            feeTenant.setProfitAmount(getCreatOrderProfitAmount(lastPrice, tenantDueAmount, ObjectUtil.isNotEmpty(bigVo.getFeeUser()) ? bigVo.getFeeUser().getReliefAmount() : null, ObjectUtil.isNotEmpty(bigVo.getFeeUser()) ? bigVo.getFeeUser().getCouponCost() : null));
        }

        bigVo.setFeeTenant(feeTenant);
    }

    /**
     * @param payAmount  支付金额
     * @param orderCost  订单成本
     * @param couponCost 优惠券沉成本
     * @return
     */
    public BigDecimal getCreatOrderProfitAmount(BigDecimal payAmount, BigDecimal orderCost, BigDecimal reliefAmount, BigDecimal couponCost) {

        BigDecimal profitAmount = payAmount.subtract(orderCost);

        // 减去减免金额
        if (ObjectUtil.isNotEmpty(reliefAmount)) {
            profitAmount = profitAmount.subtract(reliefAmount);
        }

        // 加上优惠券成本
        if (ObjectUtil.isNotEmpty(couponCost)) {
            profitAmount = profitAmount.add(couponCost);
        }

        return profitAmount;
    }


    /**
     * @param payAmount  支付金额
     * @param orderCost  订单成本
     * @param couponCost 优惠券沉成本
     * @return
     */
    public BigDecimal getProfitAmount(BigDecimal payAmount, BigDecimal orderCost, BigDecimal couponCost) {

        BigDecimal profitAmount = ObjectUtil.isNotEmpty(orderCost) ? payAmount.subtract(orderCost) : BigDecimal.ZERO;

        // 减去减免金额
//        if(ObjectUtil.isNotEmpty(reliefAmount)){
//            profitAmount = profitAmount.subtract(reliefAmount);
//        }

        // 加上优惠券成本
        if (ObjectUtil.isNotEmpty(couponCost)) {
            profitAmount = profitAmount.add(couponCost);
        }

        return profitAmount;
    }

    // 组装修改加盟商费用信息
    private void assembleUpdateFeeTenant(String orderId, OrderBigVo bigVo, UpdateFeesRepVO updateFeesRepVO) {
        // 查询是否存在
        FeeTenantDO feeTenantDO = feeTenantService.getFeeTenantByOrderId(orderId);

        if (ObjectUtil.isEmpty(feeTenantDO)) {
            return;
        }

        Boolean isUpdate = false;

        FeeTenantDO updateFeeTenantDO = new FeeTenantDO();

//        updateFeeTenantDO.setId(feeTenantDO.getId());
        // 加盟商支付金额
        BigDecimal tenantDueAmount = updateFeesRepVO.getTenantPayAmount();
        // 用户最终价格
        BigDecimal lastPrice = updateFeesRepVO.getLastPrice();
        // 成本
        BigDecimal price = updateFeesRepVO.getPrice();

        // 加盟商费用
        if (ObjectUtil.isNotEmpty(lastPrice)) {
            isUpdate = true;
            updateFeeTenantDO.setDueAmount(lastPrice);
        }
        if (ObjectUtil.isNotEmpty(tenantDueAmount)) {
            isUpdate = true;
            // 疑点：此处存放的是加盟商的成本价还是总部的？
            updateFeeTenantDO.setCost(tenantDueAmount);
        }

        // 计算加盟商利润
        if (ObjectUtil.isNotEmpty(tenantDueAmount) && ObjectUtil.isNotEmpty(lastPrice)) {
            isUpdate = true;
            updateFeeTenantDO.setProfitAmount(lastPrice.subtract(tenantDueAmount));
        }

        if (isUpdate) {
            bigVo.setFeeTenant(updateFeeTenantDO);
        }
    }


    /**
     * 获取到小程序推送模版
     *
     * @return
     */
    private List<String> getMiniTemplate() {
        return orderService.getOrderTmplIds();
    }

    @Resource
    private CommissionRecordsService commissionRecordsService;

    @Resource
    private OrderFeeSuperEsService orderFeeSuperEsService;

    private void saveOrder(OrderBigVo bigVo) {
        // 保存基本信息
        orderService.insert(bigVo.getOrderInfo());

        // 保存包裹信息
        orderParcelService.insert(bigVo.getOrderParcel());

        // 寄件人、收件人信息
        contactsService.insertBatch(Arrays.asList(bigVo.getSendContacts(), bigVo.getReceiveContacts()));

        // 保存用户费用信息
        feeUserService.insert(bigVo.getFeeUser());

        // 保存代理费用
        feeTenantService.insert(bigVo.getFeeTenant());

        // 保存总部费用
        feeSuperService.insert(bigVo.getFeeSuper());

        // 保存快照表信息
        feeSnapshotService.insert(bigVo.getFeeSnapshot());

        // 保存订单分佣和风控状态
        commissionRecordsService.create(bigVo.getOrderId());

        try {
            // 发送订单同步到PostgreSQL消息
            orderMqProducer.sendOrderSyncPgsql(bigVo.getOrderId(), "add");
        } catch (Exception e) {
            log.error("创建订单发送订单同步到PostgreSQL消息失败，订单ID：{}，错误信息：{}", bigVo.getOrderId(), e.getMessage());
        }


        // 修改优惠券状态
        if (ObjectUtil.isNotEmpty(bigVo.getUpdateCouponRecordDO())) {
            couponService.markCouponAsUsed(bigVo.getUpdateCouponRecordDO().getId(), Long.parseLong(bigVo.getOrderId()), bigVo.getUpdateCouponRecordDO().getUsedAmount());
//            couponRecordMapper.updateById(bigVo.getUpdateCouponRecordDO());
        }

        // 保存订单数据到ES
        // TODO 2024-12-10 取消订单相关ES操作
//        try {
//            bigVo.getOrderInfo().setTenantId(TenantContextHolder.getTenantId());
//            orderEsService.insertOrderES(new ESOrderDO()
//                    .setId( TenantContextHolder.getTenantId() + "_" + bigVo.getOrderInfo().getId())
//                    .setUserId(bigVo.getOrderInfo().getUserId())
//                    .setOrderInfo(bigVo.getOrderInfo())
//                    .setSenderContactInfo(bigVo.getSendContacts())
//                    .setReceiverContactInfo(bigVo.getReceiveContacts())
//                    .setParcelInfo(bigVo.getOrderParcel()));
//        }catch (Exception e) {
//            log.error("用户下单信息保存ES失败：{}",e.getMessage());
//        }
    }

//    private void saveFeeSuperEs(OrderBigVo bigVo) {
//        try {
//            ESOrderFeeSuperDO esSuperDO = new ESOrderFeeSuperDO();
//            esSuperDO.setCost(bigVo.getFeeSuper().getCost());
//            esSuperDO.setOrderId(bigVo.getFeeSuper().getOrderId());
//            esSuperDO.setOrderNo(bigVo.getOrderInfo().getOrderNo());
//            esSuperDO.setChannelId(bigVo.getOrderInfo().getChannel());
//            esSuperDO.setTenantId(bigVo.getOrderInfo().getTenantId());
//           //  esSuperDO.setCreateTime(LocalDateTime.now());
//            orderFeeSuperEsService.insertOrderFeeSuperES(esSuperDO);
//        } catch (Exception e) {
//            log.error("保存总部费用至ES失败，订单ID：{}, 异常信息：{}", bigVo.getFeeSuper().getOrderId(), e);
//        }
//    }

    private void validateCreate(MiniCreateOrderReqVO miniCreateOrderReqVO) {
        // 订单重复提交校验
        checkDuplicateSubmission(miniCreateOrderReqVO);

        // 订单参数校验
        validateCreateParams(miniCreateOrderReqVO);

        // 用户状态校验。此方法前置到 /preOrder 接口里面去了
        // validateUserStatus(miniCreateOrderReqVO);

        // 代理商校验
        validateAgent();

        // 优惠券风控
        couponRiskControl(miniCreateOrderReqVO);


        // 费用校验
        //  validateFees(miniCreateOrderReqVO);
    }

    public void couponRiskControl(MiniCreateOrderReqVO miniCreateOrderReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 判断是否使用了优惠券
        if (ObjectUtil.isNotEmpty(miniCreateOrderReqVO.getCouponUserId())) {
            // 检查优惠券是否开启
            String couponStatus = paramApi.getCouponStatus(TenantContextHolder.getTenantId());

            if (!"0".equals(couponStatus)) {
                throw new ServiceException(new ErrorCode(500, "优惠券功能已关闭。"));
            }

            // 判断当前优惠券是否有限
            CouponRecordRespVO couponRecordRespVO = couponRecordsService.getUserCouponDetails(miniCreateOrderReqVO.getCouponUserId());

            if (ObjectUtil.isEmpty(couponRecordRespVO)) {
                throw new ServiceException(new ErrorCode(500, "当前优惠券已停用。"));
            }
            if (ObjectUtil.isNotEmpty(couponRecordRespVO.getTemplateStatus()) && 1 != couponRecordRespVO.getTemplateStatus()) {
                throw new ServiceException(new ErrorCode(500, "当前优惠券已停用。"));
            }
            if (ObjectUtil.isNotEmpty(couponRecordRespVO.getStatus()) && couponRecordRespVO.getStatus() != 0) {
                String statusMsg = "";
                switch (couponRecordRespVO.getStatus()) {
                    case 1:
                        statusMsg = "已使用";
                        break;
                    case 2:
                        statusMsg = "已过期";
                        break;
                    case 3:
                        statusMsg = "已冻结";
                        break;
                    case 4:
                        statusMsg = "已作废";
                        break;
                    default:
                        statusMsg = "状态异常";
                }
                throw new ServiceException(new ErrorCode(500, "当前优惠券" + statusMsg));
            }

            // 查询用户信息
            String ipAddress = ServletUtils.getClientIP();
            // 获取当前用户登陆ip
            MemberUserRespDTO memberUserApiUser = memberUserApi.getUser(userId);
            if (ObjectUtil.isNotEmpty(memberUserApiUser) && (ObjectUtil.isNotEmpty(memberUserApiUser.getLoginIp()) || ObjectUtil.isNotEmpty(memberUserApiUser.getRegisterIp()))) {
                ipAddress = ObjectUtil.isNotEmpty(memberUserApiUser.getLoginIp()) ? memberUserApiUser.getLoginIp() : memberUserApiUser.getRegisterIp();
            }
            // 优惠券风控
            couponRiskControlService.checkUseRisk(userId, miniCreateOrderReqVO.getCouponUserId(), ipAddress);
        }
    }

    /**
     * 代理商校验
     * 1. 状态非冻结
     * 2. 保证金足够
     * 3. 余额大于阈值 100
     */
    private void validateAgent() {
        Long tenantId = TenantContextHolder.getTenantId();
        CommonResult<TenantRespVO> tenantResult = tenantApi.getTenantInfo(tenantId);
        Assert.isTrue(tenantResult.isSuccess(), "查询加盟商信息失败");

        TenantRespVO tenantRespVO = tenantResult.getData();
        // 加盟商状态 冻结不能下单
        Assert.isTrue(CommonStatusEnum.ENABLE.getStatus().equals(tenantRespVO.getStatus()), "加盟商状态异常");

        // 余额
        BigDecimal balanceAmount = tenantRespVO.getBalanceAmount();
        // 余额限制阈值
        BigDecimal safetyDeposit = tenantRespVO.getSafetyDeposit();
        if (balanceAmount == null) {
            balanceAmount = BigDecimal.ZERO;
        }
        if (safetyDeposit == null) {
            safetyDeposit = BigDecimal.ZERO;
        }
        Assert.isTrue(balanceAmount.compareTo(safetyDeposit) > 0, "加盟商余额小于阈值");

        // 保证金
        BigDecimal marginAmount = tenantRespVO.getMarginAmount();
        if (marginAmount == null) {
            marginAmount = BigDecimal.ZERO;
        }
        Assert.isTrue(marginAmount.compareTo(new BigDecimal(100)) > 0, "加盟商保证金小于阈值");

    }

    private void validateFees(MiniCreateOrderReqVO reqVo) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String feeTagId = reqVo.getFeeTagId();
        if (StrUtil.isNotEmpty(feeTagId)) {
            String key = "checkPrice" + "::" + userId + "::" + feeTagId;
            String priceRes = redisUtils.get(key, String.class);
            Assert.isTrue(!StringUtils.isEmpty(priceRes), "费用已变更请重新进入快递列表");
        }
    }


    /**
     * 调整预约取件时间。
     * 根据提供的产品信息和预约时间，确保预约时间符合业务规则。
     *
     * @param bigVo                订单对象
     * @param miniCreateOrderReqVO 订单创建请求的详细信息
     */
    private void adjustAppointmentTimes(OrderBigVo bigVo, MiniCreateOrderReqVO miniCreateOrderReqVO) {
        OrderParcelDO orderParcelDO = bigVo.getOrderParcel();
        ExpressProductRespVO productAndPickup = expressProductService.getExpressProductAndPickup(miniCreateOrderReqVO.getExpressProductId());

        if (miniCreateOrderReqVO.getPickUpEndTime() != null && miniCreateOrderReqVO.getPickUpStartTime() != null) {
            orderParcelDO.setAppointEndTime(convertToLocalDateTime(miniCreateOrderReqVO.getPickUpEndTime()));
            orderParcelDO.setAppointStartTime(convertToLocalDateTime(miniCreateOrderReqVO.getPickUpStartTime()));

            if (productAndPickup != null && "2".equals(productAndPickup.getType())) {
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime pickUpStart = orderParcelDO.getAppointStartTime();

                if (now.toLocalDate().isEqual(pickUpStart.toLocalDate()) && !isBeforeLatestOrderTime(now, productAndPickup)) {
                    adjustForNextDay(orderParcelDO);
                }
            }
        }
    }

    /**
     * 将日期转换为本地时间。
     *
     * @param date 需要转换的日期
     * @return 转换后的本地时间
     */
    private LocalDateTime convertToLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * 检查当前时间是否超过最晚预约时间。
     *
     * @param now              当前时间
     * @param productAndPickup 快递产品和取件预约规则
     * @return 如果当前时间超过最晚预约时间返回true，否则返回false
     */
    private boolean isBeforeLatestOrderTime(LocalDateTime now, ExpressProductRespVO productAndPickup) {
        String latestOrderTime = productAndPickup.getLatestOrderTime();
        if (latestOrderTime != null) {
            int latestHour = Integer.parseInt(latestOrderTime.substring(0, 2));
            return now.getHour() < latestHour;
        }
        return true; // 如果没有配置最晚预约时间，默认为true
    }

    /**
     * 将预约时间调整到次日。
     * 根据业务规则，如果当前时间超过了最晚预约时间，
     * 则将预约时间调整到次日的指定时间段内。
     *
     * @param orderParcelDO 订单包裹信息对象
     */
    private void adjustForNextDay(OrderParcelDO orderParcelDO) {
        LocalDateTime nextDayStart = orderParcelDO.getAppointStartTime().plusDays(1).withHour(9).withMinute(0).withSecond(0);
        LocalDateTime nextDayEnd = nextDayStart.plusHours(2);
        orderParcelDO.setAppointStartTime(nextDayStart);
        orderParcelDO.setAppointEndTime(nextDayEnd);
    }

    /**
     * 用户状态校验
     * 1. 非黑名单用户
     * 2. 没有超重补差的订单
     */
    public void validateUserStatus(MiniCreateOrderReqVO miniCreateOrderReqV) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        if (userId == null) {
            throw new RuntimeException("用户未登录");
        }

        MemberUserRespVO userInfo = socialUserApi.getMemberInfo(userId).getData();
        Assert.notNull(userInfo, "当前用户不存在");
        Assert.isTrue(userInfo.getFreezeFlag() != 1, "账号状态异常");

        // 黑名单规则校验
        CommonResult<String> userOpenId = socialUserApi.getUserOpenId(userId, null, SocialTypeEnum.WECHAT_MINI_APP.getType());

        // 后台下单不校验黑名单
        if (ObjectUtil.isNotEmpty(miniCreateOrderReqV.getOrderType()) && OrderConstants.ORDER_TYPE_USER.equals(miniCreateOrderReqV.getOrderType())) {
            // Boolean fal = userFreezeService.isBlacklist(userInfo.getMobile(), userInfo.getUnionId(), null);
            Boolean fal = false;

            fal = userFreezeService.isBlacklist(miniCreateOrderReqV, userInfo, userOpenId);

            Long tenantId = TenantContextHolder.getTenantId();
            if (ObjectUtil.isEmpty(tenantId)) {
                tenantId = 1L;
            }
            String blacklistMessage = paramApi.getBlacklistFParam(tenantId);
            Assert.isTrue(!fal, StringUtil.isNotEmpty(blacklistMessage) ? blacklistMessage : "当前下单人已被拉黑");
        }

        // 当前用户有超重补差的订单不允许下单
        boolean isOverweight = orderService.isExistsOverweight(userId);
        if (isOverweight) {
            throw new ServiceException(new ErrorCode(902, "有补差价订单未补"));
        }
    }

    /**
     * 订单参数校验
     * 1. 地址详情长度大于4
     * 2. 渠道产品不能为空 expressProductId
     */
    private void validateCreateParams(MiniCreateOrderReqVO miniCreateOrderReqV) {
        AddressDO sendAddress = addressService.getAddress(miniCreateOrderReqV.getSenderAddrId());
        Assert.notNull(sendAddress, "寄件人信息缺失");
        Assert.isTrue(sendAddress.getAddress().length() >= 4, "寄件详细地址不得少于四个字，请补充再下单");

        AddressDO receiveAddress = addressService.getAddress(miniCreateOrderReqV.getReceiveAddrId());
        Assert.notNull(receiveAddress, "收件人信息缺失");
        Assert.isTrue(receiveAddress.getAddress().length() >= 4, "收件详细地址不得少于四个字，请补充再下单");

        Assert.notNull(miniCreateOrderReqV.getExpressProductId(), "没有选择快递信息");


        Long channelExpressId = miniCreateOrderReqV.getChannelExpressId();
        /*ChannelExpressDO channelExpress = expressService.getChannelExpress(channelExpressId);
        Long channel = channelExpress.getChannelId();
        Assert.notNull(channel, "没有找到订单渠道信息，请联系管理员处理");

        // 查询订单渠道是否开通
        if (!channelService.isChannelOpen(channel)) {
            log.error("当前下单渠道还未开通, {}", channel);
            throw new ServiceException(new ErrorCode(31502, "当前渠道已关闭"));
        }*/

        // 查询总部渠道状态
        if (!kdChannelService.isChannelOpen(channelExpressId)) {
            log.error("当前下单渠道还未开通, {}", channelExpressId);
            throw new ServiceException(new ErrorCode(31502, "当前渠道已关闭"));
        }

        // 查询代理商渠道状态
        Long tenantId = TenantContextHolder.getTenantId();
        if (!tenantChannelService.isChannelOpen(channelExpressId, tenantId)) {
            log.error("当前下单渠道还未开通, {}", channelExpressId);
            throw new ServiceException(new ErrorCode(31502, "当前渠道已关闭"));
        }

        Assert.notNull(miniCreateOrderReqV.getChannelExpressId(), "没有选择快递信息");
    }

    /**
     * 订单重复提交校验
     */
    private void checkDuplicateSubmission(MiniCreateOrderReqVO miniCreateOrderReqV) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Long tenantId = TenantContextHolder.getTenantId();
        if (ObjectUtil.isNotEmpty(userId)) {
            String key = tenantId + "_" + userId;
            if (!stringRedisTemplate.opsForValue().setIfAbsent("lock::user_create_order::" + key, "", 5, TimeUnit.SECONDS)) {
                throw new ServiceException(new ErrorCode(31501, "订单已提交，请勿重复操作"));
            }
        }
    }




    @Override
    @GlobalTransactional
    @DSTransactional
    public Map submitOrder(MiniOrderPayReqVO params) {
        String orderId = params.getOrderId();
        OrderDO order = orderService.getOrder(orderId);

        // lock
        checkDuplicateSubmit(order.getTenantId(), order.getId());

        try {

            // 构建扣费对象、构建订单对象
            OrderBigVo orderBigVo = orderService.assembleBigVo(orderId);

            // 参数校验
            validateSubmit(params, order, orderBigVo.getFeeTenant().getCost());

            // 如果用了优惠券，需支付金额为0的话直接下单
            if (ObjectUtil.isNotEmpty(orderBigVo.getFeeUser()) && ObjectUtil.isNotEmpty(orderBigVo.getFeeUser().getCouponId()) && orderBigVo.getFeeUser().getLastPayAmount().compareTo(BigDecimal.ZERO) <= 0) {
                // 优惠券支付
                order.setPayType(OrderConstants.ORDER_PAY_TYPE_THREE);
                toThirdPartyOrder(order, orderBigVo, OrderConstants.ORDER_PAY_TYPE_THREE, orderBigVo.getFeeUser().getCouponId());
                return new HashMap();
            }


            // 支付渠道
            String payType = params.getPayType();
            if (OrderConstants.PAY_TYPE_BALANCE.equals(payType)) {
                // 第三方只要下单成功一次
                if (!OrderConstants.THIRD_STATUS_CALLED.equals(order.getThirdStatus())) {
                    // 用户余额支付扣款
                    userDeduct(orderBigVo);
                    order.setPayType(payType);
                    toThirdPartyOrder(order, orderBigVo, payType, null);
                }
            } else if (OrderConstants.PAY_TYPE_WECHAT.equals(payType)) {
                // 用户微信支付返回支付参数
                return getWechatPayParams(params, orderBigVo);
//                return getWechatPayParams(params, order);
            } else if (OrderConstants.PAY_TYPE_ALIPAY.equals(payType)) {
                Map<String, String> channelExtras = new HashMap<>();
                channelExtras.put("orderId", order.getId());
                channelExtras.put("type", "deliveryV3");
                PayStrategy payHandler = PayStrategyFactory.createPayHandler(PayLogConstants.PAY_LOG_PAY_TYPE_ALIPAY);
                Map returnMap = payHandler.createPayOrder(new PayRequest().setDisplayMode(params.getDisplayMode()).setPaymentChannel(params.getPaymentChannel()).setCode(params.getCode()).setOrderNo(order.getOrderNo()).setBody(order.getDeliveryType()).setSubject("快递寄件").setChannelExtras(channelExtras).setAmount(orderBigVo.getFeeUser().getLastPayAmount()).setOrderId(order.getId()).setBizId(order.getId()).setBizType(FeeConstants.WECHAT_PAY_BIZ_ORDERPAY));
                return returnMap;
                // 用户支付宝支付返回支付参数
//                return getWechatPayParams(params, order);
            }
        } finally {
            // unlock解锁
            unlockOrder(order.getTenantId(), orderId);
        }

        // 异步风控
        return new HashMap();
    }

    /**
     * 优惠券支付
     */
    public void couponPayment() {

    }

    @Override
    public Map submitDepponOrder(MiniOrderPayReqVO params) {
        String orderId = params.getOrderId();
        OrderDO order = orderService.getOrder(orderId);

        // lock
        checkDuplicateSubmit(order.getTenantId(), order.getId());
        try {
            // 构建扣费对象、构建订单对象
            OrderBigVo orderBigVo = orderService.assembleBigVo(orderId);

            // 构建第三方下单参数
            CreateOrderVO createOrderVO = buildThirdPartyOrderParams(orderBigVo);

            // 构建订单详情修改对象
            OrderParcelUpdateReqVO updateParcelDO = new OrderParcelUpdateReqVO();
            updateParcelDO.setId(orderBigVo.getOrderParcel().getId());

            createOrderVO.setTransportType(params.getTransportType());
            createOrderVO.setPayType(params.getPayType());
            // 调用第三方接口下单
            callThirdOrderApi(createOrderVO, order, updateParcelDO);

            // 修改订单信息
            order.setPayType(params.getPayType());
            syncUpdatedOrders(order);

            // 修改订单详情
            orderParcelService.updateOrderParcel(updateParcelDO);

            // 保存总部费用至ES
            orderBigVo.getOrderInfo().setOrderNo(order.getOrderNo());
//            saveFeeSuperEs(orderBigVo);

            try {
                // WebSocket推送订单状态
                String statusLabel = DictFrameworkUtils.getDictDataLabel(DictTypeConstants.KUAIDI_STATUS, order.getStatus());
                String topic = "msg/" + order.getTenantId() + "/" + order.getUserId();
                PushMqttVO pushMqttVO = new PushMqttVO();
                BeanUtil.copyProperties(order, pushMqttVO);
                pushMqttVO.setStatusStr(statusLabel);
                mqttSendClient.publish(false, topic, JsonUtils.toJsonString(pushMqttVO));
            } catch (Exception e) {
                log.error("WebSocket推送订单状态失败：{ }", e);
            }

            try {
// TODO 2025-5-19 coreData服务删除，相关代码删除
//                if (!OrderConstants.ORDER_STATUS_WAITING_ABNORMAL.equals(order.getStatus())) {
//                    // 订单信息上报mq
//                    producer.sendOrderReported(order, OrderConstants.ORDER_REPORTED_ADD);
//                }

                // TODO 2024-12-10 取消订单相关ES操作
//                orderEsService.updateOrderES(new ESOrderDO()
//                        .setId(TenantContextHolder.getTenantId() + "_" + order.getId())
//                        .setUserId(order.getUserId())
//                        .setOrderInfo(order));
            } catch (Exception e) {
                log.error("订单支付上报COREDATA异常：{}", e.getMessage());
            }

        } finally {
            // unlock解锁
//            unlockOrder(order.getTenantId(), orderId);
        }

        // 异步风控
        return new HashMap();
    }

    /**
     * unlock解锁
     *
     * @param tenantId
     * @param orderId
     */
    private void unlockOrder(Long tenantId, String orderId) {
        String redisLockKey = tenantId + "_" + orderId;

        RLock lock = redissonClient.getLock(redisLockKey);
        RLock wxPayLock = redissonClient.getLock("wxpay::lock::" + redisLockKey);

        lock.unlock();
        wxPayLock.unlock();
    }

    @Override
    public void calculateCommissionAsync(OrderDO order, OrderBigVo orderBigVo, BigDecimal payAmount) {
        // 如果使用了优惠券的话，查询配置看是否需要分佣
        if (ObjectUtil.isNotEmpty(orderBigVo.getFeeUser()) && ObjectUtil.isNotEmpty(orderBigVo.getFeeUser().getCouponId())) {
            // 获取优惠券是否参与分佣配置
            String couponOrderShare = paramApi.getCouponOrderShare(order.getTenantId());
            if ("1".equals(couponOrderShare)) {
                return;
            }
        }

        // TODO order分库修改（金额不查询订单表）
        BigDecimal profit = orderBigVo.getFeeTenant().getDueAmount().subtract(orderBigVo.getFeeTenant().getCost());
        producer.sendCalculationKickback(new MQOrderKickbackVo().setOrderId(order.getId()).setUserId(orderBigVo.getUserId()).setOrderAmount(payAmount).setProfit(profit).setType("1").setTenantId(TenantContextHolder.getTenantId()));
    }


    public String filterMessage(String message) {
        if (message == null) {
            return null;
        }
        // 正则表达式匹配 "有赞", "有赞寄件", "有赞寄件-"
        String regex = "有赞寄件-?|有赞";
        // 使用正则替换为空字符串
        return message.replaceAll(regex, "");
    }

    /**
     * 去第三方下单
     *
     * @param order
     * @param orderBigVo
     */
    public void toThirdPartyOrder(OrderDO order, OrderBigVo orderBigVo, String payType, Long bizId) {
        if (Objects.equals(order.getSourceType(), ThirdParamsSourceTypeEnum.HEADQUARTERS.getValue())) {
            // 代理商账户扣款
            orderProcessService.orderTenantDeduct(orderBigVo.getOrderId(), Long.parseLong(orderBigVo.getOrderId()), getTenantPayAmount(orderBigVo), TenantAmountConstants.TENANT_AMOUNT_MODE_TWO, String.format("订单下单扣费-%s", orderBigVo.getOrderId()));
        }

        // 查询当前订单的
        ThirdParamsDO thirdParamsDO = thirdParamsService.getParams(Long.valueOf(order.getChannel()), order.getTenantId());


        // 构建第三方下单参数
        CreateOrderVO createOrderVO = buildThirdPartyOrderParams(orderBigVo);

        // 获取支付金额
        BigDecimal payAmount = getPayAmount(orderBigVo);

        // 记录订单支付情况
        savePayType(order.getId(), payType, payAmount, bizId);

        // 构建订单详情修改对象
        OrderParcelUpdateReqVO updateParcelDO = new OrderParcelUpdateReqVO();
        updateParcelDO.setId(orderBigVo.getOrderParcel().getId());
        updateParcelDO.setThirdVersion(thirdParamsDO.getVersion());

        // 调用第三方接口下单
        callThirdOrderApi(createOrderVO, order, updateParcelDO);

        String orderPayType = payType;
        // 判断是否优惠券支付
        if (ObjectUtil.isNotEmpty(orderBigVo.getFeeUser()) && ObjectUtil.isNotEmpty(orderBigVo.getFeeUser().getCouponId()) && payType.equals(OrderConstants.PAY_TYPE_WECHAT)) {
            orderPayType = OrderConstants.ORDER_PAY_TYPE_FOUR;
        }

        // 修改订单信息
        order.setPayType(orderPayType);
        syncUpdatedOrders(order);

        // 如果下单异常，屏蔽关键字
        if (OrderConstants.ORDER_STATUS_WAITING_ABNORMAL.equals(order.getStatus()) && ObjectUtil.isNotEmpty(updateParcelDO.getMessage())) {
            // 屏蔽关键字
            updateParcelDO.setMessage(filterMessage(updateParcelDO.getMessage()));
        }
        if (ObjectUtil.isNotEmpty(order.getUserId()) && OrderConstants.ORDER_STATUS_WAITING_PARTS.equals(order.getStatus())) {
            try {
                // 发生下单成功通知
                String expressCompany = getKdChannelName(order);
                NotifyMsgVo orderSuccess = new NotifyMsgVo().createOrderSuccess(order.getId(), order.getOrderNo(), order.getTenantId(), order.getUserId(), expressCompany, payAmount);
                notifyParamService.sendNotify(NoticeGroupCodeEnum.ORDER_SUCCESS.getCode(), orderSuccess);
            } catch (Exception e) {
                log.error("发送下单成功通知失败：{}", e.getMessage());
            }
        }

        // 修改订单详情
        orderParcelService.updateOrderParcel(updateParcelDO);

        // 修改用户费用表信息
        FeeUserUpdateReqVO updateReqVO = new FeeUserUpdateReqVO();
        updateReqVO.setPayAmount(payAmount).setOrderId(Long.valueOf(order.getId()));
        feeUserService.updateFeeUser(updateReqVO);

        // 保存总部费用至ES
        orderBigVo.getOrderInfo().setOrderNo(order.getOrderNo());
//        saveFeeSuperEs(orderBigVo);

        try {
            // 获取租户信息
            TenantRespVO tenantRespVO = tenantApi.getTenantInfo(order.getTenantId()).getData();

            // 判断当前用户是否存在上级
            MemberUserRespDTO user = memberUserApi.getUser(order.getUserId());
            if (ObjectUtil.isEmpty(user) || ObjectUtil.isEmpty(user.getPromoteId())) {
                return;
            }
            // 获取上级用户信息
            MemberUserRespDTO promoteUser = memberUserApi.getUser(user.getPromoteId());
//            if (ObjectUtil.isEmpty(promoteUser) || !Objects.equals(promoteUser.getLoginMode(), MemberConstants.DISTRIBUTOR_USER)) {
//                // 用户的上级不是分销用户，是新用户或受邀用户，不进行分佣
//                return;
//            }

            // 获取营销类型
            String marketingType = tenantRespVO.getMktType();

            // 判断配置的营销类型，进行不同的营销活动
            if (MemberConstants.MBR_INVITE_MARKETING_TYPE_ONE.equals(marketingType)) {
                return;
            }
            // 模式五下，需要校验
            if (MemberConstants.MODE_FIVE.equals(marketingType)) {
                // 用户保存的上级用户类型 与 上级的用户类型不一致 不进行分佣
                if (!Objects.equals(user.getPromoteType(), promoteUser.getLoginMode())) {
                    return;
                }

                // 用户保存的上级用户类型 与 上级的用户类型一致,但是不是分销用户，不进行分佣
                if (Objects.equals(user.getPromoteType(), promoteUser.getLoginMode()) && !Objects.equals(user.getPromoteType(), MemberConstants.DISTRIBUTOR_USER)) {
                    return;
                }
            }

            // 异步计算分佣
            calculateCommissionAsync(order, orderBigVo, payAmount);

        } catch (Exception e) {
            log.error("下单异步计算分佣失败，订单ID：{}，错误信息：{}", order.getId(), e.getMessage());
        } finally {
            try {
                // 发送订单同步到PostgreSQL消息
                orderMqProducer.sendOrderSyncPgsql(order.getId(), "update");
            } catch (Exception e) {
                log.error("订单支付发送订单同步到PostgreSQL消息失败，订单ID：{}，错误信息：{}", order.getId(), e.getMessage());
            }
        }

        try {
            // WebSocket推送订单状态
            String statusLabel = DictFrameworkUtils.getDictDataLabel(DictTypeConstants.KUAIDI_STATUS, order.getStatus());
            String topic = "msg/" + order.getTenantId() + "/" + order.getUserId();
            PushMqttVO pushMqttVO = new PushMqttVO();
            BeanUtil.copyProperties(order, pushMqttVO);
            pushMqttVO.setStatusStr(statusLabel);
            mqttSendClient.publish(false, topic, JsonUtils.toJsonString(pushMqttVO));
        } catch (Exception e) {
            log.error("WebSocket推送订单状态失败：{ }", e);
//            throw new ServiceException(new ErrorCode(400,"下单失败，请联系在线客服"));
        }

        try {
            if (!OrderConstants.ORDER_STATUS_WAITING_ABNORMAL.equals(order.getStatus())) {
                // TODO  发送消息  【下单异常通知】


                // 订单信息上报mq
//                producer.sendOrderReported(order, OrderConstants.ORDER_REPORTED_ADD);
            } else {
                // 如果使用了优惠券，优惠券退回
                if (ObjectUtil.isNotEmpty(orderBigVo.getFeeUser()) && ObjectUtil.isNotEmpty(orderBigVo.getFeeUser().getCouponId())) {
                    couponService.couponRefund(orderBigVo.getFeeUser().getCouponId());
                }

                // 下单失败，统计渠道失败次数，达到条件推送企业微信通知
                try {
                    channelFailNotify(order, createOrderVO);
                } catch (Exception e) {
                    log.error("发送渠道下单失败通知异常：{}", e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("订单上报COREDATA异常：{}", e.getMessage());
        }
    }

    /**
     * 渠道下单失败通知
     *
     * @param order
     * @param createOrderVO
     * @throws JsonProcessingException
     */
    private void channelFailNotify(OrderDO order, CreateOrderVO createOrderVO) throws Exception {
        /**
         * 需求：企业微信通知：当某个渠道，xx分钟内下单异常超过xx个，推送到企业微信通知
         * 主要实现逻辑
         * 1.先去system_wecom_config查询到关于“渠道下单失败通知”的这条数据
         * 2.拿到params参数，获取到配置的 xx分钟  和  xx个  参数
         * 3.封装推送类型，调用企业消息发送MQ发送消息
         */
        CommonResult<WecomConfigRespVO> wecomConfig = wecomConfigService.getWecomConfig(WeChatGroupLevelEnum.BUSINESS_LEVEL.getCode(), WeChatGroupMessageTypeEnum.ORDER_FAILED_NOTIFY.getValue());
        if (ObjectUtil.isNotEmpty(wecomConfig.getData())) {
            WecomConfigRespVO configData = wecomConfig.getData();
            String params = configData.getParams();
            if (!StringUtils.isEmpty(params)) {
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonNode = jsonNode = objectMapper.readTree(params);

                int time = jsonNode.get("time").asInt();
                int num = jsonNode.get("num").asInt();
                if (time > 0 && num > 0) {

                    Long tenantId = order.getTenantId();
                    String key = "channel::create-order-fail::" + tenantId + "-" + createOrderVO.getDeliveryChannelId();

                    Boolean flag = false;
                    if (stringRedisTemplate.hasKey(key)) {
                        // 使用 Redis INCRBY 命令自增值
                        Long count = stringRedisTemplate.opsForValue().increment(key, 1); // 自增 1
                        if (count > num) {
                            flag = true;
                        }
                    } else {
                        // 设置初始值
                        stringRedisTemplate.opsForValue().setIfAbsent(key, "1", time, TimeUnit.MINUTES);
                    }

                    if (flag) {
                        CommonResult<TenantRespVO> tenantResult = tenantApi.getTenantInfo(tenantId);
                        TenantRespVO tenantRespVO = tenantResult.getData();

                        // 下单的渠道
                        KdChannelDO kdChannelDO = kdChannelService.getChannel(createOrderVO.getChannelExpressId());

                        String pushContent = String.format("渠道下单预警！！！[%s]渠道在[%s]分钟内下单失败次数超过[%s]次，请重点关注。", kdChannelDO != null ? kdChannelDO.getChannelName() : createOrderVO.getChannelExpressId(), time, num);
                        WeChatGroupNotifyReq weChatGroupNotifyReq = new WeChatGroupNotifyReq();
                        weChatGroupNotifyReq.setAppName(tenantRespVO.getName());
                        weChatGroupNotifyReq.setPushType(WeChatGroupMessageTypeEnum.ORDER_FAILED_NOTIFY.getCode());
                        weChatGroupNotifyReq.setExecuteMethod(WeChatGroupMessageTypeEnum.ORDER_FAILED_NOTIFY.getCode());
                        weChatGroupNotifyReq.setPushContent(pushContent);
                        weChatGroupNotifyReq.setTenantId(tenantId);
                        orderMqProducer.orderOverweightNotifyOutput(weChatGroupNotifyReq);
                    }
                }
            }
        }
    }

    public BigDecimal getTenantPayAmount(OrderBigVo orderBigVo) {
        BigDecimal payAmount = BigDecimal.ZERO;

        if (ObjectUtil.isNotEmpty(orderBigVo.getFeeTenant()) && ObjectUtil.isNotEmpty(orderBigVo.getFeeTenant().getCost())) {
            payAmount = orderBigVo.getFeeTenant().getCost();
            return payAmount;
        } else {
            throw new ServiceException(new ErrorCode(31501, "获取代理商金额失败，请联系管理员处理。"));
        }
//        payAmount = orderBigVo.getOrderInfo().getTenantPayAmount();
//        return payAmount;
    }

    public BigDecimal getPayAmount(OrderBigVo orderBigVo) {
        BigDecimal payAmount = BigDecimal.ZERO;

        if (ObjectUtil.isNotEmpty(orderBigVo.getFeeUser()) && ObjectUtil.isNotEmpty(orderBigVo.getFeeUser().getLastPayAmount())) {
            payAmount = orderBigVo.getFeeUser().getLastPayAmount();
            return payAmount;
        } else {
            throw new ServiceException(new ErrorCode(31501, "获取用户扣款金额失败，请联系管理员处理。"));
        }
//        payAmount = orderBigVo.getOrderInfo().getLastPrice();
//        return payAmount;
    }

    @Override
    public OrderDO createBackgroundOrder(CreateOrderVO createOrderVO) {

        buildCreateOrderParams(createOrderVO);
        //数据校验
        validateBackgroundCreate(createOrderVO);
        MiniCreateOrderReqVO miniCreateOrderReqVO = new MiniCreateOrderReqVO();

        BeanUtil.copyProperties(createOrderVO, miniCreateOrderReqVO);

        OrderBigVo bigVo = assembleBigVo(miniCreateOrderReqVO);

        // 费用计算
        calculateTotalFees(miniCreateOrderReqVO.getFeeTagId(), bigVo);
        // 保存数据
        saveOrder(bigVo);

        return bigVo.getOrderInfo();
    }

    /**
     * 创建批量订单
     *
     * @param createOrderVO
     * @param dataSource    来源：2：后台，1：小程序
     * @return
     */
    @Override
    public OrderDO batchCreateOrder(CreateOrderVO createOrderVO, String dataSource) {
        buildCreateOrderParams(createOrderVO);
        //数据校验
        validateBackgroundCreate(createOrderVO);
        MiniCreateOrderReqVO miniCreateOrderReqVO = new MiniCreateOrderReqVO();

        BeanUtil.copyProperties(createOrderVO, miniCreateOrderReqVO);

        OrderBigVo bigVo = assembleBackgroundBigVo(miniCreateOrderReqVO);
        if (OrderConstants.ORDER_TYPE_SYS.equals(dataSource)) {
            bigVo.setUserId(null);
        }

        // 费用计算
        calculateTotalFees(miniCreateOrderReqVO.getFeeTagId(), bigVo);
        // 保存数据
        saveOrder(bigVo);

        return bigVo.getOrderInfo();
    }


    private void syncUpdatedOrders(OrderDO order) {
        OrderDO queryOrder = orderService.getOrder(order.getId());
        if (queryOrder.getRefundFlag().equals(OrderConstants.ORDER_REFUND_FLAG_RETURNED)) {
            order.setRefundFlag(OrderConstants.ORDER_REFUND_FLAG_RETURNED);
        }

        OrderUpdateReqVO reqVO = new OrderUpdateReqVO();
        BeanUtil.copyProperties(order, reqVO);
        orderService.updateOrder(reqVO);
    }

    public CreateOrderVO buildThirdPartyOrderParams(OrderBigVo bigVo) {
        CreateOrderVO createOrderVO = new CreateOrderVO();

//        构建订单基本信息
        copyOrderInfo(bigVo, createOrderVO);

//        构建订单物品信息
        copyOrderPriceInfo(bigVo, createOrderVO);

        // 构建发件人信息
        copySenderInfo(bigVo, createOrderVO);

        // 构建收件人信息
        copyReceiveInfo(bigVo, createOrderVO);

        // 构建回调地址
        buildCallback(bigVo, createOrderVO);


        createOrderVO.setOrderId(bigVo.getOrderId());

        return createOrderVO;
    }

    public void buildCallback(OrderBigVo bigVo, CreateOrderVO createOrderVO) {
        // 构建快递100回调地址
        if (ObjectUtil.isNotEmpty(bigVo.getOrderInfo()) && OrderConstants.ORDER_CHANNEL_KUAIDI100.equals(bigVo.getOrderInfo().getChannel())) {
            String channel = bigVo.getOrderInfo().getChannel();
            Long tenantId = getChannelTenantId(bigVo.getOrderInfo().getTenantId());
            // 快递回调地址
            // 快递回调地址，根据tenantId和 thirdId 查询新表 kd_third_params
            String callbackUrl = paramApi.getKuaidi100CallbackUrl(tenantId);
            ThirdParamsDO thirdParamsDO = thirdParamsService.getParams(OrderConstants.ORDER_CHANNEL_KUAIDI100_L, tenantId);
            if (ObjectUtil.isNotEmpty(thirdParamsDO) && ObjectUtil.isNotEmpty(thirdParamsDO.getCallbackUrl())) {
                callbackUrl = thirdParamsDO.getCallbackUrl();
            }

            // 轨迹回调地址
            String locusCallbackUrl = paramApi.getkuaidi100LocusCallbackUrl(tenantId);
            if (ObjectUtil.isNotEmpty(thirdParamsDO) && ObjectUtil.isNotEmpty(thirdParamsDO.getTrackCallbackUrl())) {
                locusCallbackUrl = thirdParamsDO.getTrackCallbackUrl();
            }

            if (ObjectUtil.isEmpty(callbackUrl)) {
                throw new ServiceException(new ErrorCode(3000015, "未配置回调地址,请联系管理处理"));
            }
            callbackUrl = callbackUrl + "/" + tenantId;
            locusCallbackUrl = locusCallbackUrl + "/" + tenantId;
            createOrderVO.setCallbackUrl(callbackUrl);
            createOrderVO.setLocusCallbackUrl(locusCallbackUrl);
        }
    }


    public Long getChannelTenantId(Long tenantId) {
        // 查询当前分销商是否取总部渠道
        TenantRespVO tenantRespVO = tenantApi.getTenantInfo(tenantId).getData();
        if (ObjectUtil.isEmpty(tenantRespVO) || ObjectUtil.isEmpty(tenantRespVO.getChannelObtain())) {
            return tenantId;
        }
        // 判断是取总部还是取后台配置
        return tenantRespVO.getChannelObtain().equals(TenantConstants.TENANT_CHANNEL_TYPE_ROOT) ? TenantConstants.ROOT_TENANT_ID : tenantId;
    }

    @Resource
    private ThirdService thirdService;

    /**
     * 调用第三方接口下单
     */
    private void callThirdOrderApi(CreateOrderVO createOrderVO, OrderDO orderDO, OrderParcelUpdateReqVO updateParcelDO) {
        try {
            // 有赞地址ID传递
            youzanAddressId(createOrderVO);

            CreateOrderResVo result = TenantBeanUtils.getKuaidiRequestBean(createOrderVO.getTenantId(), createOrderVO.getChannel()).createOrder(createOrderVO, envProperties.isTestApi());
            orderDO.setOrderNo(result.getKuaidinum());
            orderDO.setTaskId(result.getTaskId());
            orderDO.setThirdNo(result.getOrderId());
            orderDO.setStatus(OrderConstants.ORDER_STATUS_WAITING_PARTS);
            orderDO.setThirdStatus(OrderConstants.THIRD_STATUS_CALLED);
            // TODO order分库注释 （此字段去除）
//            orderDO.setPayAmount(new BigDecimal(createOrderVO.getLastPrice()));
            updateParcelDO.setPaymentTime(LocalDateTime.now());
            // 快递名称
            updateParcelDO.setThirdExpressType(result.getExpressCode());
            updateParcelDO.setThirdExpressName(result.getExpressName());
            // TODO 发送自检测消息、完成价格比对
            producer.sendRiskMessage(new MQOrderRiskVo().setOrderId(orderDO.getId()).setTenantId(orderDO.getTenantId()));
        } catch (Exception ex) {
            log.error("创建订单失败 {}", ex.getMessage());
            orderDO.setStatus(OrderConstants.ORDER_STATUS_WAITING_ABNORMAL);
            orderDO.setThirdStatus(OrderConstants.THIRD_STATUS_FAILED);
            updateParcelDO.setMessage(ex.getMessage());

            // 创建 key
            String key = "order::thirdCreate::error::" + orderDO.getUserId() + "::" + orderDO.getTenantId();

            // 存储数据到 Redis
            redisTemplate.opsForHash().put(key, "userId", orderDO.getUserId().toString());
            redisTemplate.opsForHash().put(key, "sendAddress", createOrderVO.getSenderProvince() + createOrderVO.getSenderCity() + createOrderVO.getSenderDistrict() + createOrderVO.getSenderAddress());
            redisTemplate.opsForHash().put(key, "receiveAddress", createOrderVO.getReceiveProvince() + createOrderVO.getReceiveCity() + createOrderVO.getReceiveDistrict() + createOrderVO.getReceiveAddress());
            // 处理 expressProductId，避免覆盖
            String existingExpressProductId = (String) redisTemplate.opsForHash().get(key, "expressProductId");
            if (existingExpressProductId == null) {
                // 如果不存在，则直接存储
                redisTemplate.opsForHash().put(key, "expressProductId", orderDO.getExpressProductId().toString());
            } else {
                // 如果已存在，则添加新的 expressProductId（以逗号分隔）
                String newExpressProductId = existingExpressProductId + "," + orderDO.getExpressProductId().toString();
                redisTemplate.opsForHash().put(key, "expressProductId", newExpressProductId);
            }

            // 设置过期时间为 5 分钟（300 秒）
            redisTemplate.expire(key, 5, TimeUnit.MINUTES);

            if (orderDO.getChannel().equals(OrderConstants.ORDER_CHANNEL_DEPPON)) {
                // 德邦无需退款
                orderDO.setRefundFlag(OrderConstants.ORDER_REFUND_FLAG_NO_REFUND);
            } else {
                orderDO.setRefundFlag(OrderConstants.ORDER_REFUND_FLAG_NOT_RETIRED);
                // TODO 发送异步消息 退款
                producer.sendOrderRefundMessage(new MQOrderRefundVo().setOrderId(orderDO.getId()).setTenantId(orderDO.getTenantId()));
            }

            if (ObjectUtil.isNotEmpty(orderDO.getUserId())) {
                try {
                    String expressCompany = getKdChannelName(orderDO);
                    NotifyMsgVo notifyMsgVo = new NotifyMsgVo().createOrderException(orderDO.getId(), orderDO.getOrderNo(), orderDO.getTenantId(), orderDO.getUserId(), expressCompany, ex.getMessage());
                    notifyParamService.sendNotify(NoticeGroupCodeEnum.ORDER_EXCEPTION.getCode(), notifyMsgVo);
                } catch (Exception e) {
                    log.error("发送下单异常通知失败，{}", e.getMessage());
                }
            }

        } finally {
            // 调用完成后把有赞地址锁删掉
            if (createOrderVO.getChannel().equals(OrderConstants.ORDER_CHANNEL_YOUZAN)) {
                String key = "YOUZAN::ADDRESSID" + "-" + createOrderVO.getYouzanAddressId();
                stringRedisTemplate.delete(key);
            }
        }
    }

    private String getKdChannelName(OrderDO orderDO) {
        if (ObjectUtil.isEmpty(orderDO)) {
            return null;
        }

        Long channelExpressId = orderDO.getChannelExpressId();
        if (ObjectUtil.isNotEmpty(channelExpressId)) {
            KdChannelExportReqVO kdChannelExportReqVO = kdChannelService.getChannelAndExpressName(channelExpressId);
            if (ObjectUtil.isNotEmpty(kdChannelExportReqVO)) {
                return kdChannelExportReqVO.getExpressName();
            }
        }

        return ObjectUtil.isNotEmpty(orderDO.getDeliveryType()) ? orderDO.getDeliveryType() : null;
    }

    /**
     * 有赞下单获取地址ID
     *
     * @param createOrderVO
     */
    private void youzanAddressId(CreateOrderVO createOrderVO) {
        if (createOrderVO.getChannel().equals(OrderConstants.ORDER_CHANNEL_YOUZAN)) {
            ThirdDO third = thirdService.getThird(OrderConstants.ORDER_CHANNEL_YOUZAN_L);
            if (third == null || StringUtils.isEmpty(third.getAddressId())) {
                throw new ServiceException(7741594, "有赞未正确配置地址ID");
            }

            List<String> addressIds = Arrays.asList(third.getAddressId().split(","));
            boolean addressFound = false;
            // 遍历addressIds列表并尝试设置地址Id
            for (String addressId : addressIds) {
                if (stringRedisTemplate.opsForValue().setIfAbsent("YOUZAN::ADDRESSID" + "-" + addressId, addressId, 2, TimeUnit.SECONDS)) {
                    createOrderVO.setYouzanAddressId(Long.valueOf(addressId));
                    addressFound = true;
                    break;  // 一旦找到地址Id就退出循环
                }
            }

            if (!addressFound) {
                throw new ServiceException(7741594, "有赞缺少可使用的地址ID");
            }
        }
    }

    /**
     * 微信支付逻辑
     *
     * @param params
     */
    private Map getWechatPayParams(MiniOrderPayReqVO params, OrderBigVo orderBigVo) {

        BigDecimal lastPrice = orderBigVo.getFeeUser().getLastPayAmount();

//         获取到用户订阅消息模板
        Map coreMap = new HashMap();
        try {
            //        1. 获取消息订阅模板
            coreMap.put("tempIds", orderService.getOrderTmplIds());
//        2. 校验微信渠道支付
            Assert.isTrue(paramApi.isOpenWechatPay(TenantContextHolder.getTenantId()), "微信支付渠道已关闭");
//        3. 校验系统支付参数
            List<PayAppDO> appList = payAppService.getAppList();
            Assert.isTrue(CollUtil.isNotEmpty(appList), "系统支付参数不正确");


//        4. 组装信息调用微信支付
            Long payment = buildWeChatPayment(params, orderBigVo.getOrderInfo(), coreMap, appList, lastPrice);
//        5. 创建微信支付日志
            createWechatPayLog(payment, orderBigVo.getOrderInfo(), lastPrice);
        } catch (ServiceException e) {
            if (e.getCode() == 401111) {
                throw e;
            }
            // 支付失败，发送企微短信
            weChatPayError(orderBigVo.getOrderInfo(), lastPrice, e);
        } catch (Exception e) {
            // 支付失败，发送企微短信
            weChatPayError(orderBigVo.getOrderInfo(), lastPrice, e);
        }
        return coreMap;

    }

    private void weChatPayError(OrderDO order, BigDecimal lastPrice, Exception e) {
        String errorMsg =  e.getMessage();
        try {
            // 获取详细的堆栈信息
            errorMsg = getDetailedExceptionInfo(e);
        } catch (Exception ex) {
            log.error("获取错误堆栈信息失败：", ex.getMessage());
        }

        WxMiniMsgVo miniMsgVo = new WxMiniMsgVo();
        miniMsgVo.setTenantId(TenantContextHolder.getTenantId());
        miniMsgVo.setBizId(order.getId());
        miniMsgVo.setOrderNo(order.getOrderNo());
        miniMsgVo.setAmount(lastPrice);
        miniMsgVo.setExceptionMessage(errorMsg);
        miniMsgVo.setMsgType(WxMsgTypeConstants.ERROR);
        miniMsgVo.setBizType(WxMsgTypeConstants.PAY_BIZ_TYPE);
        miniMsgVo.setUserId(order.getUserId());
        wxMqProducer.sendMiniMessage(miniMsgVo);
    }

    /**
     * 获取异常的详细信息，包括行号
     *
     * @param e 异常
     * @return 格式化的异常信息
     */
    private String getDetailedExceptionInfo(Exception e) {
        StringBuilder stackTraceStr = new StringBuilder();
        stackTraceStr.append("错误类型: ").append(e.getClass().getName()).append("\n");
        stackTraceStr.append("错误信息: ").append(e.getMessage()).append("\n");
        stackTraceStr.append("堆栈信息: \n");

        // 获取堆栈跟踪
        StackTraceElement[] stackTrace = e.getStackTrace();
        // 只取前10个堆栈元素，避免信息过长
        int maxElements = Math.min(10, stackTrace.length);
        for (int i = 0; i < maxElements; i++) {
            StackTraceElement element = stackTrace[i];
            // 如果是项目内的类，更详细展示
            if (element.getClassName().contains("com.hnyiti")) {
                stackTraceStr.append("    at ").append(element.getClassName())
                        .append(".").append(element.getMethodName())
                        .append("(").append(element.getFileName())
                        .append(":").append(element.getLineNumber())
                        .append(") [项目内部代码]\n");
            } else {
                stackTraceStr.append("    at ").append(element.getClassName())
                        .append(".").append(element.getMethodName())
                        .append("(").append(element.getFileName())
                        .append(":").append(element.getLineNumber())
                        .append(")\n");
            }
        }

        if (stackTrace.length > maxElements) {
            stackTraceStr.append("    ... ").append(stackTrace.length - maxElements).append(" more\n");
        }

        // 设置错误信息，但不超过最大长度限制
        int maxLength = 2000;
        String result = stackTraceStr.toString();
        if (result.length() > maxLength) {
            result = result.substring(0, maxLength) + "..."; // 超出部分截断并加省略号
        }

        return result;
    }

    /**
     * 补差支付逻辑
     *
     * @param params
     * @param order
     */
    private Map getWechatOverweightParams(MiniOrderPayReqVO params, OrderDO order) {
//         获取到用户订阅消息模板
        Map coreMap = new HashMap();
//        1. 获取消息订阅模板
        coreMap.put("tempIds", orderService.getOrderTmplIds());
//        2. 校验微信渠道支付
        Assert.isTrue(paramApi.isOpenWechatPay(TenantContextHolder.getTenantId()), "微信支付渠道已关闭");
//        3. 校验系统支付参数
        List<PayAppDO> appList = payAppService.getAppList();
        Assert.isTrue(CollUtil.isNotEmpty(appList), "系统支付参数不正确");
//        4. 组装信息调用微信支付
        Long payment = buildWeChatOverweightPayment(params, order, coreMap, appList);
//        5. 创建微信支付日志
        createWechatOverweightPayLog(payment, order);
        return coreMap;

    }


    /**
     * 用户余额账户扣款
     *
     * @param bigVo
     */
    private void userDeduct(OrderBigVo bigVo) {
        // 先查询出用户的余额
        Long userId = bigVo.getUserId();
        MemberUserRespDTO user = memberUserApi.getUser(userId);
        CommonResult commonResult = tenantApi.deductUserAmount(userId, bigVo.getOrderId(), null, bigVo.getFeeUser().getLastPayAmount().negate(), FeeConstants.PAY_TYPE_ORDER, String.format("普通订单扣款-%s", bigVo.getOrderId()));
        if (!commonResult.isSuccess()) {
            throw new ServiceException(commonResult.getCode(), commonResult.getMsg());
        }

        // 发送账户余额扣除成功通知
        try {
            BalanceRespVO data = balanceApi.getBalanceByUserId(userId, BalanceConstants.WALLET_TYPE_BALANCE).getData();
            BigDecimal balance = ObjectUtil.isNotEmpty(data) ? data.getBalance() : BigDecimal.ZERO;
            BigDecimal payAmount = bigVo.getFeeUser().getLastPayAmount().negate();
            NotifyMsgVo accountBalanceDeducted = new NotifyMsgVo().createAccountBalanceDeducted(TenantContextHolder.getTenantId(), userId, payAmount, balance, balance.add(payAmount), LocalDateTime.now());
            notifyParamService.sendNotify(NoticeGroupCodeEnum.ACCOUNT_BALANCE_DEDUCTED.getCode(), accountBalanceDeducted);
        } catch (Exception e) {
            log.error("发送账户余额扣除成功通知失败：{}", e.getMessage());
        }
    }

    /**
     * 用户余额账户扣款
     *
     * @param userId
     * @param orderId
     * @param payAmount
     * @param type
     * @param remark
     */
    private void userDeduct(Long userId, String orderId, BigDecimal payAmount, String type, String remark) {
        CommonResult commonResult = tenantApi.deductUserAmount(userId, orderId, null, payAmount, type, remark);
        if (!commonResult.isSuccess()) {
            throw new ServiceException(commonResult.getCode(), commonResult.getMsg());
        }
    }

    private void agentDeduct(OrderBigVo bigVo) {
//        CommonResult commonResult = orderParcelService.orderTenantDeduct(bigVo.getOrderId(),
//                bigVo.getFeeTenant().getCost().negate(), FeeConstants.PAY_TYPE_ORDER,
//                bigVo.getUserId(), String.format("订单下单扣费-%s", bigVo.getOrderId()));
        CommonResult commonResult = tenantApi.deductTenantAmount(bigVo.getOrderId(), bigVo.getFeeTenant().getCost().negate(), FeeConstants.PAY_TYPE_ORDER, bigVo.getUserId(), String.format("订单下单扣费-%s", bigVo.getOrderId()));
        if (!commonResult.isSuccess()) {
            throw new ServiceException(commonResult.getCode(), commonResult.getMsg());
        }
        // 支付记录


        // 删除缓存中的redis差价数据
        /*String priceRedisKey = "checkPrice";
        priceRedisKey = priceRedisKey + "::" + bigVo.getUserId()+"::" + feeTagId;
        redisUtils.delete(priceRedisKey);*/
    }

    @Override
    @GlobalTransactional
    @DSTransactional
    public Map submitOverweightOrder(MiniOrderPayReqVO params) {
        String orderId = params.getOrderId();
        OrderDO order = orderService.getOrder(orderId);

        // 参数校验
        validateCompensation(params, order);

        // 构建扣费对象、构建订单对象
        OrderBigVo orderBigVo = orderService.assembleBigVo(orderId);

        // 获取补差金额
        BigDecimal overweightAmount = getMakeupAmount(orderBigVo);

        // 支付渠道
        String payType = params.getPayType();
        if (OrderConstants.PAY_TYPE_BALANCE.equals(payType)) {
            OrderDO orderDO = orderBigVo.getOrderInfo();

            // 用户余额支付扣款
            userDeduct(orderDO.getUserId(), orderDO.getId(), overweightAmount.negate(), FeeConstants.PAY_TYPE_MOREWEIGHT, String.format("超重补差扣款-%s", orderId));

            // 补差完成
            compensationFinish(orderDO.getId(), overweightAmount.multiply(BigDecimal.valueOf(100)), params.getPayType(), null);
        } else if (OrderConstants.PAY_TYPE_WECHAT.equals(payType)) {
            // 用户微信支付返回支付参数
//            return getWechatPayParams(params, order);
            return getWechatOverweightParams(params, order);
        } else if (OrderConstants.PAY_TYPE_ALIPAY.equals(payType)) {
            Map<String, String> channelExtras = new HashMap<>();
            channelExtras.put("orderId", order.getId());
            // 查询补差金额
            channelExtras.put("type", "compensationV3");
            PayStrategy payHandler = PayStrategyFactory.createPayHandler(PayLogConstants.PAY_LOG_PAY_TYPE_ALIPAY);
            return payHandler.createPayOrder(new PayRequest().setDisplayMode(params.getDisplayMode()).setPaymentChannel(params.getPaymentChannel()).setCode(params.getCode()).setOrderNo(order.getOrderNo()).setBody(order.getDeliveryType()).setSubject("超重补差").setChannelExtras(channelExtras).setAmount(getCompensationAmountV2(order)).setOrderId(order.getId()).setBizId(order.getId()).setBizType(FeeConstants.WECHAT_PAY_BIZ_MOREWEIGHT));
        }

        // 异步风控

        return null;
    }


    /**
     * 完成超重补差
     *
     * @param orderId
     * @param amount
     */
//    @Override
    public OrderDO compensationFinish(String orderId, BigDecimal amount, String payType, Long bizId) {
        // 构建补差大对象
        OrderBigVo orderBigVo = orderService.compensationBigVo(orderId);
        OrderDO order = orderBigVo.getOrderInfo();

        // 验证数据
        Assert.isTrue(ObjectUtil.isNotEmpty(order), "订单不存在");

        // 判断是否已补差完成
        String compensatingResult = order.getCompensatingResult();
        Assert.isTrue(OrderConstants.ORDER_COMPENSATING_RESULT_TWO.equals(compensatingResult), "当前订单已补差完成");

        // 处理逻辑
        handleCompensationDate(orderBigVo, amount, payType, bizId);

        try {
            // 发送订单同步到PostgreSQL消息
            orderMqProducer.sendOrderSyncPgsql(orderId, "update");
        } catch (Exception e) {
            log.error("后台订单取消发送订单同步到PostgreSQL消息失败，订单ID：{}，错误信息：{}", orderId, e.getMessage());
        }
        return order;
    }


    public void compensationUpdate(OrderBigVo orderBigVo, BigDecimal amount) {
        // 构建订单修改对象
        OrderUpdateReqVO updateReqVO = buildCompensationOrder(orderBigVo, amount);
        // 构建用户费用明细修改对象
        FeeUserDO updateFeeUser = buildCompensationFeeUser(orderBigVo.getFeeUser(), amount);
        // 构建分销商费用明细修改对象
        FeeTenantDO updateFeeTenant = buildCompensationFeeTenant(orderBigVo, updateFeeUser.getPayAmount());

        // 修改数据
        if (ObjectUtil.isNotEmpty(updateReqVO)) {
            orderService.updateOrder(updateReqVO);
        }
        if (ObjectUtil.isNotEmpty(updateFeeUser)) {
            feeUserService.updateById(updateFeeUser);
        }
        if (ObjectUtil.isNotEmpty(updateFeeTenant)) {
            feeTenantService.updateById(updateFeeTenant);
        }

    }

    /**
     * 处理短信
     *
     * @param orderId
     */
    private void handleSysMessage(String orderId) {
        try {
            tenantApi.updateSmsLogSendStatusByOrderId(orderId);
        } catch (Exception e) {
            log.error("处理订单：{}短信失败", orderId);
        }
    }

    /**
     * 处理补差分佣逻辑
     *
     * @param order
     */
    public void handleCompensationBrokerage(OrderDO order) {
        // 查询当前代理商活动类型
        TenantRespVO tenantRespVO = tenantApi.getTenantInfo(order.getTenantId()).getData();

        // 判断当前用户是否存在上级
        MemberUserRespDTO user = memberUserApi.getUser(order.getUserId());
        if (ObjectUtil.isEmpty(user) || ObjectUtil.isEmpty(user.getPromoteId())) {
            return;
        }
        // 获取上级用户信息
        MemberUserRespDTO promoteUser = memberUserApi.getUser(user.getPromoteId());
        if (ObjectUtil.isEmpty(promoteUser) || !Objects.equals(promoteUser.getLoginMode(), MemberConstants.DISTRIBUTOR_USER)) {
            // 用户的上级是新用户，不进行分佣
            return;
        }

        // 1. 判断租户分佣模式
        if (ObjectUtil.isEmpty(tenantRespVO.getMktType()) || Objects.equals(MemberConstants.MODE_THREE, tenantRespVO.getMktType())) {
            // 空 null 模式3 不分佣
            return;
        }

        // 2. 判断租户营销模式是否为 推新分佣
        if (MemberConstants.MBR_INVITE_MARKETING_TYPE_REBATE.equals(tenantRespVO.getMktType()) || MemberConstants.MODE_FOUR.equals(tenantRespVO.getMktType())) {
            // 获取分佣记录
            List<BrokerageRespVO> brokerageList = brokerageApi.byOrderIdGetData(Long.valueOf(order.getId()));
            // 判断订单的补差状态
            if (CollUtil.isNotEmpty(brokerageList) && Objects.equals(OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO, order.getCompensatingState()) && Objects.equals(OrderConstants.ORDER_COMPENSATING_RESULT_TWO, order.getCompensatingResult())) {
                // 分佣记录不为空，但是超重未补 不分佣并且修改分佣记录为等待分佣
                List<BrokerageUpdateReqVO> updateReqVOs = new ArrayList<>();
                for (BrokerageRespVO brokerageRespVO : brokerageList) {
                    BrokerageUpdateReqVO updateReqVO = new BrokerageUpdateReqVO();
                    updateReqVO.setId(brokerageRespVO.getId());
                    if (brokerageRespVO.getStatus().equals("4")) {
                        updateReqVO.setStatus("2");
                    }
                    updateReqVOs.add(updateReqVO);
                }
                brokerageApi.updateBrokerageList(updateReqVOs);
            }
            // 判断是否已签收
            if (OrderConstants.ORDER_STATUS_SIGN_FOR.equals(order.getStatus())) {
                // 佣金到账 划转到余额
                brokerageApi.arrival(Long.valueOf(order.getId()));
            }
            return;
        }

        if (MemberConstants.MODE_FIVE.equals(tenantRespVO.getMktType())) {
            handleModeFiveCommission(order);
        }

        // 3. 判断租户营销模式是否为 推三返一
        if (MemberConstants.MBR_INVITE_MARKETING_TYPE_ONE.equals(tenantRespVO.getMktType())) {
            if (Objects.equals(OrderConstants.ORDER_STATUS_SIGN_FOR, order.getStatus()) && !Objects.equals(OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO, order.getCompensatingState()) && !Objects.equals(OrderConstants.ORDER_COMPENSATING_RESULT_TWO, order.getCompensatingResult())) {
                // 已签收、不是超重未补 分佣
                // 推三返一 佣金入账
                orderMqProducer.sendRebateReported(order);
                return;
            }
        }

        // 发送佣金到账消息
//        try {
//            NotifyMsgVo commissionReceived = new NotifyMsgVo().createCommissionReceived(null, null, null, null);
//            notifyParamService.sendNotify(NoticeGroupCodeEnum.SIGN_EXCEPTION.getCode(),commissionReceived);
//        }catch (Exception e) {
//            log.error("发送佣金到账消息失败：{}",e.getMessage());
//        }
    }

    /**
     * 处理模式五的分佣逻辑
     */
    private void handleModeFiveCommission(OrderDO orderDO) {
        MemberUserRespDTO user = memberUserApi.getUser(orderDO.getUserId());
        MemberUserRespDTO promoteUser = memberUserApi.getUser(user.getPromoteId());

        // 校验用户保存的上级标识是否与上级的用户登录方式一致
        if (!Objects.equals(user.getPromoteType(), promoteUser.getLoginMode())) {
            return;
        }

        if (Objects.equals(MemberConstants.DISTRIBUTOR_USER, user.getPromoteType())) {
            // 分销用户，进行分佣
            brokerageApi.arrival(Long.valueOf(orderDO.getId()));
        }
        // TODO 暂时注释
//        else {
//            // 其他用户，检查是否满足发放优惠券条件
//            grantCouponIfEligible(user, promoteUser, orderDO);
//        }
    }

    /**
     * 判断是否符合发放优惠券条件，并执行发放
     */
    private void grantCouponIfEligible(MemberUserRespDTO user, MemberUserRespDTO promoteUser, OrderDO orderDO) {
        // 仅当上级是至尊会员且当前用户为首单时，发放优惠券
        boolean isFirstOrder = orderService.isFirstOrder(user.getId(), orderDO.getId());
        if (promoteUser.getUserLevel() == 3 && isFirstOrder) {
            // TODO: 发放优惠券逻辑
        }
    }

    public FeeUserDO buildCompensationFeeUser(FeeUserDO feeUser, BigDecimal amount) {
        FeeUserDO updateDo = new FeeUserDO();
        if (ObjectUtil.isEmpty(feeUser)) {
            return null;
        }
        BigDecimal payAmount = ObjectUtil.isNotEmpty(feeUser.getPayAmount()) ? feeUser.getPayAmount() : BigDecimal.ZERO;
        payAmount = payAmount.add(amount.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        updateDo.setOrderId(feeUser.getOrderId());
        updateDo.setPayAmount(payAmount);

        return updateDo;
    }

    public FeeTenantDO buildCompensationFeeTenant(OrderBigVo orderBigVo, BigDecimal payAmount) {
        FeeTenantDO feeTenant = orderBigVo.getFeeTenant();
        FeeUserDO feeUser = orderBigVo.getFeeUser();

        if (ObjectUtil.isEmpty(feeTenant)) {
            return null;
        }
        if (ObjectUtil.isEmpty(payAmount)) {
            return null;
        }
        if (ObjectUtil.isEmpty(payAmount)) {
            return null;
        }

        feeTenant.setProfitAmount(getProfitAmount(payAmount, feeTenant.getCost(), ObjectUtil.isNotEmpty(feeUser) ? feeUser.getCouponCost() : null));

        return feeTenant;
    }

    /**
     * 构建补差订单修改对象
     *
     * @param orderBigVo
     * @param amount
     */
    public OrderUpdateReqVO buildCompensationOrder(OrderBigVo orderBigVo, BigDecimal amount) {
        OrderUpdateReqVO updateDo = new OrderUpdateReqVO();
        updateDo.setId(orderBigVo.getOrderInfo().getId());

        if (ObjectUtil.isNotEmpty(orderBigVo.getFeeUser()) && ObjectUtil.isNotEmpty(orderBigVo.getFeeUser().getPayAmount())) {
            // 历史支付金额累加此次补差的费用
            BigDecimal payAmount = orderBigVo.getFeeUser().getPayAmount().add(amount.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
            updateDo.setPayAmount(payAmount);
        } else {
            // 历史支付金额累加此次补差的费用
//            BigDecimal payAmount = orderBigVo.getOrderInfo().getPayAmount().add(amount.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
//            updateDo.setPayAmount(payAmount);
            throw new ServiceException(new ErrorCode(3000015, "用户支付金额为空,请联系管理处理"));
        }
        updateDo.setCompensatingResult(OrderConstants.ORDER_COMPENSATING_RESULT_THREE);
        //记录补差金额
        BigDecimal compensationPaid0 = ObjectUtil.isNotEmpty(orderBigVo.getOrderInfo().getCompensationPaid0()) ? orderBigVo.getOrderInfo().getCompensationPaid0() : BigDecimal.ZERO;
        compensationPaid0 = compensationPaid0.add(amount.divide(BigDecimal.valueOf(100)));
        updateDo.setCompensationPaid0(compensationPaid0);
        // 补差金额减去已补差金额=待补差金额
        BigDecimal compensationAmount = ObjectUtil.isNotEmpty(orderBigVo.getOrderInfo().getCompensationAmount()) ? orderBigVo.getOrderInfo().getCompensationAmount() : BigDecimal.ZERO;
        compensationAmount = compensationAmount.subtract(amount.divide(BigDecimal.valueOf(100)));
        updateDo.setCompensationAmount(compensationAmount);

        return updateDo;
    }

    /**
     * 处理超重补差订单钉钉推送
     */
    private void handleOverweightDindin(String orderNo) {
        OrderOverweightCallbackDO callbackDO = overweightCallbackService.getOrderOverweightCallbackByOrderNo(orderNo);
        if (ObjectUtil.isNotEmpty(callbackDO)) {
            overweightCallbackService.deleteDataByOrderNo(orderNo, callbackDO.getTenantId());
        }
    }

    /**
     * 处理补差数据
     */
    private void handleCompensationDate(OrderBigVo orderBigVo, BigDecimal amount, String payType, Long bizId) {
        // 需要做兼容（老版本兼容）
        OrderDO order = orderBigVo.getOrderInfo();

        String orderId = order.getId();

        // 组装修改数据
        compensationUpdate(orderBigVo, amount);
        // 查询补差明细表
        orderCompensateService.compensationFinish(orderId, amount.divide(BigDecimal.valueOf(100)), payType);
        // 处理分佣逻辑
        handleCompensationBrokerage(orderBigVo.getOrderInfo());
        // 记录订单支付情况
        savePayType(orderId, payType, amount.divide(BigDecimal.valueOf(100)), bizId);
        // 补差完成删除钉钉定时任务推送
        handleOverweightDindin(orderBigVo.getOrderInfo().getOrderNo());
        // 处理短信
        handleSysMessage(orderBigVo.getOrderId());
        // 处理通知message
        handleNotice(NoticeGroupCodeEnum.OVERWEIGHT_SURCHARGE.getCode(), orderBigVo.getOrderId());
        // ==================== 新增：取消多阶段通知流程 ====================
        cancelMultiStageNotification(orderBigVo.getOrderInfo());
        // 判断是否处理黑名单
        handleBlacklist(orderBigVo, order);
    }

    /**
     * ==================== 新增：取消多阶段通知流程 ====================
     * 取消多阶段通知流程
     *
     * @param orderDO 订单信息
     */
    private void cancelMultiStageNotification(OrderDO orderDO) {
        try {
            log.info("[cancelMultiStageNotification] 取消多阶段通知流程，订单ID: {}, 订单号: {}",
                    orderDO.getId(), orderDO.getOrderNo());

            // TODO: 集成多阶段通知服务
            // 当 pgsql 模块可用时，取消注释以下代码：
            /*
            compensationNotificationService.cancelCompensationNotification(
                orderDO.getId(),
                orderDO.getTenantId()
            );
            */

            // 临时日志记录，表示取消通知已触发
            log.info("[cancelMultiStageNotification] 多阶段通知流程已取消（待集成），订单ID: {}, 用户ID: {}",
                    orderDO.getId(), orderDO.getUserId());

        } catch (Exception e) {
            log.error("[cancelMultiStageNotification] 取消多阶段通知流程异常，订单ID: {}", orderDO.getId(), e);
            // 异常不影响主流程
        }
    }

    private void handleBlacklist(OrderBigVo orderBigVo, OrderDO order) {
        try {
            // 多条超重未补全部完成才解除拉黑
            /*Long overweightNum = orderService.isExistsOverweightNum(orderBigVo.getUserId());
            if (overweightNum <= 1) {
                handleBlacklist(orderBigVo.getUserId());
            }*/

            // 开启了自动解除拉黑
            Integer mParam = paramApi.getBlacklistMParam(order.getTenantId());
            if (mParam != null && mParam == 0) {
                UserFreezeLogCreateReqVO logCreateReqVO = new UserFreezeLogCreateReqVO();
                logCreateReqVO.setOrderNo(order.getOrderNo());
                logCreateReqVO.setOrderId(order.getId());
                logCreateReqVO.setUserId(order.getUserId());
                logCreateReqVO.setRemark(UserFreezeLogEnum.PATCH_SUCCESS.getRemark());
                logCreateReqVO.setType(UserFreezeLogEnum.PATCH_SUCCESS.getValue());

                userFreezeService.differenceDelete(orderBigVo.getOrderInfo().getOrderNo(), logCreateReqVO);
            }
        } catch (Exception e) {
            log.error("自动解除用户黑名单失败：{}", e.getMessage());
        }
    }

    private void handleNotice(String code, String orderId) {
        try {
            messageSendApi.cancelInformationPush(code, orderId);
        } catch (Exception e) {
            log.error("处理订单：{}通知失败", orderId);
        }
    }

    /**
     * 补差完成根据逻辑自动解除解除黑名单
     * 这里逻辑简单点直接根据userid去查询黑名单是否有数据（而且只解除本平台的）
     *
     * @param userId
     */
    private void handleBlacklist(Long userId) {
        if (ObjectUtil.isEmpty(userId)) {
            throw new ServiceException(592416, "自动解除用户黑名单userId为空");
        }
        Long tenantId = TenantContextHolder.getTenantId();
        Integer mParam = paramApi.getBlacklistMParam(tenantId);
        if (mParam != null && mParam == 0) {
            UserFreezeDO freezeByUserId = userFreezeService.getUserFreezeByUserId(userId, tenantId);
            if (ObjectUtils.isNotEmpty(freezeByUserId)) {
                TenantUtils.execute(tenantId, () -> {
                    MemberUserRespDTO user = memberUserApi.getUser(userId);
                    user.setFreezeFlag(0);
                    memberUserApi.updateUser(user);

                    userFreezeService.deleteUserFreeze(freezeByUserId.getId());
                });
            }
        }
    }

    private void savePayType(String orderId, String payType, BigDecimal amount, Long bizId) {
        // 记录支付方式
        OrderPaymentCreateReqVO createReqVO = new OrderPaymentCreateReqVO();

        createReqVO.setType("payment");
        createReqVO.setAmount(amount);
        createReqVO.setOrderId(orderId);
        createReqVO.setPayWay(payType);
        createReqVO.setBizId(bizId);
        createReqVO.setPaymentRole(OrderConstants.PAYMENT_ROLE_USER);

        orderPaymentService.createOrderPayment(createReqVO);
    }

    /**
     * 获取补差金额
     *
     * @return
     */
    private BigDecimal getMakeupAmount(OrderBigVo orderBigVo) {
        BigDecimal overweightAmount = BigDecimal.ZERO;

        // 新版本接口，做兼容
        if (ObjectUtil.isNotEmpty(orderBigVo.getFeeUser()) && ObjectUtil.isNotEmpty(orderBigVo.getFeeUser().getOverweightAmount())) {
            overweightAmount = orderBigVo.getFeeUser().getOverweightAmount();
        } else {
            // 旧版本接口
            overweightAmount = getOldMakeupAmount(orderBigVo.getOrderInfo());
        }

        return overweightAmount;
    }

    // 获取旧版本的补差金额
    private BigDecimal getOldMakeupAmount(OrderDO order) {
        // 根据老板要求：补差金额不能被修改，所以用户与租户要扣除的余额需要进行计算：扣除余额 = 补差金额 - （ 需补差金额 + 收益补差 + 余额补差 ）
        BigDecimal compensationAmount = ObjectUtil.isNotEmpty(order.getCompensationAmount()) ? order.getCompensationAmount() : BigDecimal.valueOf(0);
        BigDecimal compensationPaid0 = ObjectUtil.isNotEmpty(order.getCompensationPaid0()) ? order.getCompensationPaid0() : BigDecimal.valueOf(0);
        BigDecimal amount = (compensationAmount.subtract(compensationPaid0)).abs();
        return amount;
    }

    private void validateOrderStatus(String status) {
        Assert.isTrue(OrderConstants.ORDER_STATUS_TREAT_PAYMENT.equals(status), "订单已支付");
    }

    private void validateCompensationStatus(String compensatingState, String compensatingResult) {
        Assert.isTrue(OrderConstants.ORDER_COMPENSATING_STATE_OMPENSATIO.equals(compensatingState) && OrderConstants.ORDER_COMPENSATING_RESULT_TWO.equals(compensatingResult), "订单已补差");
    }

    private void validateSubmitParams(MiniOrderPayReqVO params, OrderDO orderDO) {
        Assert.isTrue(!StringUtils.isEmpty(params.getOrderType()), "缺少订单类型");

        // 一天之前的订单不允许再提交支付
        if (orderDO.getStatus().equals(OrderConstants.ORDER_STATUS_TREAT_PAYMENT)) {
            LocalDateTime createTime = orderDO.getCreateTime();
            LocalDateTime now = LocalDateTime.now();
            Assert.isTrue(!createTime.isBefore(now.minusDays(1)), "订单已过期");
        }

        // 支付方式校验
        String payType = params.getPayType();
        Assert.isTrue(OrderConstants.PAY_TYPE_BALANCE.equals(payType) || OrderConstants.PAY_TYPE_WECHAT.equals(payType) || OrderConstants.PAY_TYPE_ALIPAY.equals(payType), "支付方式不合法");
    }

    private void validateCompensationParams(MiniOrderPayReqVO params, OrderDO orderDO) {
        Assert.isTrue(!StringUtils.isEmpty(params.getOrderType()), "缺少订单类型");
        Assert.isTrue(!StringUtils.isEmpty(params.getPayType()), "缺少payType参数");
        Assert.isTrue(!StringUtils.isEmpty(orderDO), "订单不存在");
    }

    /**
     * 订单支付校验
     *
     * @param params
     * @param order
     */
    private void validateSubmit(MiniOrderPayReqVO params, @NotNull(message = "订单不存在") OrderDO order, BigDecimal discountPrice) {
        // 订单补差提交校验
        checkDuplicateSubmit(order.getTenantId(), order.getId());

        // 订单参数校验
        validateSubmitParams(params, order);

        // 订单状态校验
        validateOrderStatus(order.getStatus());

        // 代理商状态校验
        validateSubmitAgentStatus(order.getTenantId(), discountPrice);

        // 用户状态校验
        validateSubmitUserStatus(order.getUserId());

        // 支付渠道校验
        validateSubmitPayChannel(params.getPayType(), order.getTenantId());

        // 费用有消息校验
//        TODO 费用校验中需要从缓存中获取价格，但是 feeTagId 暂无保存， 无法获取数据进行校验
//        validateSubmitFee(params, order.getLastPrice());
    }

    /**
     * 订单补差校验
     *
     * @param params
     * @param order
     */
    private void validateCompensation(MiniOrderPayReqVO params, @NotNull(message = "订单不存在") OrderDO order) {
        // 订单补差提交校验
        checkDuplicateSubmit(order.getTenantId(), order.getId());

        // 订单参数校验
        validateCompensationParams(params, order);

        // 订单补差状态校验
        validateCompensationStatus(order.getCompensatingState(), order.getCompensatingResult());

        // 支付渠道校验
        validateSubmitPayChannel(params.getPayType(), order.getTenantId());

        // 费用有消息校验
//        TODO 费用校验中需要从缓存中获取价格，但是 feeTagId 暂无保存， 无法获取数据进行校验
//        validateSubmitFee(params, order.getLastPrice());
    }

    /**
     * 校验前端提交的费用与缓存中的费用是否一致
     *
     * @param params
     */
    private void validateSubmitFee(MiniOrderPayReqVO params, BigDecimal lastPrice) {
        BigDecimal price = (BigDecimal) redissonClient.getBucket("").get();
        Assert.isTrue(price.compareTo(lastPrice) == 0, "费用有变动，请刷新页面");
    }

    private void validateSubmitPayChannel(String payType, Long tenantId) {
        switch (payType) {
            // 余额支付
            case OrderConstants.PAY_TYPE_BALANCE:
                Assert.isTrue(paramApi.isOpenBalancePay(tenantId), "余额支付渠道已关闭");
                break;
            // 微信支付
            case OrderConstants.PAY_TYPE_WECHAT:
                Assert.isTrue(paramApi.isOpenWechatPay(tenantId), "微信支付渠道已关闭");
                break;
            // 支付宝支付
            case OrderConstants.PAY_TYPE_ALIPAY:
//                Assert.isTrue(paramApi.isOpenWechatPay(tenantId), "微信支付渠道已关闭");
                break;
            default:
                throw new ServiceException(20003, "不支持的支付渠道");
        }
    }

    /**
     * 校验加盟商的状态
     * 冻结的不能下单
     *
     * @param tenantId
     */
    private void validateSubmitAgentStatus(Long tenantId, BigDecimal discountPrice) {
        // 查询加盟商信息
        // TODO 加盟商信息改用缓存
        CommonResult<TenantRespVO> tenantResult = tenantApi.getTenantInfo(tenantId);
        Assert.isTrue(tenantResult.isSuccess(), "查询加盟商信息失败");

        TenantRespVO tenantRespVO = tenantResult.getData();

        // 加盟商冻结 不能下单
        Assert.isTrue(CommonStatusEnum.ENABLE.getStatus().equals(tenantRespVO.getStatus()), "小程序状态异常");

//        增加校验
//        代理商余额
        BigDecimal balanceAmount = null == tenantRespVO.getBalanceAmount() ? BigDecimal.ZERO : tenantRespVO.getBalanceAmount();
//        代理商限额
        BigDecimal safetyDeposit = null == tenantRespVO.getSafetyDeposit() ? BigDecimal.ZERO : tenantRespVO.getSafetyDeposit();
//        代理商保证金
        BigDecimal marginAmount = null == tenantRespVO.getMarginAmount() ? BigDecimal.ZERO : tenantRespVO.getMarginAmount();

        Assert.isTrue(balanceAmount.compareTo(safetyDeposit) > 0, "加盟商余额小于阈值，请联系在线客服咨询！");
        Assert.isTrue(marginAmount.compareTo(new BigDecimal(100)) > 0, "加盟商保证金小于阈值，请联系在线客服咨询！");
        Assert.isTrue(balanceAmount.compareTo(discountPrice) >= 0, "余额不足，请联系在线客服咨询！");

    }

    /**
     * 校验用户的状态
     */
    private void validateSubmitUserStatus(Long userId) {
        // 用户存在超重补差的订单不允许下单
        Assert.isTrue(!orderService.isExistsOverweight(userId), "有补差价订单未补");
    }

    /**
     * 检测重复提交的问题
     *
     * @param tenantId
     * @param orderId
     */
    private void checkDuplicateSubmit(Long tenantId, String orderId) {
        String redisLockKey = tenantId + "_" + orderId;

        RLock lock = redissonClient.getLock(redisLockKey);
        RLock wxPayLock = redissonClient.getLock("wxpay::lock::" + redisLockKey);

        boolean isLock;
        try {
            isLock = lock.tryLock(3, 10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        if (!isLock) {
            throw new ServiceException(590111, "请不要重复提交,10秒后重试");
        }

        boolean isWxPayLock;
        try {
            isWxPayLock = wxPayLock.tryLock(3, 10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        if (!isWxPayLock) {
            throw new ServiceException(590111, "当前订单已提交支付，请刷新页面。");
        }
    }

    /**
     * 构建寄件人信息
     *
     * @param bigVo 订单信息总线
     * @return 创建订单VO
     */
    private void copySenderInfo(OrderBigVo bigVo, CreateOrderVO createOrderVO) {
        Assert.notNull(bigVo.getSendContacts(), "寄件人信息缺失");
        OrderContactsDO sendContacts = bigVo.getSendContacts();
        createOrderVO.setSenderProvince(sendContacts.getProvince()).setSenderCity(sendContacts.getCity()).setSenderDistrict(sendContacts.getDistrict()).setSenderProvinceCode(sendContacts.getProvinceCode()).setSenderCityCode(sendContacts.getCityCode()).setSenderDistrictCode(sendContacts.getDistrictCode()).setSenderAddress(sendContacts.getAddress()).setSenderName(sendContacts.getName()).setSenderTel(sendContacts.getTelephone()).setSenderMobile(sendContacts.getMobile());
    }

    /**
     * 构建收件人信息
     *
     * @param bigVo 订单信息总线
     * @return 创建订单VO
     */
    private void copyReceiveInfo(OrderBigVo bigVo, CreateOrderVO createOrderVO) {
        Assert.notNull(bigVo.getSendContacts(), "寄件人信息缺失");
        OrderContactsDO receiveContacts = bigVo.getReceiveContacts();
        createOrderVO.setReceiveProvince(receiveContacts.getProvince()).setReceiveCity(receiveContacts.getCity()).setReceiveDistrict(receiveContacts.getDistrict()).setReceiveProvinceCode(receiveContacts.getProvinceCode()).setReceiveCityCode(receiveContacts.getCityCode()).setReceiveDistrictCode(receiveContacts.getDistrictCode()).setReceiveAddress(receiveContacts.getAddress()).setReceiveName(receiveContacts.getName()).setReceiveTel(receiveContacts.getTelephone()).setReceiveMobile(receiveContacts.getMobile());
    }

    /**
     * 构建订单基本信息
     *
     * @param bigVo
     * @param createOrderVO
     */
    private void copyOrderInfo(OrderBigVo bigVo, CreateOrderVO createOrderVO) {
        BeanUtil.copyProperties(bigVo.getOrderInfo(), createOrderVO);

// TODO order分库修改 (取订单费用表的数据)
//        需要获取原价
//        createOrderVO.setDefPrice(String.valueOf(bigVo.getOrderInfo().getOriginalPrice()));
//        折扣价
        createOrderVO.setPrice(String.valueOf(bigVo.getFeeSuper().getCost()));
    }

    /**
     * 构建订单物品信息
     *
     * @param bigVo
     * @param createOrderVO
     */
    private void copyOrderPriceInfo(OrderBigVo bigVo, CreateOrderVO createOrderVO) {
        BeanUtil.copyProperties(bigVo.getOrderParcel(), createOrderVO);

        // 重新计算重量
        weightRecalculation(bigVo, createOrderVO);

//        保价
        createOrderVO.setGuaranteeValueAmount(bigVo.getOrderParcel().getInsuredAmount());
//        预约取件时间
        if (ObjectUtil.isNotEmpty(bigVo.getOrderParcel().getAppointStartTime()) && ObjectUtil.isNotEmpty(bigVo.getOrderParcel().getAppointEndTime())) {
            createOrderVO.setPickUpStartTime(Date.from(bigVo.getOrderParcel().getAppointStartTime().atZone(ZoneId.systemDefault()).toInstant()));
            createOrderVO.setPickUpEndTime(Date.from(bigVo.getOrderParcel().getAppointEndTime().atZone(ZoneId.systemDefault()).toInstant()));
        }
    }

    /**
     * 重量重新计算
     */
    private void weightRecalculation(OrderBigVo bigVo, CreateOrderVO createOrderVO) {
        OrderDO orderInfo = bigVo.getOrderInfo();
        if (ObjectUtil.isEmpty(orderInfo)) {
            orderInfo = orderService.getOrder(bigVo.getOrderId());
        }
        // 快递100 重新计算下单重量
        if (null != bigVo.getOrderParcel().getVloumHeight() && null != bigVo.getOrderParcel().getVloumLong() && null != bigVo.getOrderParcel().getVloumWidth() && OrderConstants.ORDER_CHANNEL_KUAIDI100.equals(bigVo.getOrderInfo().getChannel() + "")) {
            try {
                BigDecimal lightGood = new BigDecimal(8000);
                ChannelExpressDetailVO channelExpress = channelExpressService.getChannelExpressDetail(bigVo.getOrderInfo().getChannelExpressId());
                if (ObjectUtil.isNotEmpty(channelExpress) && ObjectUtil.isNotEmpty(channelExpress.getWeightRatio())) {
                    lightGood = channelExpress.getWeightRatio();
                }
                // 计算体积和抛比 重新赋值重量去查询价格
                BigDecimal vloumLong = bigVo.getOrderParcel().getVloumLong();
                BigDecimal vloumHeight = bigVo.getOrderParcel().getVloumHeight();
                BigDecimal vloumWidth = bigVo.getOrderParcel().getVloumWidth();
                if (null != vloumHeight && null != vloumLong && null != vloumWidth && null != lightGood) {
                    BigDecimal vloumWeight = vloumHeight.multiply(vloumLong).multiply(vloumWidth).divide(lightGood, 2, RoundingMode.HALF_UP).setScale(0, RoundingMode.UP);
                    if (bigVo.getOrderParcel().getWeight().compareTo(vloumWeight) < 0) {
                        createOrderVO.setWeight(vloumWeight);
                    }
                }
            } catch (Exception e) {
                log.error("快递100体积记录重量失败, {}", e);
            }
        }
    }

    /**
     * 调用微信支付接口
     *
     * @param params
     * @param order
     * @param coreMap
     * @param appList
     * @return
     */
    private Long buildWeChatPayment(MiniOrderPayReqVO params, OrderDO order, Map<String, Object> coreMap, List<PayAppDO> appList, BigDecimal lastPrice) {
        String subject = "快递寄件";
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String openId = socialUserApi.getUserOpenId(userId, params.getCode(), SocialTypeEnum.WECHAT_MINI_APP.getType()).getData();
        // 如果openId为空，删除token让用户重新登录
        if (ObjectUtil.isEmpty(openId)) {
            // 删除用户和token
            memberUserApi.removeAccessTokenByUserId(userId);
            throw new ServiceException(new ErrorCode(401111, "当前登录已失效，请重新登录。"));
        }
        Map<String, String> channelExtras = new HashMap<>();
        channelExtras.put("openid", openId);
        channelExtras.put("orderId", order.getId());
        Integer price = lastPrice.multiply(BigDecimal.valueOf(100)).intValue();
//        Integer price = 1;
        channelExtras.put("type", "deliveryV2");
        Long payOrderId = payOrderService.createOrder(new PayOrderCreateReqDTO().setAppId(appList.get(0).getId()).setUserIp(getClientIP()) // 支付应用
                .setMerchantOrderId(IdWorker.getIdStr()) // 业务的订单编号
                .setSubject(subject).setBody(order.getDeliveryType()).setPrice(price) // 价格信息
                .setExpireTime(addTime(Duration.ofHours(2L))).setUserId(userId));

        PayOrderSubmitReqVO reqVO = new PayOrderSubmitReqVO();
        reqVO.setChannelCode(PayChannelEnum.WX_LITE.getCode());
        reqVO.setId(payOrderId);
        reqVO.setChannelExtras(channelExtras);
        reqVO.setDisplayMode(PayOrderDisplayModeEnum.APP.getMode());

        PayOrderSubmitRespVO payOrderSubmitRespVO = payOrderService.submitOrder(reqVO, getClientIP());
        String coreJson = payOrderSubmitRespVO.getDisplayContent();
//        coreMap = JSONUtil.toBean(coreJson, Map.class);
//        coreMap.put("package", coreMap.get("packageValue"));

        Map<String, Object> tempMap = JSONUtil.toBean(coreJson, Map.class);
        tempMap.put("package", tempMap.get("packageValue"));
        tempMap.put("payOrderId", payOrderId);

        coreMap.putAll(tempMap);

        return payOrderId;
    }

    /**
     * 获取补差金额
     *
     * @param order
     * @return
     */
    public Integer getCompensationAmount(OrderDO order) {
        Integer price = 0;
        BigDecimal compensationAmount = ObjectUtil.isNotEmpty(order.getCompensationAmount()) ? order.getCompensationAmount() : BigDecimal.valueOf(0);
        BigDecimal compensationPaid0 = ObjectUtil.isNotEmpty(order.getCompensationPaid0()) ? order.getCompensationPaid0() : BigDecimal.valueOf(0);
        BigDecimal amount = (compensationAmount.subtract(compensationPaid0)).abs();
        price = amount.multiply(BigDecimal.valueOf(100)).intValue();
        return price;
    }

    /**
     * 获取补差金额不转换
     *
     * @param order
     * @return
     */
    public BigDecimal getCompensationAmountV2(OrderDO order) {
        Integer price = 0;
        BigDecimal compensationAmount = ObjectUtil.isNotEmpty(order.getCompensationAmount()) ? order.getCompensationAmount() : BigDecimal.valueOf(0);
        BigDecimal compensationPaid0 = ObjectUtil.isNotEmpty(order.getCompensationPaid0()) ? order.getCompensationPaid0() : BigDecimal.valueOf(0);
        BigDecimal amount = (compensationAmount.subtract(compensationPaid0)).abs();
        return amount;
    }


    /**
     * 调用微信支付接口
     *
     * @param params
     * @param order
     * @param coreMap
     * @param appList
     * @return
     */
    private Long buildWeChatOverweightPayment(MiniOrderPayReqVO params, OrderDO order, Map coreMap, List<PayAppDO> appList) {
        String subject = "超重补差";
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String openId = socialUserApi.getUserOpenId(userId, params.getCode(), SocialTypeEnum.WECHAT_MINI_APP.getType()).getData();
        // 如果openId为空，删除token让用户重新登录
        if (ObjectUtil.isEmpty(openId)) {
            // 删除用户和token
            memberUserApi.removeAccessTokenByUserId(userId);
            throw new ServiceException(new ErrorCode(401111, "当前登录已失效，请重新登录。"));
        }
        Map<String, String> channelExtras = new HashMap<>();
        channelExtras.put("openid", openId);
        channelExtras.put("orderId", order.getId());
//        order.getLastPrice().multiply(BigDecimal.valueOf(100)).intValue()
        Integer price = getCompensationAmount(order);
        // 查询补差金额

        channelExtras.put("type", "compensationV2");
        Long payOrderId = payOrderService.createOrder(new PayOrderCreateReqDTO().setAppId(appList.get(0).getId()).setUserIp(getClientIP()) // 支付应用
                .setMerchantOrderId(IdWorker.getIdStr()) // 业务的订单编号
                .setSubject(subject).setBody(order.getDeliveryType()).setPrice(price) // 价格信息
                .setExpireTime(addTime(Duration.ofHours(2L))).setUserId(userId));
        PayOrderSubmitReqVO reqVO = new PayOrderSubmitReqVO();
        reqVO.setChannelCode(PayChannelEnum.WX_LITE.getCode());
        reqVO.setId(payOrderId);
        reqVO.setChannelExtras(channelExtras);
        reqVO.setDisplayMode(PayOrderDisplayModeEnum.APP.getMode());

        PayOrderSubmitRespVO payOrderSubmitRespVO = payOrderService.submitOrder(reqVO, getClientIP());
        String coreJson = payOrderSubmitRespVO.getDisplayContent();
//        coreMap = JSONUtil.toBean(coreJson, Map.class);
//        coreMap.put("package", coreMap.get("packageValue"));

        Map<String, Object> tempMap = JSONUtil.toBean(coreJson, Map.class);
        tempMap.put("package", tempMap.get("packageValue"));
        tempMap.put("payOrderId", payOrderId);
        coreMap.putAll(tempMap);

        return payOrderId;
    }

    private void createWechatPayLog(Long payment, OrderDO order, BigDecimal lastPrice) {
        WechatPayLogCreateReqVO createReqVO = new WechatPayLogCreateReqVO();
        createReqVO.setPayOrderId(payment);
        createReqVO.setBizType(FeeConstants.WECHAT_PAY_BIZ_ORDERPAY);
        createReqVO.setOrderId(Long.valueOf(order.getId()));
        createReqVO.setMbrId(SecurityFrameworkUtils.getLoginUserId());
        createReqVO.setPayStatus(FeeConstants.PAY_STATUS_UNPAY);
        createReqVO.setPayAmount(lastPrice);
        // 新增添加运单号
        createReqVO.setOrderNo(order.getOrderNo());
        wechatPayLogService.createWechatPayLog(createReqVO);
    }


    /**
     * 构建创建订单参数
     *
     * @param createOrderVO
     */
    private void buildCreateOrderParams(CreateOrderVO createOrderVO) {
        if (checkPhoneNumberType(createOrderVO.getSenderMobile())) {
            createOrderVO.setSenderMobile(createOrderVO.getSenderMobile());
            createOrderVO.setSenderTel(null);
        } else {
            createOrderVO.setSenderTel(createOrderVO.getSenderMobile());
            createOrderVO.setSenderMobile(null);
        }
        if (checkPhoneNumberType(createOrderVO.getReceiveMobile())) {
            createOrderVO.setReceiveMobile(createOrderVO.getReceiveMobile());
            createOrderVO.setReceiveTel(null);
        } else {
            createOrderVO.setReceiveTel(createOrderVO.getReceiveMobile());
            createOrderVO.setReceiveMobile(null);
        }

    }

    private Boolean checkPhoneNumberType(String number) {
//
        if (ObjectUtil.isEmpty(number)) {
            throw new ServiceException(new ErrorCode(500, "手机号码为空，请输入手机号码"));
        }
        // 手机号码正则表达式
        String mobileRegex = "^1[3-9]\\d{9}$";
        // 座机号码正则表达式
//        String landlineRegex = "^(\\d{3,4}-\\d{7,8})$";


        Pattern mobilePattern = Pattern.compile(mobileRegex);
//        Pattern landlinePattern = Pattern.compile(landlineRegex);

        Matcher mobileMatcher = mobilePattern.matcher(number);
//        Matcher landlineMatcher = landlinePattern.matcher(number);

        if (mobileMatcher.matches()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 订单参数校验
     *
     * @param createOrderVO
     */
    private void validateBackgroundCreate(CreateOrderVO createOrderVO) {
        // 订单重复提交校验
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Long tenantId = TenantContextHolder.getTenantId();
//        if (ObjectUtil.isNotEmpty(userId)) {
//            String key = tenantId + "_" + userId;
//            if (!stringRedisTemplate.opsForValue().setIfAbsent("lock::back_create_order::" + key, "", 5, TimeUnit.SECONDS)) {
//                throw new ServiceException(new ErrorCode(31501, "订单已提交，请勿重复操作"));
//            }
//        }
        // 订单参数校验
        validateCreateOrderParams(createOrderVO);

        // 用户状态校验
        if (ObjectUtil.isNull(userId)) {
            throw new ServiceException(new ErrorCode(21501, "用户未登录"));
        }

        // 代理商校验
        validateAgent();


    }

    /**
     * 后台订单创建校验
     */
    private void validateCreateOrderParams(CreateOrderVO createOrderVO) {
        Long channelExpressId = createOrderVO.getChannelExpressId();
        KdChannelDO channelExpress = kdChannelService.getChannel(channelExpressId);
        Long channel = channelExpress.getThirdId();
        Assert.notNull(channel, "没有找到订单渠道信息，请联系管理员处理");

        // 查询订单渠道是否开通
        if (!kdChannelService.isChannelOpen(channelExpressId)) {
            log.error("当前下单渠道还未开通, {}", channelExpressId);
            throw new ServiceException(new ErrorCode(31502, "当前渠道已关闭"));
        }

        Assert.notNull(createOrderVO.getChannelExpressId(), "没有选择快递信息");
        //设置渠道code
//        createOrderVO.setDeliveryType(channelExpress.getCode());
        //设置渠道
        createOrderVO.setChannel(channel + "");


    }


    private void createWechatOverweightPayLog(Long payment, OrderDO order) {
        WechatPayLogCreateReqVO createReqVO = new WechatPayLogCreateReqVO();
        createReqVO.setPayOrderId(payment);
        createReqVO.setBizType(FeeConstants.WECHAT_PAY_BIZ_MOREWEIGHT);
        createReqVO.setOrderId(Long.valueOf(order.getId()));
        createReqVO.setMbrId(SecurityFrameworkUtils.getLoginUserId());
        createReqVO.setPayStatus(FeeConstants.PAY_STATUS_UNPAY);
        createReqVO.setPayAmount(order.getCompensationAmount());
        // 新增添加运单号
        createReqVO.setOrderNo(order.getOrderNo());
        wechatPayLogService.createWechatPayLog(createReqVO);
    }



    @Override
    @GlobalTransactional
    @DSTransactional
    public Boolean updateOrder(MiniUpdateOrderReqVO params) {
        String orderId = params.getOrderId();
        OrderDO order = orderService.getOrder(orderId);

        if (ObjectUtil.isEmpty(params) && ObjectUtil.isEmpty(params.getPickUpStartTime())) {
            throw new ServiceException(new ErrorCode(31502, "预约开始时间为空"));
        }
        if (ObjectUtil.isEmpty(params) && ObjectUtil.isEmpty(params.getPickUpEndTime())) {
            throw new ServiceException(new ErrorCode(31502, "预约结束时间为空"));
        }

//        if(!(OrderConstants.ORDER_CHANNEL_KDNIAO.equals(order.getChannel()) || OrderConstants.ORDER_CHANNEL_YIDA.equals(order.getChannel()))){
        if(!(OrderConstants.ORDER_CHANNEL_KDNIAO.equals(order.getChannel()))){
            throw new ServiceException(new ErrorCode(31502, "当前订单不允许修改预约时间"));
        }

        String redisKye = "lock::orderupdate::" + orderId;
        //redis 加锁
        if (!redisTemplate.opsForValue().setIfAbsent(redisKye, orderId, 3, TimeUnit.SECONDS)) {
            throw new ServiceException(new ErrorCode(31502, "修改订单执行中，请不要重复点击"));
        }

        try {
            // 构建扣费对象、构建订单对象
            OrderBigVo orderBigVo = orderService.assembleBigVo(orderId);
            UpdateOrderVO UpdateOrderVO = buildThirdUpdateOrderParams(orderBigVo);
            UpdateOrderVO.setPickUpStartTime(params.getPickUpStartTime());
            UpdateOrderVO.setPickUpEndTime(params.getPickUpEndTime());

            // 调用第三方修改
            UpdateOrderResVo updateOrderResVo = callThirdUpdateOrderApi(UpdateOrderVO,order);
            if(updateOrderResVo.isResult()){
                if(ObjectUtil.isNotEmpty(orderBigVo.getOrderParcel())){
                    // 修改订单包裹表的预约时间
                    OrderParcelUpdateReqVO updateReqVO = new OrderParcelUpdateReqVO();
                    updateReqVO.setId(orderBigVo.getOrderParcel().getId());
                    updateReqVO.setAppointStartTime(DateUtil.toLocalDateTime(params.getPickUpStartTime()));
                    updateReqVO.setAppointEndTime(DateUtil.toLocalDateTime(params.getPickUpEndTime()));
                    orderParcelService.updateOrderParcel(updateReqVO);
                }

                return true;
            } else {
                throw new ServiceException(new ErrorCode(31502, updateOrderResVo.getMessage()));
            }

        } finally {
            //释放锁
            stringRedisTemplate.delete(redisKye);
        }
    }


    /**
     * 调用第三方修改订单接口
     */
    private UpdateOrderResVo callThirdUpdateOrderApi(UpdateOrderVO updateOrder, OrderDO orderDO) {
        UpdateOrderResVo result = new UpdateOrderResVo();
        try {
            result = TenantBeanUtils.getKuaidiRequestBean(updateOrder.getTenantId(), updateOrder.getChannel()).updateOrder(updateOrder, envProperties.isTestApi());
        } catch (Exception ex) {
            log.error("修改订单失败 {}", ex.getMessage());
            result.setResult(false);
            result.setMessage("修改订单失败，请联系管理员");
        } finally {

        }
        return result;
    }


    public UpdateOrderVO buildThirdUpdateOrderParams(OrderBigVo bigVo) {
        UpdateOrderVO UpdateOrderVO = new UpdateOrderVO();

//        构建订单基本信息
        copyUpdateOrderInfo(bigVo, UpdateOrderVO);


        // 构建发件人信息
        copyUpdateSenderInfo(bigVo, UpdateOrderVO);

        // 构建收件人信息
        copyUpdateReceiveInfo(bigVo, UpdateOrderVO);


        UpdateOrderVO.setOrderId(bigVo.getOrderId());

        return UpdateOrderVO;
    }

    /**
     * 构建订单基本信息
     *
     * @param bigVo
     * @param updateOrderVO
     */
    private void copyUpdateOrderInfo(OrderBigVo bigVo, UpdateOrderVO updateOrderVO) {
        BeanUtil.copyProperties(bigVo.getOrderInfo(), updateOrderVO);
        updateOrderVO.setPrice(String.valueOf(bigVo.getFeeSuper().getCost()));
    }

    /**
     * 构建寄件人信息
     *
     * @param bigVo 订单信息总线
     * @return 创建订单VO
     */
    private void copyUpdateSenderInfo(OrderBigVo bigVo, UpdateOrderVO updateOrder) {
        Assert.notNull(bigVo.getSendContacts(), "寄件人信息缺失");
        OrderContactsDO sendContacts = bigVo.getSendContacts();
        updateOrder.setSenderProvince(sendContacts.getProvince()).setSenderCity(sendContacts.getCity()).setSenderDistrict(sendContacts.getDistrict()).setSenderProvinceCode(sendContacts.getProvinceCode()).setSenderCityCode(sendContacts.getCityCode()).setSenderDistrictCode(sendContacts.getDistrictCode()).setSenderAddress(sendContacts.getAddress()).setSenderName(sendContacts.getName()).setSenderTel(sendContacts.getTelephone()).setSenderMobile(sendContacts.getMobile());
    }


    /**
     * 构建收件人信息
     *
     * @param bigVo 订单信息总线
     * @return 创建订单VO
     */
    private void copyUpdateReceiveInfo(OrderBigVo bigVo, UpdateOrderVO updateOrder) {
        Assert.notNull(bigVo.getSendContacts(), "寄件人信息缺失");
        OrderContactsDO receiveContacts = bigVo.getReceiveContacts();
        updateOrder.setReceiveProvince(receiveContacts.getProvince()).setReceiveCity(receiveContacts.getCity()).setReceiveDistrict(receiveContacts.getDistrict()).setReceiveProvinceCode(receiveContacts.getProvinceCode()).setReceiveCityCode(receiveContacts.getCityCode()).setReceiveDistrictCode(receiveContacts.getDistrictCode()).setReceiveAddress(receiveContacts.getAddress()).setReceiveName(receiveContacts.getName()).setReceiveTel(receiveContacts.getTelephone()).setReceiveMobile(receiveContacts.getMobile());
    }

    @Override
    public List<TdAppointTimeDTO> getOrderAppointment(String orderId) {
        Assert.notNull(orderId, "订单id为空");
        OrderDO order = orderService.getOrder(orderId);
        Assert.notNull(order, "当前订单已被删除");
//        if(!(OrderConstants.ORDER_CHANNEL_KDNIAO.equals(order.getChannel()) || OrderConstants.ORDER_CHANNEL_YIDA.equals(order.getChannel()))){
        if(!(OrderConstants.ORDER_CHANNEL_KDNIAO.equals(order.getChannel()))){
            return null;
//            throw new ServiceException(new ErrorCode(31502, "当前订单不允许修改预约时间"));
        }

        // 构建扣费对象、构建订单对象
        OrderBigVo orderBigVo = orderService.assembleBigVo(orderId);
        QueryAppointTimeReqVO queryAppointTimeReqVO = buildThirdAppointmentParams(orderBigVo,order);

        // 查询预约时间列表
        List<TdAppointTimeDTO> tdAppointTimeDTOS = TenantBeanUtils.getKuaidiRequestBean(order.getTenantId(), order.getChannel()).queryAppointTime(queryAppointTimeReqVO);

        return tdAppointTimeDTOS;
    }


    public QueryAppointTimeReqVO buildThirdAppointmentParams(OrderBigVo bigVo, OrderDO order) {
        QueryAppointTimeReqVO queryAppointTimeReqVO = new QueryAppointTimeReqVO();


        // 构建发件人信息
        copyAppointmentSenderInfo(bigVo, queryAppointTimeReqVO);

        // 构建收件人信息
        copyAppointmentReceiveInfo(bigVo, queryAppointTimeReqVO);

        if(ObjectUtil.isNotEmpty(order.getChannelExpressId())){
            KdChannelDO channelDO = kdChannelService.getChannel(order.getChannelExpressId());
            if(ObjectUtil.isNotEmpty(channelDO) && ObjectUtil.isNotEmpty(channelDO.getPickupRuleId())){
                ProductPickupRuleDO productPickupRule = productPickupRuleService.getProductPickupRule(channelDO.getPickupRuleId());
                if(ObjectUtil.isNotEmpty(productPickupRule)){
                    queryAppointTimeReqVO.setPickupRule(productPickupRule.getPickupTime());
                    queryAppointTimeReqVO.setPickupRuleType(productPickupRule.getType());
                    queryAppointTimeReqVO.setPostponeTime(productPickupRule.getPostponeTime());
                }
            }
        }

//        KdChannelDO channelDO = kdChannelService.getChannel(order.getChannelExpressId());
//        ProductPickupRuleDO productPickupRule = productPickupRuleService.getProductPickupRule(channelDO.getPickupRuleId());

        return queryAppointTimeReqVO;
    }


    /**
     * 构建寄件人信息
     *
     * @param bigVo 订单信息总线
     * @return 创建订单VO
     */
    private void copyAppointmentSenderInfo(OrderBigVo bigVo, QueryAppointTimeReqVO queryAppointTimeReqVO) {
        Assert.notNull(bigVo.getSendContacts(), "寄件人信息缺失");
        OrderContactsDO sendContacts = bigVo.getSendContacts();
        queryAppointTimeReqVO.setSenderProvince(sendContacts.getProvince()).setSenderCity(sendContacts.getCity()).setSenderDistrict(sendContacts.getDistrict()).setSenderAddress(sendContacts.getAddress()).setSenderName(sendContacts.getName()).setSenderTel(sendContacts.getTelephone()).setSenderMobile(sendContacts.getMobile());
    }


    /**
     * 构建收件人信息
     *
     * @param bigVo 订单信息总线
     * @return 创建订单VO
     */
    private void copyAppointmentReceiveInfo(OrderBigVo bigVo, QueryAppointTimeReqVO queryAppointTimeReqVO) {
        Assert.notNull(bigVo.getSendContacts(), "寄件人信息缺失");
        OrderContactsDO receiveContacts = bigVo.getReceiveContacts();
        queryAppointTimeReqVO.setReceiveProvince(receiveContacts.getProvince()).setReceiveCity(receiveContacts.getCity()).setReceiveDistrict(receiveContacts.getDistrict()).setReceiveAddress(receiveContacts.getAddress()).setReceiveName(receiveContacts.getName()).setReceiveTel(receiveContacts.getTelephone()).setReceiveMobile(receiveContacts.getMobile());
    }
}
