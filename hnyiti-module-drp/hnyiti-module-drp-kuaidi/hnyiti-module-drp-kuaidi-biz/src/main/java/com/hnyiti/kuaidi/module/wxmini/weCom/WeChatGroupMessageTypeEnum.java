package com.hnyiti.kuaidi.module.wxmini.weCom;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 企业微信消息推送类型
 */
@Getter
@AllArgsConstructor
public enum WeChatGroupMessageTypeEnum {

    WEIGHT_NOTIFY(1, "补差价通知", "21"),
    BALANCE_NOTIFY(2, "代理商余额不足通知", "22"),
    WORK_NOTIFY(3, "工单处理通知", "10"),
    CALLBACK_ERROR_NOTIFY(4,"订单回调失败通知","18"),
    PAY_ERROR_NOTIFY(5,"支付失败通知","19"),
    REFUND_ERROR_NOTIFY(6,"退款失败通知","20"),
    PICKET_YIDA_TOKEN(7,"数据纠察易达TOKEN失效","101"),
    MIGRATE_HISTORY_ERROR_NOTIFY(8,"定时任务迁移数据异常","30"),
    PASSCALLBACK_TIPS_NOTIFY(9,"PassCallback判断提示消息","40"),
    DATA_MOVE_TIPS_NOTIFY(10,"SPLIT-数据迁移异常","45"),
    PHONE_GET_ERROR_NOTIFY(11,"手机号获取异常","404"),
    REFUND_CALLBACK_ERROR_NOTIFY(12,"微信回调状态失败","400"),

    ORDER_FAILED_NOTIFY(13,"渠道下单失败通知","410"),
    AMOUNT_THRESHOLD_NOTIFY(14,"提现阈值通知","420"),
    FAILED_MERGE_USERS(15,"用户合并失败通知","500"),
    DIRECT_PUSH_NOTIFY(16,"直推通知-传递webhook","510"),
    PRICE_VARIANCE_NOTIFY(19,"订单价格差异通知","19"),
    MQ_OVERSTOCK_NOTIFY(20,"消息队列监控情况","20"),
    MQ_PGSQL_ERROR_NOTIFY(21, "pgsql保存信息异常", "50"),
    LOGIN_NOTIFY(22, "用户登录通知", "60"),

    ;

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 类型名字
     */
    private final String typeName;

    /**
     * 数据库通知类型value值
     */
    private String value;

    public static String getTypeNameByCode(int code) {
        for (WeChatGroupMessageTypeEnum express : WeChatGroupMessageTypeEnum.values()) {
            if (express.code.equals(code)) {
                return express.typeName;
            }
        }
        return "提醒通知";
    }

    public static String getValueByCode(int code) {
        for (WeChatGroupMessageTypeEnum express : WeChatGroupMessageTypeEnum.values()) {
            if (express.code.equals(code)) {
                return express.value;
            }
        }
        return "21";
    }

    public static WeChatGroupMessageTypeEnum fromCode(int code) {
        for (WeChatGroupMessageTypeEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
