package com.hnyiti.kuaidi.module.delivery.framework.config;

import com.hnyiti.kuaidi.module.api.callback
        .CallbackLogApi;
import com.hnyiti.kuaidi.module.member.api.balance.vo.api.BalanceApi;
import com.hnyiti.kuaidi.module.member.api.balancelog.BalanceLogApi;
import com.hnyiti.kuaidi.module.member.api.distribution.DistributionConfigApi;
import com.hnyiti.kuaidi.module.member.api.levels.MemberLevelsApi;
import com.hnyiti.kuaidi.module.member.api.memberupgradecondition.MemberUpgradeConditionApi;
import com.hnyiti.kuaidi.module.member.api.user.MemberUserApi;
import com.hnyiti.kuaidi.module.member.api.usercomplain.UserComplainApi;
import com.hnyiti.kuaidi.module.member.api.userparam.UserParamConfigApi;
import com.hnyiti.kuaidi.module.message.api.MessageSendApi;
import com.hnyiti.kuaidi.module.message.api.group.NoticeGroupApi;
import com.hnyiti.kuaidi.module.pgsql.api.order.PgsqlOrderApi;
import com.hnyiti.kuaidi.module.pgsql.api.user.PgsqlAddressFreezeApi;
import com.hnyiti.kuaidi.module.pgsql.api.user.PgsqlUserFreezeApi;
import com.hnyiti.kuaidi.module.system.api.dept.DeptApi;
import com.hnyiti.kuaidi.module.system.api.dept.PostApi;
import com.hnyiti.kuaidi.module.system.api.dict.DictDataApi;
import com.hnyiti.kuaidi.module.system.api.menuconfig.MenuConfigApi;
import com.hnyiti.kuaidi.module.system.api.param.ReasonParamApi;
import com.hnyiti.kuaidi.module.system.api.permission.RoleApi;
import com.hnyiti.kuaidi.module.system.api.serveriteratelog.ServerIterateLogApi;
import com.hnyiti.kuaidi.module.system.api.sms.SmsCodeApi;
import com.hnyiti.kuaidi.module.system.api.sms.SmsSendApi;
import com.hnyiti.kuaidi.module.system.api.social.SocialUserApi;
import com.hnyiti.kuaidi.module.system.api.system.SystemSensitiveWordApi;
import com.hnyiti.kuaidi.module.system.api.tenant.TenantApi;
import com.hnyiti.kuaidi.module.system.api.tenantAmount.TenantAmountApi;
import com.hnyiti.kuaidi.module.system.api.tenantAmountLog.TenantAmountLogApi;
import com.hnyiti.kuaidi.module.system.api.user.AdminUserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = { BalanceApi.class, RoleApi.class, MemberUserApi.class, UserComplainApi.class,
        SocialUserApi.class, SmsCodeApi.class, DeptApi.class, PostApi.class, AdminUserApi.class,
        SmsSendApi.class, DictDataApi.class, BalanceLogApi.class, TenantApi.class, SystemSensitiveWordApi.class, MessageSendApi.class,
        MemberLevelsApi.class, CallbackLogApi.class, TenantAmountApi.class, TenantAmountLogApi.class, MenuConfigApi.class,
        ReasonParamApi.class, NoticeGroupApi.class, ServerIterateLogApi.class, UserParamConfigApi.class, DistributionConfigApi.class,
        MemberUpgradeConditionApi.class, PgsqlOrderApi.class, PgsqlUserFreezeApi.class, PgsqlAddressFreezeApi.class})
public class RpcConfiguration {
}
