package com.hnyiti.kuaidi.module.pgsql.api.notification.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 创建通知任务 API 请求 VO
 *
 * <AUTHOR>
 */
@Data
public class CreateNotificationTaskApiReqVO {

    /**
     * 订单ID
     */
    @NotBlank(message = "订单ID不能为空")
    private String orderId;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    /**
     * 任务类型：MINI_PROGRAM, WECHAT_MP, SMS, PHONE_CALL
     */
    @NotBlank(message = "任务类型不能为空")
    private String taskType;

    /**
     * 计划执行时间
     */
    private LocalDateTime scheduledTime;

    /**
     * 任务数据（收件人、内容等）
     */
    private Map<String, Object> taskData;
}
