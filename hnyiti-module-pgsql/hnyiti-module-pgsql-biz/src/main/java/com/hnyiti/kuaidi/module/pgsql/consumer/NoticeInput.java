package com.hnyiti.kuaidi.module.pgsql.consumer;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.SubscribableChannel;

public interface NoticeInput {
    // 消费立即通知消息
    String COMPENSATION_IMMEDIATE_NOTIFY = "compensation-immediate-input";

    // 消费延时通知消息
    String COMPENSATION_DELAYED_NOTIFY_INPUT = "compensation-delayed-notify-input";

    // 消费重新调度消息
    String COMPENSATION_RESCHEDULE_NOTIFY_INPUT = "compensation-reschedule-notify-input";


    /**
     * 消费立即通知消息
     *
     * @return
     */
    @Input(COMPENSATION_IMMEDIATE_NOTIFY)
    SubscribableChannel compensationImmediateNotifyInput();


    /**
     * 消费延时通知消息
     *
     * @return
     */
    @Input(COMPENSATION_DELAYED_NOTIFY_INPUT)
    SubscribableChannel compensationDelayedNotifyInput();


    /**
     * 消费重新调度消息
     *
     * @return
     */
    @Input(COMPENSATION_RESCHEDULE_NOTIFY_INPUT)
    SubscribableChannel compensationRescheduleNotifyInput();

}
