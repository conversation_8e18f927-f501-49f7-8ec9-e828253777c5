package com.hnyiti.kuaidi.module.consumer;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.hnyiti.kuaidi.framework.redis.util.RedisUtils;
import com.hnyiti.kuaidi.module.consumer.vo.CallbackRepeatVo;
import com.hnyiti.kuaidi.module.consumer.vo.MarketingInputVo;
import com.hnyiti.kuaidi.module.consumer.vo.NotificationMessage;
import com.hnyiti.kuaidi.module.consumer.vo.RebateReportedVo;
import com.hnyiti.kuaidi.module.delivery.api.order.OrderApiImpl;
import com.hnyiti.kuaidi.module.delivery.api.order.vo.OrderAllVo;
import com.hnyiti.kuaidi.module.delivery.controller.admin.differencelog.vo.DifferenceLogCreateReqVO;
import com.hnyiti.kuaidi.module.delivery.controller.admin.order.OrderCallbackVo;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.order.OrderDO;
import com.hnyiti.kuaidi.module.delivery.service.cache.CacheServiceImpl;
import com.hnyiti.kuaidi.module.member.api.user.MemberUserApi;
import com.hnyiti.kuaidi.module.message.api.dto.BatchMessageSendReqDTO;
import com.hnyiti.kuaidi.module.system.api.tenant.TenantApi;
import com.hnyiti.kuaidi.module.wxmini.weCom.WeChatGroupNotifyReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.context.annotation.Lazy;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@EnableBinding(KuaiDiProducer.class)
@Slf4j
public class OrderMqProducer {
    @Autowired
    private KuaiDiProducer mqProducer;
    @Lazy
    @Resource
    private CacheServiceImpl cacheService;
    @Resource
    private MemberUserApi memberUserApi;
    @Resource
    private TenantApi tenantApi;
    @Lazy
    @Resource
    private OrderApiImpl orderApi;
    @Resource
    private RedisUtils redisUtils;

    /**
     * 订单风控
     * @param orderRiskVo
     * @return
     */
    public boolean sendRiskMessage(MQOrderRiskVo orderRiskVo) {
        log.info("风控消息：" + orderRiskVo);
        //延迟5分钟
        int delayTimeLevel = 4;
        return mqProducer.orderRiskOutput().send(MessageBuilder.withPayload(orderRiskVo)
                        .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }

    /**
     * 订单异常退款
     * @param orderRefundVo
     * @return
     */
    public boolean sendOrderRefundMessage(MQOrderRefundVo orderRefundVo) {
        log.info("订单异常退款消息：" + orderRefundVo);
        //延迟5秒
        int delayTimeLevel = 2;
        return mqProducer.orderRefundOutput().send(MessageBuilder.withPayload(orderRefundVo)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }

    /**
     * 计算订单分佣
     * @param orderKickbackVo 计算分佣VO
     * @return
     */
    public boolean sendCalculationKickback(MQOrderKickbackVo orderKickbackVo){
        log.info("订单分佣计算消息：" + orderKickbackVo);

        // 延迟时间
        int delayTimeLevel = 3;
        return mqProducer.calculationKickback().send(MessageBuilder.withPayload(orderKickbackVo)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }


    /**
     * 订单支付补偿消息
     * @return
     */
    public boolean sendOrderPayCompensation(MQOrderPayRemedyVo mqOrderPayRemedyVo, int delayTimeLevel){
        log.info("订单支付补偿消息：" + mqOrderPayRemedyVo);

        return mqProducer.orderPayCompensation().send(MessageBuilder.withPayload(mqOrderPayRemedyVo)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }

    /**
     * 用户余额充值补偿消息
     * @param compensationVo
     * @return
     */
    public boolean sendBalanceRechargeCompensation(MQRechargeCompensationVo compensationVo){
        log.info("用户余额充值补偿消息：" + compensationVo);
        // 延迟时间
        int delayTimeLevel = 1;
        return mqProducer.balanceRechargeCompensation().send(MessageBuilder.withPayload(compensationVo)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }

    /**
     * 订单支付补偿消息
     * @return
     */
    public boolean sendOrderPayCompensation(MQOrderPayRemedyVo mqOrderPayRemedyVo){
        log.info("订单支付补偿消息：" + mqOrderPayRemedyVo);

        // 延迟时间
        int delayTimeLevel = 0;
        return mqProducer.orderPayCompensation().send(MessageBuilder.withPayload(mqOrderPayRemedyVo)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }

    /**
     * 查价差额记录日志消息
     * @return
     */
    public boolean sendOrderDifferenceOutput(DifferenceLogCreateReqVO differenceLogCreateReqVO){
        log.info("查价差额记录日志消息：" + differenceLogCreateReqVO);

        // 延迟时间
        int delayTimeLevel = 0;
        return mqProducer.orderDifferenceOutput().send(MessageBuilder.withPayload(differenceLogCreateReqVO)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }

// TODO 2025-5-19 coreData服务删除，相关代码删除
//    /**
//     * 订单信息上报coreData服务 （coreData 服务删除，相关逻辑查询pgsql服务）
//     * @return
//     */
//    public boolean sendOrderReported(OrderDO orderDO, String type){
//        log.info("订单支付补偿消息：" + orderDO);
//
//        OrderRequestVO orderRequestVO = new OrderRequestVO();
//        orderRequestVO.setType(type);
//        orderRequestVO.setOrderId(orderDO.getId());
//        orderRequestVO.setOrderNo(orderDO.getOrderNo());
//        orderRequestVO.setCreateTime(orderDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
//        orderRequestVO.setTenantId(orderDO.getTenantId()+"");
//
//
//        String mobile = "";
//        if(ObjectUtil.isNotEmpty(orderDO.getUserId()) && type.equals(OrderConstants.ORDER_REPORTED_ADD)) {
//            CacheUserDTO userInfo = cacheService.getUserInfo(orderDO.getUserId());
//            if(ObjectUtil.isEmpty(userInfo) && ObjectUtil.isEmpty(userInfo.getMobile())){
//                MemberUserRespDTO memberUserApiUser = memberUserApi.getUser(orderDO.getUserId());
//                mobile = ObjectUtil.isNotEmpty(memberUserApiUser) ? memberUserApiUser.getMobile() : "";
//            } else {
//                mobile = userInfo.getMobile();
//            }
//        }
//        orderRequestVO.setMobile(mobile);
//
//        if(type.equals(OrderConstants.ORDER_REPORTED_ADD)){
//            // 查询分销商名称
//            TenantRespVO tenantRespVO = tenantApi.getTenantInfo(orderDO.getTenantId()).getData();
//            String tenantName = ObjectUtil.isNotEmpty(tenantRespVO) ? tenantRespVO.getName() : null;
//            orderRequestVO.setTenantName(tenantName);
//        }
//
//        // 延迟时间
//        int delayTimeLevel = 0;
//        return mqProducer.orderReported().send(MessageBuilder.withPayload(orderRequestVO)
//                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
//                .build());
//    }


    /**
     * 分佣上报
     * @return
     */
    public boolean sendRebateReported(OrderDO orderDO){
        log.info("分佣上报消息：" + orderDO);
        RebateReportedVo rebateReportedVo = new RebateReportedVo();
        rebateReportedVo.setTenantId(orderDO.getTenantId());
        rebateReportedVo.setOrderId(orderDO.getId());
        rebateReportedVo.setUserId(orderDO.getUserId());
        // 延迟时间
        int delayTimeLevel = 0;
        return mqProducer.rebateReported().send(MessageBuilder.withPayload(rebateReportedVo)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }

    /**
     * 分享退款
     * @param marketingInputVo
     * @return
     */
    public boolean sendCommissionRebate( MarketingInputVo marketingInputVo){
        log.info("分享退款消息：" + marketingInputVo);

        // 延迟时间
        int delayTimeLevel = 0;
        return mqProducer.commissionRebateOutput().send(MessageBuilder.withPayload(marketingInputVo)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }


    /**
     * 导出公共方法
     * @return
     */
    public boolean kdExport(MQExportVo mqExportVo){
        log.info(mqExportVo.getTenantId()+"导出记录请求参数日志发送消息：" + mqExportVo);
        // 延迟时间
        int delayTimeLevel = 2;
        return mqProducer.kdExportOutput().send(MessageBuilder.withPayload(mqExportVo)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }

    /**
     * 订单超重发送企业微信群通知
     * @param weChatGroupNotifyReq
     * @return
     */
    public boolean orderOverweightNotifyOutput(WeChatGroupNotifyReq weChatGroupNotifyReq){

        log.info("补差价发送sendDifferencePrice：{}",weChatGroupNotifyReq);
        // 延迟时间
        int delayTimeLevel = 0;
        return mqProducer.overweightNotifyOutput().send(MessageBuilder.withPayload(weChatGroupNotifyReq)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }

    /**
     * 总部对账单
     * @param rootBillingFeeVo
     * @return
     */
    public boolean rootOrderBilling(MQOrderBillingFeeVo rootBillingFeeVo){
        // 延迟时间
        /*int delayTimeLevel = 0;
        return mqProducer.orderBilling().send(MessageBuilder.withPayload(rootBillingFeeVo)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());*/

        return sendLargeMessage(rootBillingFeeVo);
    }

    public boolean sendLargeMessage(MQOrderBillingFeeVo rootBillingFeeVo) {
        List<List<OrderDO>> partitions = ListUtil.partition(rootBillingFeeVo.getOrderDOList(), 500);
        boolean success = true;

        for (int i = 0; i < partitions.size(); i++) {
            MQOrderBillingFeeVo partialFeeVo = new MQOrderBillingFeeVo();
            partialFeeVo.setTenantId(rootBillingFeeVo.getTenantId());
            partialFeeVo.setBillingLogId(rootBillingFeeVo.getBillingLogId());
            partialFeeVo.setOrderDOList(partitions.get(i));

            // 构建消息并发送
            Message message = MessageBuilder.withPayload(partialFeeVo)
                    .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, 0)
                    .setHeader("messageId", UUID.randomUUID().toString())
                    .setHeader("totalParts", partitions.size())
                    .build();

            log.info("总部对账单-->数据分发后数据切片发送至MQ，当前切片：{}，当前切片数据量：{}，当前代理商：{}", i, partitions.get(i).size(), rootBillingFeeVo.getTenantId());
            success &= mqProducer.orderBilling().send(message);
        }
        return success;
    }

    /**
     * 有赞回调消息
     * @param kuaiDiCallbackVo
     * @return
     */
    public boolean youZanNotifyOutput(KuaiDiCallbackVo kuaiDiCallbackVo){
        // 延迟时间
        int delayTimeLevel = 0;
        return mqProducer.youZanOutput().send(MessageBuilder.withPayload(kuaiDiCallbackVo)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }



    /**
     * 回调重新执行
     * @param callbackRepeatVo
     * @param delayTimeLevel
     * @return
     */
    public boolean orderCallbackRepeatOutput(CallbackRepeatVo callbackRepeatVo, Integer delayTimeLevel) {
        // 延迟时间
        if(ObjectUtil.isEmpty(delayTimeLevel)){
            delayTimeLevel = 3;
        }
        return mqProducer.orderCallbackRepeatOutput().send(MessageBuilder.withPayload(callbackRepeatVo)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }


    public boolean userMergeOutput(MQUserMergeVo mqUserMergeVo){
        // 延迟时间
        int delayTimeLevel = 0;
        return mqProducer.userMergeOutput().send(MessageBuilder.withPayload(mqUserMergeVo)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }


    public boolean sendMessageOutPut(BatchMessageSendReqDTO batchMessageSendReqDTO){
        // 延迟时间
        int delayTimeLevel = 0;
        return mqProducer.sendMessageOutPut().send(MessageBuilder.withPayload(batchMessageSendReqDTO)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }

    /**
     * 手动推送订单回调信息
     *
     * @param orderCallbackVo 订单回调信息
     * @param delayTimeLevel  延迟级别，默认为2（5秒）
     * @return 发送结果
     */
    public boolean orderCallbackOutput(OrderCallbackVo orderCallbackVo, Integer delayTimeLevel) {
        log.info("手动推送订单回调信息：{}", orderCallbackVo);
        // 如果未指定延迟级别，默认使用2级别（5秒）
        if (ObjectUtil.isEmpty(delayTimeLevel)) {
            delayTimeLevel = 2;
        }
        return mqProducer.orderCallbackOutput().send(MessageBuilder.withPayload(orderCallbackVo)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }

    /**
     * 发送订单同步到PostgreSQL数据库的消息
     *
     * @param orderId 订单ID
     * @param type    操作类型："add"表示新增，"update"表示更新
     * @return 发送结果
     */
    public boolean sendOrderSyncPgsql(String orderId, String type) {
        // 使用 TenantContextHolder 获取当前租户ID
        Long tenantId = com.hnyiti.kuaidi.framework.tenant.core.context.TenantContextHolder.getTenantId();

        log.info("发送订单同步到PostgreSQL消息，订单ID：{}，操作类型：{}，租户ID：{}", orderId, type, tenantId);

        // 如果租户ID为空，则无法发送消息
        if (tenantId == null) {
            log.error("租户ID为空，无法发送订单同步到PostgreSQL消息");
            return false;
        }

        Map<String, Object> payload = new HashMap<>();
        payload.put("orderId", orderId);
        payload.put("type", type);
        payload.put("tenantId", String.valueOf(tenantId));

        // 查询对象信息放到redis里面
        OrderAllVo orderAllVo = orderApi.getSyncPgsqlOrderInfo(orderId, type).getData();
        if (ObjectUtil.isNotEmpty(orderAllVo)) {
            String redisKey = "orderSyncPgsql::" + tenantId + "::" + orderId + "_" + type;
            // 2. List存储 产品id 与 产品最终定价
            redisUtils.set(redisKey, JSONUtil.toJsonStr(orderAllVo), 1, TimeUnit.DAYS);
        }

        // 不设置延迟
        int delayTimeLevel = 0;
        return mqProducer.orderSyncPgsqlOutput().send(MessageBuilder.withPayload(payload)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }


    /**
     * 电话催缴补差费用
     *
     * @param orderId 订单ID
     * @return 发送结果
     */
    public boolean sendCompensationImmediate(String orderId, Long taskId, String taskType, String type) {
        // 使用 TenantContextHolder 获取当前租户ID
        Long tenantId = com.hnyiti.kuaidi.framework.tenant.core.context.TenantContextHolder.getTenantId();

        log.info("发送订单同步到PostgreSQL消息，订单ID：{}，操作类型：{}，租户ID：{}", orderId, taskType, tenantId);

        // 如果租户ID为空，则无法发送消息
        if (tenantId == null) {
            log.error("租户ID为空，无法发送订单同步到PostgreSQL消息");
            return false;
        }

        NotificationMessage notificationMessage = new NotificationMessage();

        notificationMessage.setOrderId(orderId);
        notificationMessage.setTaskId(taskId);
        notificationMessage.setTaskType(taskType);
        if (ObjectUtil.isNotEmpty(type) && "2".equals(type)) {
            notificationMessage.setAction(NotificationMessage.Action.CANCEL);
        } else {
            notificationMessage.setAction(NotificationMessage.Action.SEND);
        }


        // 不设置延迟
        int delayTimeLevel = 0;
        return mqProducer.compensationImmediateOutput().send(MessageBuilder.withPayload(notificationMessage)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
                .build());
    }

}
