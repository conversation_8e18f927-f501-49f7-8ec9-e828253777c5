# 大汉三通语音通知配置
dahan:
  voice:
    # 基本配置
    account: ${DAHAN_VOICE_ACCOUNT:dh8528}
    raw-password: ${DAHAN_VOICE_PASSWORD:your_password_here}

    # 接口地址
    submit-url: ${DAHAN_VOICE_SUBMIT_URL:http://voice.3tong.net/json/voiceSms/SubmitVoc}
    report-url: ${DAHAN_VOICE_REPORT_URL:http://voice.3tong.net/json/voiceSms/GetReport}

    # 超时配置
    connect-timeout: ${DAHAN_VOICE_CONNECT_TIMEOUT:30000}
    read-timeout: ${DAHAN_VOICE_READ_TIMEOUT:60000}

    # 功能开关
    enabled: ${DAHAN_VOICE_ENABLED:true}

    # 语音配置
    default-speaker: ${DAHAN_VOICE_SPEAKER:B0}  # B0=汉小美, B1=汉小宇, B3=汉逍遥, B4=汉丫丫
    default-play-times: ${DAHAN_VOICE_PLAY_TIMES:1}
    max-retry-times: ${DAHAN_VOICE_MAX_RETRY:3}

# 日志配置
logging:
  level:
    com.hnyiti.kuaidi.module.pgsql.service.notification.voice: DEBUG

# 多阶段通知中的语音配置
notification:
  compensation:
    # 语音通知模板
    templates:
      phone-call:
        compensation: "您好，您的快递订单{orderNo}需要补差{amount}元，请及时处理。如需帮助请联系客服。"
        verify-code: "您的验证码是{code}，请在5分钟内输入。"

    # 语音通知费用配置
    cost:
      phone-call: 0.15  # 每次通话费用（元）
