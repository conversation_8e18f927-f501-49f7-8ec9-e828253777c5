package com.hnyiti.kuaidi.module.system.api.tenant;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.hnyiti.kuaidi.framework.common.exception.ErrorCode;
import com.hnyiti.kuaidi.framework.common.exception.ServiceException;
import com.hnyiti.kuaidi.framework.common.pojo.CommonResult;
import com.hnyiti.kuaidi.framework.tenant.core.context.TenantContextHolder;
import com.hnyiti.kuaidi.module.member.api.balance.BalanceApiImpl;
import com.hnyiti.kuaidi.module.system.api.tenant.dto.TenantDataSourceConfigRespDTO;
import com.hnyiti.kuaidi.module.system.api.tenant.dto.TenantQueryVO;
import com.hnyiti.kuaidi.module.system.api.tenant.dto.TenantRespVO;
import com.hnyiti.kuaidi.module.system.convert.db.DataSourceConfigConvert;
import com.hnyiti.kuaidi.module.system.convert.tenant.TenantConvert;
import com.hnyiti.kuaidi.module.system.dal.dataobject.tenant.TenantAmountDO;
import com.hnyiti.kuaidi.module.system.dal.dataobject.tenant.TenantAmountLogDO;
import com.hnyiti.kuaidi.module.system.dal.dataobject.tenant.TenantDO;
import com.hnyiti.kuaidi.module.system.service.db.DataSourceConfigService;
import com.hnyiti.kuaidi.module.system.service.sms.SmsLogService;
import com.hnyiti.kuaidi.module.system.service.tenant.TenantAmountService;
import com.hnyiti.kuaidi.module.system.service.tenant.TenantService;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static com.hnyiti.kuaidi.framework.common.pojo.CommonResult.success;

@RestController
@Validated
public class TenantApiImpl implements TenantApi {

    @Resource
    private TenantService tenantService;

    @Resource
    private BalanceApiImpl balanceApi;

    @Resource
    private TenantAmountService tenantAmountService;

    @Resource
    private SmsLogService smsLogService;

     private DataSourceConfigService dataSourceConfigService;

    @Override
    public CommonResult<List<Long>> getTenantIdList() {
        return success(tenantService.getTenantIdList());
    }

    @Override
    public CommonResult<Boolean> validTenant(Long id) {
        tenantService.validTenant(id);
        return success(true);
    }

    @Override
    public CommonResult<TenantRespVO> getTenantInfo(Long id) {
        TenantDO tenant = tenantService.getTenant(id);

        // 查询余额
        TenantAmountDO amountDo = tenantAmountService.getTenantAmountByTenantId(id);
        TenantRespVO respVO = TenantConvert.INSTANCE.convert(tenant);

        if (amountDo != null) {
            respVO.setBalanceAmount(amountDo.getBalanceAmount());
            respVO.setMarginAmount(amountDo.getMarginAmount());
        }

        return success(respVO);
    }

    @Override
    public CommonResult<TenantRespVO> getTenantByDomain(String domain) {
        TenantDO tenant = tenantService.getTenantByDomain(domain);
        if (tenant == null) {
            return success(null);
        }

        // 查询余额
        TenantAmountDO amountDo = tenantAmountService.getTenantAmountByTenantId(tenant.getId());
        TenantRespVO respVO = TenantConvert.INSTANCE.convert(tenant);

        if (amountDo != null) {
            respVO.setBalanceAmount(amountDo.getBalanceAmount());
            respVO.setMarginAmount(amountDo.getMarginAmount());
        }

        return success(respVO);
    }

    /**
     * 代理商扣款
     *
     * @param orderId
     * @param tenantAmount
     * @param scene
     * @param mbrId
     * @param remark
     * @return
     */
    @Override
    @GlobalTransactional
    public CommonResult deductTenantAmount(String orderId, BigDecimal tenantAmount, String scene, Long mbrId, String remark) {
        // 加盟商进行扣费
        boolean success = tenantService.deductBalance(TenantContextHolder.getTenantId(), tenantAmount, Long.valueOf(orderId), scene, remark);
        if (!success) {
            throw new ServiceException(new ErrorCode(500, "加盟商余额不够"));
        }

        return CommonResult.success(success);
    }

    @Override
    @DSTransactional
    @GlobalTransactional
    public CommonResult<Long> deduct(Long mbrId, BigDecimal mbrAmount, BigDecimal tenantAmount, String scene, String remark) {
        Long tenantId = TenantContextHolder.getTenantId();

        if (ObjectUtil.isNotEmpty(tenantAmount)) {

            // 加盟商进行扣费
            boolean success = tenantService.deductBalance(tenantId, tenantAmount,null, scene, remark);

            if (!success) {
//                throw new ServiceException(500, "加盟商余额不够");
//                return CommonResult.error(20159, "加盟商余额不够");
                throw new ServiceException(new ErrorCode(500, "加盟商余额不够"));
            }
        }

        // 用户扣费
        return balanceApi.deductAmount(mbrId, mbrAmount, remark, scene, null);
    }

    @Override
    public CommonResult rechargeTenantAmount(String type, Integer amount, Long payOrderId) {
        TenantAmountLogDO logItem = new TenantAmountLogDO();
        logItem.setType(type);
        logItem.setChangeAmount(new BigDecimal(amount).divide(new BigDecimal(100)));
        logItem.setTenantId(TenantContextHolder.getTenantId());
        logItem.setOrderId(payOrderId);

        if ("balance".equals(type)) {
            logItem.setType("mapper/balance");
        }

        tenantAmountService.updateRechargePayOrder(logItem);

        return CommonResult.success(null);
    }

    /**
     * kuaidi服务调用system，新建的SysLogApi加载bean失败（未解决），所以先将方法放置在此类中
     * @param orderId
     * @return
     */
    @Override
    public CommonResult updateSmsLogSendStatusByOrderId(String orderId) {
        smsLogService.updateSmsLogSendStatusByOrderId(orderId);
        return CommonResult.success(null);
    }

    @Override
    @GlobalTransactional
    @DSTransactional
    public CommonResult deductUserAmount(Long userId, String orderId, Long batchOrderId, BigDecimal payAmount, String type, String remark) {
        balanceApi.deductUserAmount(userId, orderId, batchOrderId, payAmount, type, remark);
        return CommonResult.success(null);
    }

    @Override
    @GlobalTransactional
    @DSTransactional
    public CommonResult refundUserAmount(Long userId, String orderId, BigDecimal payAmount, String type, String remark) {
        balanceApi.refundUserAmount(userId, orderId, payAmount, type, remark);
        return CommonResult.success(null);
    }

    @Override
    public TenantDataSourceConfigRespDTO getTenantDataSourceConfig(Long tenantId) {
        // 获得租户信息
        TenantDO tenant = tenantService.getTenant(tenantId);
        if (tenant == null) {
            return null;
        }

        if (null == tenant.getDataSourceConfigId()) {
            return DataSourceConfigConvert.INSTANCE.convert03(dataSourceConfigService.getDataSourceConfig(0L));
//            return TenantConvert.INSTANCE.convert(fileApi.getDataSourceConfig(0L));
        }

        return DataSourceConfigConvert.INSTANCE.convert03(dataSourceConfigService.getDataSourceConfig(tenant.getDataSourceConfigId()));
    }

    @Override
    public CommonResult<List<TenantDataSourceConfigRespDTO>> getTenantDataSourceList() {
        List<TenantDataSourceConfigRespDTO> convert = DataSourceConfigConvert.INSTANCE.convertList03(dataSourceConfigService.getDataSourceConfigList());

        return CommonResult.success(convert);
    }


    @Override
    public CommonResult<List<TenantRespVO>> getTenantList() {
        TenantQueryVO reqVO = new TenantQueryVO();
        List<TenantDO> list = tenantService.getTenantList2(reqVO);
        return success(TenantConvert.INSTANCE.convertList(list));
    }
}
