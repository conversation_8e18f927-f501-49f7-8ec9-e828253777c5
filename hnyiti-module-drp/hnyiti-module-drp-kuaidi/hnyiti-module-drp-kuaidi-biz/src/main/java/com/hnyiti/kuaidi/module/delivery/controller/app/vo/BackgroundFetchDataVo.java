package com.hnyiti.kuaidi.module.delivery.controller.app.vo;

import com.hnyiti.kuaidi.module.system.api.notice.NoticeVO;
import com.hnyiti.kuaidi.module.system.api.window.SystemWindowVO;
import com.hnyiti.kuaidi.module.wxmini.dal.dataobject.banner.BannerDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 小程序背景数据VO
 *
 * 用于封装小程序初始化时需要获取的各类配置数据和业务开关状态，包括但不限于：
 * - 菜单配置
 * - 广告栏图片
 * - 横幅广告
 * - 个人中心配置
 * - 公众号配置
 * - 系统通知公告
 * - 会员模块配置
 * - 客服信息
 * - 各类业务功能开关（支付、优惠券、提现等）
 *
 * 该VO对象由MiniV1ApiController的getBackgroundFetchData接口返回，
 * 提供小程序端所需的基础配置数据，支持小程序端的初始化渲染和功能开关控制
 */
@Schema(description = "小程序背景数据 VO")
@Data
public class BackgroundFetchDataVo {
    @Schema(description = "菜单配置列表 - 小程序中显示的菜单项配置信息")
    private List<MiniMenuConfigVO> menuConfigList;

    @Schema(description = "主图/广告栏 - 小程序首页轮播图或广告图片列表")
    private List<BannerDO> banners;

    @Schema(description = "横幅广告 - 活动分享或营销推广用的横幅广告列表")
    private List<BannerDO> shareRespVO;

    @Schema(description = "个人中心菜单按钮开关 - 控制个人中心页面中各功能按钮的显示状态")
    private MiniPersonalCenterConfigVo centerConfig;

    @Schema(description = "个人中心按钮开关列表 - 基于用户参数配置的个人中心功能按钮列表")
    private List<MiniPersonalCenterConfigVo> centerConfigVoList;

    @Schema(description = "公众号配置信息 - 包含小程序关联的公众号相关配置")
    private MiniPublicConfigVO publicConfig;

    @Schema(description = "系统通知 - 获取通知公告(系统通知)信息")
    private NoticeVO systemNotify;

    @Schema(description = "系统公告 - 系统级别的公告信息，用于在小程序中展示")
    private SystemWindowVO systemBulletin;

    @Schema(description = "会员模块显示开关 - 控制小程序中是否显示会员相关的功能模块")
    private Boolean showVip;

    @Schema(description = "小程序会员配置信息 - 会员相关的配置参数，包括会员等级、权益等信息")
    private MiniMemberConfigVo memberConfigVo;

    @Schema(description = "客服信息 - 小程序客服配置，包括客服入口、联系方式等")
    private MiniCustomServiceVo customerInfo;

    @Schema(description = "公众号状态 - 小程序对应的公众号是否开启")
    private Boolean miniProgramsOpen;

    @Schema(description = "登录类型 - 小程序支持的登录方式配置")
    private MiniFunctionConfigVo miniFunctionConfigVo;

    @Schema(description = "营销类型 - 代理分销模式：1-推新返佣、2-推三反一，默认为推新返佣模式")
    private String mktType = "1";

    @Schema(description = "分销开关 - 控制小程序中是否启用分销功能，0：开启  1：关闭")
    private int mktSwitch = 0;

    @Schema(description = "优惠券开关 - 控制小程序中是否启用优惠券功能，0：开启  1：关闭")
    private String couponSwitch = "1";

    @Schema(description = "新人优惠券开关 - 控制是否向新用户展示优惠券弹窗，0：开启  1：关闭")
    private String couponNewcomerSwitch = "1";

    @Schema(description = "优惠券调优开关 - 控制是否自动选择最优惠券功能，0：开启  1：关闭")
    private String couponOptimalSwitch = "1";

    @Schema(description = "余额支付开关 - 控制是否允许使用账户余额支付，true：开启  false：关闭")
    private Boolean isOpenBalancePay;

    @Schema(description = "取消原因开关 - 控制取消订单时是否需要填写取消原因，0：开启  1：关闭")
    private String cancelReasonSwitch = "1";

    @Schema(description = "余额充值开关 - 控制是否允许用户进行余额充值，0：开启  1：关闭")
    private String balanceRechargeSwitch = "1";

    @Schema(description = "余额提现开关 - 控制是否允许用户将余额提现，0：开启  1：关闭")
    private String balanceWithdrawSwitch = "1";

    @Schema(description = "收益提现开关 - 控制是否允许用户将佣金收益提现，0：开启  1：关闭")
    private String profitWithdrawSwitch = "1";

    /**
     * 会员升级类型；0：升级  1：升级到指定等级
     */
    private Boolean memberVipUpType;

    /**
     * 分销商id
     */
    private Long tenantId;

}
