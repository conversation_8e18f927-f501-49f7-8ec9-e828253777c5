package com.hnyiti.kuaidi.module.pgsql.service.notification.voice.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 大汉三通语音响应模型
 *
 * <AUTHOR>
 */
@Data
public class DahanVoiceResponse {

    /**
     * 总的提交结果状态码
     */
    private String result;

    /**
     * 提交结果描述
     */
    private String desc;

    /**
     * 具体消息的响应数据
     */
    private List<Map<String, Object>> data;

    /**
     * 判断是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return "DH:0000".equals(result);
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getErrorDesc() {
        if (isSuccess()) {
            return null;
        }
        return desc != null ? desc : "未知错误";
    }
}
