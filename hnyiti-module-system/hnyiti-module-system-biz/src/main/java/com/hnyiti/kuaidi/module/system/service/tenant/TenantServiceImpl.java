package com.hnyiti.kuaidi.module.system.service.tenant;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.tx.LocalTxUtil;
import com.hnyiti.kuaidi.framework.common.enums.CommonStatusEnum;
import com.hnyiti.kuaidi.framework.common.pojo.PageResult;
import com.hnyiti.kuaidi.framework.common.util.collection.CollectionUtils;
import com.hnyiti.kuaidi.framework.common.util.date.DateUtils;
import com.hnyiti.kuaidi.framework.common.util.redeem.SnowCouponId;
import com.hnyiti.kuaidi.framework.security.core.util.SecurityFrameworkUtils;
import com.hnyiti.kuaidi.framework.tenant.config.TenantProperties;
import com.hnyiti.kuaidi.framework.tenant.core.context.TenantContextHolder;
import com.hnyiti.kuaidi.framework.tenant.core.util.TenantUtils;
import com.hnyiti.kuaidi.module.api.channelExpress.ChannelExpressApi;
import com.hnyiti.kuaidi.module.api.channelExpress.vo.ChannelExpressCreateReqVO;
import com.hnyiti.kuaidi.module.api.channelExpress.vo.ChannelExpressRespVO;
import com.hnyiti.kuaidi.module.member.api.levels.MemberLevelsApi;
import com.hnyiti.kuaidi.module.member.api.levels.vo.LevelsCreateReqVO;
import com.hnyiti.kuaidi.module.member.api.levels.vo.LevelsRespVO;
import com.hnyiti.kuaidi.module.pay.controller.admin.app.vo.PayAppCreateReqVO;
import com.hnyiti.kuaidi.module.pay.controller.admin.channel.vo.PayChannelCreateReqVO;
import com.hnyiti.kuaidi.module.pay.controller.admin.merchant.vo.MerchantCreateReqVO;
import com.hnyiti.kuaidi.module.pay.dal.dataobject.app.PayAppDO;
import com.hnyiti.kuaidi.module.pay.service.app.PayAppService;
import com.hnyiti.kuaidi.module.pay.service.channel.PayChannelService;
import com.hnyiti.kuaidi.module.pay.service.merchant.MerchantService;
import com.hnyiti.kuaidi.module.system.api.tenant.dto.*;
import com.hnyiti.kuaidi.module.system.controller.admin.notice.vo.NoticeCreateReqVO;
import com.hnyiti.kuaidi.module.system.controller.admin.permission.vo.role.RoleCreateReqVO;
import com.hnyiti.kuaidi.module.system.convert.tenant.TenantConvert;
import com.hnyiti.kuaidi.module.system.dal.dataobject.notice.NoticeDO;
import com.hnyiti.kuaidi.module.system.dal.dataobject.permission.MenuDO;
import com.hnyiti.kuaidi.module.system.dal.dataobject.permission.RoleDO;
import com.hnyiti.kuaidi.module.system.dal.dataobject.tenant.TenantAmountDO;
import com.hnyiti.kuaidi.module.system.dal.dataobject.tenant.TenantDO;
import com.hnyiti.kuaidi.module.system.dal.dataobject.tenant.TenantPackageDO;
import com.hnyiti.kuaidi.module.system.dal.mysql.tenant.TenantMapper;
import com.hnyiti.kuaidi.module.system.enums.ConfigConstants;
import com.hnyiti.kuaidi.module.system.enums.SuperAdminConstants;
import com.hnyiti.kuaidi.module.system.enums.permission.RoleCodeEnum;
import com.hnyiti.kuaidi.module.system.enums.permission.RoleTypeEnum;
import com.hnyiti.kuaidi.module.system.service.notice.NoticeService;
import com.hnyiti.kuaidi.module.system.service.permission.MenuService;
import com.hnyiti.kuaidi.module.system.service.permission.PermissionService;
import com.hnyiti.kuaidi.module.system.service.permission.RoleService;
import com.hnyiti.kuaidi.module.system.service.tenant.handler.TenantInfoHandler;
import com.hnyiti.kuaidi.module.system.service.tenant.handler.TenantMenuHandler;
import com.hnyiti.kuaidi.module.system.service.user.AdminUserService;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.hnyiti.kuaidi.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hnyiti.kuaidi.module.system.enums.ErrorCodeConstants.*;
import static java.util.Collections.singleton;

/**
 * 租户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TenantServiceImpl implements TenantService {

    @SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
    @Autowired(required = false) // 由于 hnyiti.tenant.enable 配置项，可以关闭多租户的功能，所以这里只能不强制注入
    private TenantProperties tenantProperties;

    @Resource
    private TenantMapper tenantMapper;

    @Resource
    private TenantPackageService tenantPackageService;

    @Lazy
    @Resource
    private TenantAmountService tenantAmountService;

    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private AdminUserService userService;

    @Resource
    private RoleService roleService;

    @Resource
    private MenuService menuService;

    @Resource
    private PermissionService permissionService;
    @Lazy
    @Resource
    private PayAppService payAppService;
    @Lazy
    @Resource
    private PayChannelService payChannelService;
    @Lazy
    @Resource
    private MerchantService merchantService;
    @Lazy
    @Autowired
    private RedisTemplate redisTemplate;
    @Lazy
    @Resource
    private ChannelExpressApi channelExpressApi;
    @Lazy
    @Resource
    private MemberLevelsApi memberLevelsApi;
    @Lazy
    @Resource
    private NoticeService noticeService;


    @Override
    public List<Long> getTenantIdList() {
        List<TenantDO> tenants = tenantMapper.selectList();
        return CollectionUtils.convertList(tenants, TenantDO::getId);
    }

    @Override
    public void validTenant(Long id) {
        TenantDO tenant = getTenant(id);
        if (tenant == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        if (tenant.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(TENANT_DISABLE, tenant.getName());
        }
        if (DateUtils.isExpired(tenant.getExpireTime())) {
            throw exception(TENANT_EXPIRE, tenant.getName());
        }
    }

    @Override
    public boolean deductBalance(Long tenantId, BigDecimal tenantAmount, Long orderId, String scene, String remark) {
        return tenantAmountService.deductBalance(tenantId, tenantAmount, orderId, scene, remark) > 0;
    }

    @Override
    public List<TenantDO> getTenantList2(TenantQueryVO reqVO) {
        return tenantMapper.selectList();
    }

    @Override
    @DSTransactional
    public Long createTenant(TenantCreateReqVO createReqVO) {
        createReqVO.setUsername(createReqVO.getContactMobile());

        // 校验租户名称是否重复
        validTenantNameDuplicate(createReqVO.getName(), null);
        // 20240506注释，不用代理权限表，改用角色表数据（查询角色表code为group的数据）。
        // 校验权限被禁用
        TenantPackageDO tenantPackage = tenantPackageService.validTenantPackage(createReqVO.getPackageId());

        // 创建租户
        TenantDO tenant = TenantConvert.INSTANCE.convert(createReqVO);
        // 给租户创建授权码
        tenant.setAuthorizeCode(SnowCouponId.idToCode());
        tenantMapper.insert(tenant);

        TenantUtils.execute(tenant.getId(), () -> {
            // 创建角色
            Long roleId = createRole(tenantPackage);
            // 查询集团角色
//            RoleDO roleDO = roleService.getRoleListByCode(SuperAdminConstants.SYS_ROLE_CODE_GROUP);
//            Long roleId = roleDO.getId();
            // 创建用户，并分配角色
            Long userId = createUser(roleId, createReqVO);
            // 修改租户的管理员
            tenantMapper.updateById(new TenantDO().setId(tenant.getId()).setContactUserId(userId));
            // 生成钱包信息
            tenantAmountService.initialize(tenant.getId());
            // 发送刷新消息.
//            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
//                @Override
//                public void afterCommit() {
//                    // redis 发送过期消息
//                    redisTemplate.opsForValue().set("refresh_tenantBean", "", 3, TimeUnit.SECONDS);
//                    // permissionProducer.sendRoleMenuRefreshMessage();
//                }
//            });
            try {
                LocalTxUtil.commit();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            // 发送刷新消息.
            redisTemplate.opsForValue().set("refresh_tenantBean", "", 3, TimeUnit.SECONDS);
            TenantUtils.executeIgnore(() -> {
                // 预制住户相关数据
                initializeData(tenant);
            });

        });
        return tenant.getId();
    }

    /**
     * 预制分销商相关数据
     */
    private void initializeData(TenantDO tenant) {
        Long tenantId = tenant.getId();

        /******************预制快递信息**********************/
        // 查询快递列表
        List<ChannelExpressRespVO> channelExpressDOS = channelExpressApi.getTenantExpress(1L).getData();
        List<ChannelExpressCreateReqVO> batch = new LinkedList<>();
        for (ChannelExpressRespVO bean : channelExpressDOS) {
            ChannelExpressCreateReqVO channelExpressCreateReqVO = new ChannelExpressCreateReqVO();
            BeanUtil.copyProperties(bean, channelExpressCreateReqVO);
            channelExpressCreateReqVO.setTenantId(tenantId);
            channelExpressCreateReqVO.setId(null);
            batch.add(channelExpressCreateReqVO);
        }
        if (ObjectUtil.isNotEmpty(batch)) {
            TenantUtils.execute(tenantId, () -> {
                channelExpressApi.insertBatch(batch);
            });

        }
        /******************会员等级**********************/
        List<LevelsRespVO> levelsList = memberLevelsApi.getTenantLevels(1L).getData();
        List<LevelsCreateReqVO> levelsBatch = new LinkedList<>();
        for (LevelsRespVO bean : levelsList) {
            LevelsCreateReqVO levelsCreateReqVO = new LevelsCreateReqVO();
            BeanUtil.copyProperties(bean, levelsCreateReqVO);
            levelsCreateReqVO.setTenantId(tenantId);
            levelsBatch.add(levelsCreateReqVO);
        }
        if (ObjectUtil.isNotEmpty(levelsBatch)) {
            TenantUtils.execute(tenantId, () -> {
                memberLevelsApi.insertBatch(levelsBatch);
            });
        }

        /******************预制支付相关表信息**********************/
        // 生成相关配载信息（pay_app、pay_merchant、pay_channel生成一条适配数据）
        MerchantCreateReqVO createMerchantReqVO = new MerchantCreateReqVO();
        createMerchantReqVO.setNo("wx");
        createMerchantReqVO.setName("微信支付");
        createMerchantReqVO.setShortName("微信支付");
        createMerchantReqVO.setStatus(Byte.valueOf("0"));
        createMerchantReqVO.setTenantId(tenantId);
        Long merchantId = merchantService.createMerchant(createMerchantReqVO);

        PayAppDO payAppDO = payAppService.getParentApp(1L);


        PayAppCreateReqVO createPayAppReqVO = new PayAppCreateReqVO();
        createPayAppReqVO.setMerchantId(merchantId);
        createPayAppReqVO.setStatus(0);
        createPayAppReqVO.setName(tenant.getName());
        createPayAppReqVO.setPayNotifyUrl(ObjectUtil.isNotEmpty(payAppDO) ? payAppDO.getPayNotifyUrl() : "");
        createPayAppReqVO.setRefundNotifyUrl(ObjectUtil.isNotEmpty(payAppDO) ? payAppDO.getRefundNotifyUrl() : "");
        createPayAppReqVO.setTenantId(tenantId);
        Long appId = payAppService.createApp(createPayAppReqVO);

        PayChannelCreateReqVO createChannelReqVO = new PayChannelCreateReqVO();
        createChannelReqVO.setCode("wx_lite");
        createChannelReqVO.setStatus(0);
        createChannelReqVO.setFeeRate(Double.valueOf(6));
        createChannelReqVO.setAppId(appId);
        createChannelReqVO.setTenantId(tenantId);
        payChannelService.createChannel(createChannelReqVO);

        PayChannelCreateReqVO createChannelReqVO2 = new PayChannelCreateReqVO();
        createChannelReqVO2.setCode("wx_native");
        createChannelReqVO2.setStatus(0);
        createChannelReqVO2.setFeeRate(Double.valueOf(6));
        createChannelReqVO2.setAppId(appId);
        createChannelReqVO2.setTenantId(tenantId);

        payChannelService.createChannel(createChannelReqVO2);

        /******************预制文章中心内容**********************/
//        从龙行惠递中获取数据
        TenantDO tenantDO = tenantMapper.selectById(1L);
//        隐私政策
        preWrittenArticle(ConfigConstants.MINI_KUAIDI_PRIVACYPOLICY, tenantDO, tenant);
//        用户协议
        preWrittenArticle(ConfigConstants.MINI_KUAIDI_USERAGREEMENT, tenantDO, tenant);
//        系统通知
        preWrittenArticle(ConfigConstants.MINI_KUAIDI_SYSTEMNOTIFY, tenantDO, tenant);
//        帮助文档
        preWrittenArticle(ConfigConstants.MINI_KUAIDI_HELP, tenantDO, tenant);
//        VIP协议
        preWrittenArticle(ConfigConstants.MINI_KUAIDI_VIP,tenantDO,tenant);
//        公告
        preWrittenArticle(ConfigConstants.MINI_KUAIDI_NOTICE,tenantDO,tenant);
//        通知
        preWrittenArticle(ConfigConstants.MINI_KUAIDI_NOTIFY,tenantDO,tenant);
//        订单物品详情提示框
        preWrittenArticle(ConfigConstants.MINI_KUAIDI_MINIPROMPT,tenantDO,tenant);
//        首页公告
        preWrittenArticle(ConfigConstants.MINI_KUAIDI_MININOTICE,tenantDO,tenant);

    }

    private Long createUser(Long roleId, TenantCreateReqVO createReqVO) {
        // 创建用户
        Long userId = userService.createUser(TenantConvert.INSTANCE.convert02(createReqVO));
        // 分配角色
        permissionService.assignUserRole(userId, singleton(roleId));
        return userId;
    }

    private Long createRole(TenantPackageDO tenantPackage) {
        // 创建角色
        RoleCreateReqVO reqVO = new RoleCreateReqVO();
        reqVO.setName(RoleCodeEnum.TENANT_ADMIN.getName()).setCode(RoleCodeEnum.TENANT_ADMIN.getCode())
                .setSort(0).setRemark("系统自动生成");
        Long roleId = roleService.createRole(reqVO, RoleTypeEnum.SYSTEM.getType());
        // 分配权限
        permissionService.assignRoleMenu(roleId, tenantPackage.getMenuIds());
        return roleId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenant(TenantUpdateReqVO updateReqVO) {
        // 校验存在
        TenantDO tenant = validateUpdateTenant(updateReqVO.getId());
        // 校验租户名称是否重复
        validTenantNameDuplicate(updateReqVO.getName(), updateReqVO.getId());
        // 20240508 改动，不需要代理权限，权限直接新增的时已经赋值角色为集团角色。
        // 校验权限被禁用
        TenantPackageDO tenantPackage = tenantPackageService.validTenantPackage(updateReqVO.getPackageId());

        // 更新租户
        TenantDO updateObj = TenantConvert.INSTANCE.convert(updateReqVO);
        tenantMapper.updateById(updateObj);
        // 如果修改了渠道类型，刷新快递渠道缓存
        if(ObjectUtil.isNotEmpty(updateReqVO.getChannelType()) && !updateReqVO.getChannelType().equals(tenant.getChannelType())){
            redisTemplate.opsForValue().set("refresh_tenantBean", "", 3, TimeUnit.SECONDS);
        }

        // 如果权限发生变化，则修改其角色的权限
        if (ObjectUtil.notEqual(tenant.getPackageId(), updateReqVO.getPackageId())) {
            updateTenantRoleMenu(tenant.getId(), tenantPackage.getMenuIds());
        }
    }

    private void validTenantNameDuplicate(String name, Long id) {
        TenantDO tenant = tenantMapper.selectByName(name);
        if (tenant == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同名字的租户
        if (id == null) {
            throw exception(TENANT_NAME_DUPLICATE, name);
        }
        if (!tenant.getId().equals(id)) {
            throw exception(TENANT_NAME_DUPLICATE, name);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenantRoleMenu(Long tenantId, Set<Long> menuIds) {
        TenantUtils.execute(tenantId, () -> {
            // 获得所有角色
            List<RoleDO> roles = roleService.getRoleListByStatus(null);
            roles.forEach(role -> Assert.isTrue(tenantId.equals(role.getTenantId()), "角色({}/{}) 租户不匹配",
                    role.getId(), role.getTenantId(), tenantId)); // 兜底校验
            // 重新分配每个角色的权限
            roles.forEach(role -> {
                // 如果是租户管理员，重新分配其权限为租户权限的权限
                if (Objects.equals(role.getCode(), RoleCodeEnum.TENANT_ADMIN.getCode())) {
                    permissionService.assignRoleMenu(role.getId(), menuIds);
                    log.info("[updateTenantRoleMenu][租户管理员({}/{}) 的权限修改为({})]", role.getId(), role.getTenantId(), menuIds);
                    return;
                }
                // 如果是其他角色，则去掉超过权限的权限
                Set<Long> roleMenuIds = permissionService.getRoleMenuIds(role.getId());
                roleMenuIds = CollUtil.intersectionDistinct(roleMenuIds, menuIds);
                permissionService.assignRoleMenu(role.getId(), roleMenuIds);
                log.info("[updateTenantRoleMenu][角色({}/{}) 的权限修改为({})]", role.getId(), role.getTenantId(), roleMenuIds);
            });
        });
    }

    @Override
    public void deleteTenant(Long id) {
        // 校验存在
        validateUpdateTenant(id);
        // 删除
        tenantMapper.deleteById(id);
    }

    private TenantDO validateUpdateTenant(Long id) {
        TenantDO tenant = tenantMapper.selectById(id);
        if (tenant == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        // 内置租户，不允许删除
        /*if (isSystemTenant(tenant)) {
            throw exception(TENANT_CAN_NOT_UPDATE_SYSTEM);
        }*/
        return tenant;
    }

    @Override
    public TenantDO getTenant(Long id) {
        return tenantMapper.selectById(id);
    }

    @Override
    public PageResult<TenantRespVO> getTenantPage(TenantPageVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Long tenantId = TenantContextHolder.getTenantId();
        // 如果不是root(root账号id=9)账号登录只能查看当前代理商的列表
        if(SuperAdminConstants.SUPER_USER_ID != userId){
            pageReqVO.setId(tenantId+"");
        }


        PageResult<TenantDO> pageResult = tenantMapper.selectPage(pageReqVO);
        PageResult<TenantRespVO> voPageResult = TenantConvert.INSTANCE.convertPage(pageResult);

        List<TenantRespVO> list = voPageResult.getList();

        for (TenantRespVO tenantRespVO : list) {
            Long id = tenantRespVO.getId();
            TenantAmountDO tenantAmount = tenantAmountService.getTenantAmount(id);

            tenantRespVO.setBalanceAmount(BigDecimal.ZERO);
            tenantRespVO.setMarginAmount(BigDecimal.ZERO);

            if (null != tenantAmount) {
                tenantRespVO.setBalanceAmount(tenantAmount.getBalanceAmount());
                tenantRespVO.setMarginAmount(tenantAmount.getMarginAmount());
            }
        }

        return voPageResult;
    }

    @Override
    public List<TenantDO> getTenantList(TenantQueryVO reqVO) {
        //超级管理员可以查询所有代理商，代理商自己只能查询自己
        if (SecurityFrameworkUtils.getSuperAdmin()) {
            return tenantMapper.selectList(reqVO);
        }
        TenantDO obj = tenantMapper.selectById(TenantContextHolder.getTenantId());
        return new ArrayList<TenantDO>() {{
            add(obj);
        }};
    }


    @Override
    public List<TenantDO> getTenantAllList(TenantQueryVO reqVO) {
        return tenantMapper.selectList(reqVO);
    }

    @Override
    public TenantDO getTenantByName(String name) {
        return tenantMapper.selectByName(name);
    }

    @Override
    public TenantDO getTenantByDomain(String domain) {
        return tenantMapper.selectByDomain(domain);
    }

    @Override
    public Long getTenantCountByPackageId(Long packageId) {
        return tenantMapper.selectCountByPackageId(packageId);
    }

    @Override
    public List<TenantDO> getTenantListByPackageId(Long packageId) {
        return tenantMapper.selectListByPackageId(packageId);
    }

    @Override
    public void handleTenantInfo(TenantInfoHandler handler) {
        // 如果禁用，则不执行逻辑
        if (isTenantDisable()) {
            return;
        }
        // 获得租户
        TenantDO tenant = getTenant(TenantContextHolder.getRequiredTenantId());
        // 执行处理器
        handler.handle(tenant);
    }

    @Override
    public void handleTenantMenu(TenantMenuHandler handler) {
        // 如果禁用，则不执行逻辑
        if (isTenantDisable()) {
            return;
        }
        // 获得租户，然后获得菜单
        TenantDO tenant = getTenant(TenantContextHolder.getRequiredTenantId());
        Set<Long> menuIds;
        if (isSystemTenant(tenant)) { // 系统租户，菜单是全量的
            menuIds = CollectionUtils.convertSet(menuService.getMenuList(), MenuDO::getId);
        } else {
            menuIds = tenantPackageService.getTenantPackage(tenant.getPackageId()).getMenuIds();
        }
        // 执行处理器
        handler.handle(menuIds);
    }

    private static boolean isSystemTenant(TenantDO tenant) {
        return Objects.equals(tenant.getPackageId(), TenantDO.PACKAGE_ID_SYSTEM);
    }

    private boolean isTenantDisable() {
        return tenantProperties == null || Boolean.FALSE.equals(tenantProperties.getEnable());
    }

    /**
     *
     * @return
     */
    private boolean preWrittenArticle(String type, TenantDO tenantDO, TenantDO newTenant) {
        try {
            List<NoticeDO> notice = noticeService.getNoticeByType(type, tenantDO.getId());
            NoticeCreateReqVO createReqVO = new NoticeCreateReqVO();
            if (CollUtil.isNotEmpty(notice)) {
                for (NoticeDO noticeDO : notice) {
                    if (noticeDO.getStatus().equals(CommonStatusEnum.ENABLE.getStatus().toString())) {
                        createReqVO.setTitle(noticeDO.getTitle());
                        createReqVO.setStatus(CommonStatusEnum.ENABLE.getStatus().toString());
                        createReqVO.setType(noticeDO.getType());
//                    对文章内容进行处理
                        Document doc = Jsoup.parse(noticeDO.getContent());
                        // 查找所有文本节点并进行替换
                        Elements elements = doc.body().getAllElements();
                        for (Element element : elements) {
                            // 获取所有子节点
                            for (Node node : element.childNodes()) {
                                if (node instanceof TextNode) {
                                    // 替换文本节点内容
                                    TextNode textNode = (TextNode) node;
                                    String text = textNode.text();
                                    if (text.contains(tenantDO.getName())) {
                                        textNode.text(text.replace(tenantDO.getName(), newTenant.getName()));
                                    }
                                }
                            }
                        }
                        createReqVO.setContent(doc.outerHtml());
                        createReqVO.setTenantId(newTenant.getId());
                        noticeService.createNotice(createReqVO);
                    }
                }
            }
        }catch (Exception e) {
            log.error("新增代理商预制文章{}失败{}",type,e.getMessage());
            return false;
        }
        log.info("新增代理商预制文章{}成功",type);
        return true;
    }

}
