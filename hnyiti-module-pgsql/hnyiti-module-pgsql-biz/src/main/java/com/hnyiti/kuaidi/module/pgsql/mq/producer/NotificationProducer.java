package com.hnyiti.kuaidi.module.pgsql.mq.producer;

import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.mq.message.NotificationMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.binding.BinderAwareChannelResolver;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * 通知消息生产者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@EnableBinding
public class NotificationProducer {

    @Autowired
    private BinderAwareChannelResolver resolver;

    // 消息主题常量
    private static final String IMMEDIATE_NOTIFICATION_TOPIC = "compensation-immediate-notify";
    private static final String DELAYED_NOTIFICATION_TOPIC = "compensation-delayed-notify";
    private static final String RESCHEDULE_NOTIFICATION_TOPIC = "compensation-reschedule-notify";

    /**
     * 发送立即通知消息
     *
     * @param task 通知任务
     * @return 是否发送成功
     */
    public boolean sendImmediateNotification(NotificationTask task) {
        log.info("[sendImmediateNotification] 发送立即通知消息，任务ID: {}, 类型: {}", 
                task.getId(), task.getTaskType());

        try {
            NotificationMessage message = NotificationMessage.builder()
                    .taskId(task.getId())
                    .orderId(task.getOrderId())
                    .tenantId(task.getTenantId())
                    .taskType(task.getTaskType())
                    .action(NotificationMessage.Action.SEND)
                    .taskData(task.getTaskData())
                    .retryCount(task.getRetryCount())
                    .build();

            MessageChannel channel = resolver.resolveDestination(IMMEDIATE_NOTIFICATION_TOPIC);
            boolean result = channel.send(MessageBuilder.withPayload(message).build());

            log.info("[sendImmediateNotification] 立即通知消息发送{}，任务ID: {}", 
                    result ? "成功" : "失败", task.getId());

            return result;

        } catch (Exception e) {
            log.error("[sendImmediateNotification] 发送立即通知消息异常，任务ID: {}", task.getId(), e);
            return false;
        }
    }

    /**
     * 发送延时通知消息
     *
     * @param task 通知任务
     * @param delayMillis 延时时间（毫秒）
     * @return 是否发送成功
     */
    public boolean sendDelayedNotification(NotificationTask task, long delayMillis) {
        log.info("[sendDelayedNotification] 发送延时通知消息，任务ID: {}, 类型: {}, 延时: {}ms", 
                task.getId(), task.getTaskType(), delayMillis);

        try {
            NotificationMessage message = NotificationMessage.builder()
                    .taskId(task.getId())
                    .orderId(task.getOrderId())
                    .tenantId(task.getTenantId())
                    .taskType(task.getTaskType())
                    .action(NotificationMessage.Action.SEND)
                    .delayTime(delayMillis)
                    .taskData(task.getTaskData())
                    .retryCount(task.getRetryCount())
                    .build();

            MessageChannel channel = resolver.resolveDestination(DELAYED_NOTIFICATION_TOPIC);
            
            // 设置延时级别（RocketMQ延时消息）
            int delayLevel = calculateDelayLevel(delayMillis);
            
            boolean result = channel.send(MessageBuilder.withPayload(message)
                    .setHeader("DELAY", delayLevel)
                    .build());

            log.info("[sendDelayedNotification] 延时通知消息发送{}，任务ID: {}, 延时级别: {}", 
                    result ? "成功" : "失败", task.getId(), delayLevel);

            return result;

        } catch (Exception e) {
            log.error("[sendDelayedNotification] 发送延时通知消息异常，任务ID: {}", task.getId(), e);
            return false;
        }
    }

    /**
     * 发送重新调度消息
     *
     * @param task 通知任务
     * @param delayMillis 延时时间（毫秒）
     * @return 是否发送成功
     */
    public boolean sendRescheduleNotification(NotificationTask task, long delayMillis) {
        log.info("[sendRescheduleNotification] 发送重新调度消息，任务ID: {}, 延时: {}ms", 
                task.getId(), delayMillis);

        try {
            NotificationMessage message = NotificationMessage.builder()
                    .taskId(task.getId())
                    .orderId(task.getOrderId())
                    .tenantId(task.getTenantId())
                    .taskType(task.getTaskType())
                    .action(NotificationMessage.Action.RESCHEDULE)
                    .delayTime(delayMillis)
                    .taskData(task.getTaskData())
                    .retryCount(task.getRetryCount())
                    .build();

            MessageChannel channel = resolver.resolveDestination(RESCHEDULE_NOTIFICATION_TOPIC);
            
            int delayLevel = calculateDelayLevel(delayMillis);
            
            boolean result = channel.send(MessageBuilder.withPayload(message)
                    .setHeader("DELAY", delayLevel)
                    .build());

            log.info("[sendRescheduleNotification] 重新调度消息发送{}，任务ID: {}", 
                    result ? "成功" : "失败", task.getId());

            return result;

        } catch (Exception e) {
            log.error("[sendRescheduleNotification] 发送重新调度消息异常，任务ID: {}", task.getId(), e);
            return false;
        }
    }

    /**
     * 发送取消任务消息
     *
     * @param orderId 订单ID
     * @param tenantId 租户ID
     * @return 是否发送成功
     */
    public boolean sendCancelNotification(String orderId, Long tenantId) {
        log.info("[sendCancelNotification] 发送取消任务消息，订单ID: {}", orderId);

        try {
            NotificationMessage message = NotificationMessage.builder()
                    .orderId(orderId)
                    .tenantId(tenantId)
                    .action(NotificationMessage.Action.CANCEL)
                    .build();

            MessageChannel channel = resolver.resolveDestination(IMMEDIATE_NOTIFICATION_TOPIC);
            boolean result = channel.send(MessageBuilder.withPayload(message).build());

            log.info("[sendCancelNotification] 取消任务消息发送{}，订单ID: {}", 
                    result ? "成功" : "失败", orderId);

            return result;

        } catch (Exception e) {
            log.error("[sendCancelNotification] 发送取消任务消息异常，订单ID: {}", orderId, e);
            return false;
        }
    }

    /**
     * 计算RocketMQ延时级别
     * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     */
    private int calculateDelayLevel(long delayMillis) {
        long delaySeconds = delayMillis / 1000;
        
        if (delaySeconds <= 1) return 1;
        if (delaySeconds <= 5) return 2;
        if (delaySeconds <= 10) return 3;
        if (delaySeconds <= 30) return 4;
        if (delaySeconds <= 60) return 5;
        if (delaySeconds <= 120) return 6;
        if (delaySeconds <= 180) return 7;
        if (delaySeconds <= 240) return 8;
        if (delaySeconds <= 300) return 9;
        if (delaySeconds <= 360) return 10;
        if (delaySeconds <= 420) return 11;
        if (delaySeconds <= 480) return 12;
        if (delaySeconds <= 540) return 13;
        if (delaySeconds <= 600) return 14;
        if (delaySeconds <= 1200) return 15;
        if (delaySeconds <= 1800) return 16;
        if (delaySeconds <= 3600) return 17;
        
        return 18; // 2小时
    }
}
