package com.hnyiti.kuaidi.module.delivery.service.notify;

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.hutool.core.util.ObjectUtil;
import com.hnyiti.kuaidi.module.consumer.OrderMqProducer;
import com.hnyiti.kuaidi.module.delivery.service.notify.vo.NotifyMsgVo;
import com.hnyiti.kuaidi.module.member.api.user.MemberUserApi;
import com.hnyiti.kuaidi.module.member.api.user.dto.MemberUserRespDTO;
import com.hnyiti.kuaidi.module.message.api.MessageSendApi;
import com.hnyiti.kuaidi.module.message.api.dto.BatchMessageSendReqDTO;
import com.hnyiti.kuaidi.module.message.api.group.NoticeGroupApi;
import com.hnyiti.kuaidi.module.message.api.notice.NoticeGroupCodeEnum;
import com.hnyiti.kuaidi.module.message.api.template.vo.NoticeTemplateRespVO;
import com.hnyiti.kuaidi.module.pgsql.api.notification.NotificationTaskApi;
import com.hnyiti.kuaidi.module.pgsql.api.notification.vo.CreateNotificationTaskApiReqVO;
import com.hnyiti.kuaidi.module.system.api.param.ParamApi;
import com.hnyiti.kuaidi.module.system.api.sms.dto.send.SmsNotifySendMessage;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NotifyParamServiceImpl implements NotifyParamService {
    @Resource
    private ParamApi paramApi;

    @Lazy
    @Resource
    private MemberUserApi memberUserApi;

    @Resource
    private MpNotifyParamService mpNotifyParamService;

    @Resource
    private WxMiniNotifyParamService wxMiniNotifyParamService;

    @Resource
    private NoticeGroupApi noticeGroupApi;

    @Resource
    private MessageSendApi messageSendApi;

    @Resource
    private SmsNotifyParamService smsNotifyParamService;

    @Resource
    private OrderMqProducer orderMqProducer;

    @Value("${hnyiti.sendMsg.type}")
    private String sendMsgType;

    @Resource
    private NotificationTaskApi notificationTaskApi;



    /**
     * 组装参数，发送消息
     * @param notifyCode 消息组code
     * @param notifyMsgVo 参数
     */
    @Override
    public void sendNotify(String notifyCode, NotifyMsgVo notifyMsgVo) {

        if(ObjectUtil.isEmpty(notifyCode) || ObjectUtil.isEmpty(notifyMsgVo.getTenantId())){
            return;
        }
        // TODO 根据模板分组，查询模板列表
        List<NoticeTemplateRespVO> noticeTemplateRespVOList = noticeGroupApi.byCodeQueryTemplate(notifyCode, notifyMsgVo.getTenantId()).getData();
        if(ObjectUtil.isEmpty(noticeTemplateRespVOList)) {
            return;
        }
        Map<Integer, NoticeTemplateRespVO> noticeTemplateMap = noticeTemplateRespVOList.stream()
                .collect(Collectors.toMap(NoticeTemplateRespVO::getType, vo -> vo));

        String wxAppId = paramApi.getMiniAppId(notifyMsgVo.getTenantId());

        BatchMessageSendReqDTO batchMessageSendReqDTO = new BatchMessageSendReqDTO();
        if (NoticeGroupCodeEnum.ORDER_EXCEPTION.getCode().equals(notifyCode)) {
            // 下单异常通知 order_exception
            batchMessageSendReqDTO = assembleOrderExceptionParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.COURIER_ACCEPTED.getCode().equals(notifyCode)) {
            // 快递员接单通知 courier_accepted
            batchMessageSendReqDTO = assembleCourierAcceptedParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.COURIER_PICKED_UP.getCode().equals(notifyCode)) {
            // 快递员已取件通知 courier_picked_up
            batchMessageSendReqDTO = assembleCourierPickedUpParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.OVERWEIGHT_SURCHARGE.getCode().equals(notifyCode)) {
            // 超重补差通知 overweight_surcharge
            batchMessageSendReqDTO = assembleOverweightSurchargeParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.SIGN_EXCEPTION.getCode().equals(notifyCode)) {
            // 签收异常通知 sign_exception
            batchMessageSendReqDTO = assembleSignExceptionParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.ORDER_SIGNED.getCode().equals(notifyCode)) {
            // 已签收通知 order_signed
            batchMessageSendReqDTO = assembleOrderSignedParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.ORDER_REVIVED.getCode().equals(notifyCode)) {
            // 订单复活通知 order_revived 【调用超重补差模板】
            batchMessageSendReqDTO = assembleOverweightSurchargeParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.ORDER_SUCCESS.getCode().equals(notifyCode)) {
            // 下单成功通知 order_success
            batchMessageSendReqDTO = assembleOrderSuccessParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.LIGHTWEIGHT_REFUND.getCode().equals(notifyCode)) {
            // 超轻退款通知 lightweight_refund
            batchMessageSendReqDTO = assembleLightweightRefundParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.ORDER_CANCELED.getCode().equals(notifyCode)) {
            // 取消订单通知 order_canceled
            batchMessageSendReqDTO = assembleOrderCanceledParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.BULK_SHIPMENT_FAILED.getCode().equals(notifyCode)) {
            // 批量寄件失败通知 bulk_shipment_failed
            batchMessageSendReqDTO = assemBleulkShipmentFailedParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.NEW_MEMBER_JOIN.getCode().equals(notifyCode)) {
            // 新会员加入通知 new_member_join
            batchMessageSendReqDTO = assembleNewMemberJoinParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.NEW_ORDER_COMMISSION.getCode().equals(notifyCode)) {
            // 新订单佣金通知 new_order_commission
            batchMessageSendReqDTO = assembleNewOrderCommissionParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.WORK_ORDER_REPLY.getCode().equals(notifyCode)) {
            // 工单回复通知 work_order_reply
            batchMessageSendReqDTO = assembleWorkOrderReplyParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.COMPLAINT_REPLY.getCode().equals(notifyCode)) {
            // 投诉回复通知 complaint_reply
            batchMessageSendReqDTO = assembleComplaintReplyParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.ACCOUNT_BALANCE_ADDED.getCode().equals(notifyCode)) {
            // 账户余额到账通知 account_balance_added
            batchMessageSendReqDTO = assembleAccountBalanceAddedParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.ACCOUNT_BALANCE_DEDUCTED.getCode().equals(notifyCode)) {
            // 账户余额扣除成功通知 account_balance_deducted
            batchMessageSendReqDTO = assembleAccountBalancDeductedParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.COUPON_ISSUANCE.getCode().equals(notifyCode)) {
            // 优惠券发放通知 COUPON_ISSUANCE
            batchMessageSendReqDTO = assembleCouponIssuanceParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else if (NoticeGroupCodeEnum.COUPON_EXPIRATION.getCode().equals(notifyCode)) {
            // 优惠券即将到期通知 COUPON_EXPIRATION
            batchMessageSendReqDTO = assembleCouponExpirationParams(notifyCode, notifyMsgVo, noticeTemplateMap, wxAppId);
        } else {
            // 其他处理
        }

        if(ObjectUtil.isNotEmpty(batchMessageSendReqDTO)) {
            batchMessageSendReqDTO.setUserId(notifyMsgVo.getUserId());
            batchMessageSendReqDTO.setScene(notifyCode);


            //TODO  记得加个配置 走mq 还是http
            if (ObjectUtil.equals(sendMsgType,"MQ")) {
                // 发送MQ消息
                orderMqProducer.sendMessageOutPut(batchMessageSendReqDTO);  // MQ
            } else {
                messageSendApi.sendBatchMessage(batchMessageSendReqDTO);// HTTP
            }

            // 发送催缴电话
            if (NoticeGroupCodeEnum.OVERWEIGHT_SURCHARGE.getCode().equals(notifyCode)) {

                // 查询用户
                MemberUserRespDTO memberUserRespDTO = memberUserApi.getUser(notifyMsgVo.getUserId());

                Map<String, Object> taskData = new HashMap<>();
                taskData.put("mobile", memberUserRespDTO.getMobile());
//                taskData.put("templateCode","");

                // 2025-06-03 pgsql加了一个电话催缴功能调此接口实现
                // 创建多阶段通知任务
                CreateNotificationTaskApiReqVO reqVO = new CreateNotificationTaskApiReqVO();
                reqVO.setTenantId(notifyMsgVo.getTenantId());
                reqVO.setOrderId(notifyMsgVo.getOrderId());
                reqVO.setTaskType("PHONE_CALL");
                reqVO.setTaskData(taskData);
                Long taskId = notificationTaskApi.createNotificationTask(reqVO).getData();

                try {
                    // 发送电话催缴mq
                    orderMqProducer.sendCompensationImmediate(notifyMsgVo.getOrderId(), taskId, null, "1");
                } catch (Exception e) {
                    log.error("发送催缴电话mq失败，失败原因:", e.getMessage());
                }
            }
        }

    }

    /**
     * 组装下单异常的模板参数
     */
    public BatchMessageSendReqDTO assembleOrderExceptionParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {

        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getOrderId());

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            //TODO  占未配置

        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getOrderExceptionParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/1_order_detail/2_order_detail?orderId="+msgVo.getOrderId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }
        // 返回模板参数
        return reqDTO;
    }


    /**
     * 快递员接单通知 courier_accepted
     */
    public BatchMessageSendReqDTO assembleCourierAcceptedParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {

        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getOrderId());

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(1);

            WxMaSubscribeMessage wxMpTemplateMessage = wxMiniNotifyParamService.
                    getCourierAcceptedParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/1_order_detail/2_order_detail?orderId="+msgVo.getOrderId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMaSubscribeMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getCourierAcceptedParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/1_order_detail/2_order_detail?orderId="+msgVo.getOrderId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }


        // 返回模板参数
        return reqDTO;
    }


    /**
     * 快递员已取件通知 courier_picked_up
     * @param templateCode
     * @param msgVo 【orderId：订单id,tenantId:分销商id,orderNo:运单号,userId:用户id,expressCompany：快递公司，orderNo:运单号，pickupTime:取件时间】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleCourierPickedUpParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {

        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getOrderId());

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            //TODO  占未配置

        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getCourierPickedUpParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/1_order_detail/2_order_detail?orderId="+msgVo.getOrderId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }

        // 返回模板参数
        return reqDTO;
    }


    /**
     * 超重补差通知 overweight_surcharge
     * @param templateCode
     * @param msgVo 【orderId：订单id,tenantId:分销商id,orderNo:运单号,userId:用户id,expressCompany：快递公司，orderNo:运单号，
     *              actualWeight：实际重量，actualFreight：实际运费，freightPaid：已付运费，compensationAmount：需补运费，mobile：下单人电话号码】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleOverweightSurchargeParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {

        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getOrderId());
        if(ObjectUtil.isNotEmpty(msgVo.getCompensationAmount())){
            reqDTO.setCompensationAmount(msgVo.getCompensationAmount());
        }

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(1);

            WxMaSubscribeMessage wxMpTemplateMessage = wxMiniNotifyParamService.
                    getOverweightSurchargeParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/4_over_weight/4_over_weight");
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;

                reqDTO.setMaSubscribeMessage(wxMpTemplateMessage);
            }

        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getOverweightSurchargeParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/4_over_weight/4_over_weight");
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(3);

            SmsNotifySendMessage smsSendSingleToUserReqDTO = smsNotifyParamService.
                    getOverweightSurchargeParams(msgVo, noticeTemplateRespVO.getApiTemplateId(), wxAppId , "/fpage/4_over_weight/4_over_weight");
            if(ObjectUtil.isNotEmpty(smsSendSingleToUserReqDTO)) {
                isSend = true;
                reqDTO.setSmsMessage(smsSendSingleToUserReqDTO);
            }

        }

        if(!isSend){
            return null;
        }

        // 返回模板参数
        return reqDTO;
    }





    /**
     * 签收异常通知 sign_exception
     * @param templateCode
     * @param msgVo 【orderId：订单id,tenantId:分销商id,orderNo:运单号,userId:用户id，orderNo:运单号，abnormalReason:异常原因】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleSignExceptionParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {

        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getOrderId());

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            //TODO  占未配置,需要配置

        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getSignExceptionParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/1_order_detail/2_order_detail?orderId="+msgVo.getOrderId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }

        // 返回模板参数
        return reqDTO;
    }




    /**
     * 已签收通知 order_signed
     * @param templateCode
     * @param msgVo 【orderId：订单id,tenantId:分销商id,orderNo:运单号,userId:用户id，orderNo:运单号，expressCompany:快递公司，orderTime:下单时间,signingTime:签收时间】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleOrderSignedParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {

        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getOrderId());
        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            //TODO  占未配置

        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getOrderSignedParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/1_order_detail/2_order_detail?orderId="+msgVo.getOrderId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }
        // 返回模板参数
        return reqDTO;
    }


    /**
     * 下单成功通知 order_success
     * @param templateCode
     * @param msgVo 【orderId：订单id,tenantId:分销商id,orderNo:运单号,userId:用户id，orderNo:运单号，expressCompany:快递公司，freightPaid:支付金额】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleOrderSuccessParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {
        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getOrderId());

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            //TODO  占未配置

        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getOrderSuccessParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/1_order_detail/2_order_detail?orderId="+msgVo.getOrderId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }
        // 返回模板参数
        return reqDTO;
    }


    /**
     * 超轻退款通知 lightweight_refund
     * @param templateCode
     * @param msgVo 【orderId：订单id,tenantId:分销商id,orderNo:运单号,userId:用户id，orderNo:运单号，refundMethod:退费方式差价退到钱包/原路返回，refundAmount:退费金额，createDate:操作时间】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleLightweightRefundParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {
        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getOrderId());

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            //TODO  占未配置

        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getLightweightRefundParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/1_order_detail/2_order_detail?orderId="+msgVo.getOrderId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }
        // 返回模板参数
        return reqDTO;
    }


    /**
     * 取消订单通知 order_canceled
     * @param templateCode
     * @param msgVo 【orderId：订单id,tenantId:分销商id,orderNo:运单号,userId:用户id，orderNo:运单号，orderTime:下单时间,cancelTime:取消时间,cancelCause:取消原因】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleOrderCanceledParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {
        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getOrderId());

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            // 小程序模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(1);

            WxMaSubscribeMessage wxMpTemplateMessage = wxMiniNotifyParamService.
                    getOrderCanceledParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/4_over_weight/4_over_weight");
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMaSubscribeMessage(wxMpTemplateMessage);
            }

        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getOrderCanceledParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/1_order_detail/2_order_detail?orderId="+msgVo.getOrderId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }

        // 返回模板参数
        return reqDTO;
    }



    /**
     * 批量寄件失败通知 bulk_shipment_failed
     * @param templateCode
     * @param msgVo 【orderId：订单id,tenantId:分销商id,orderNo:运单号,userId:用户id，orderNo:运单号，orderBatchId:批次号,createDate:时间,senderNum:寄件数量,failNum:失败数量】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assemBleulkShipmentFailedParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {
        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getOrderId());

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            //TODO  占未配置
        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getBleulkShipmentFailedParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/1_order_detail/2_order_detail?orderId="+msgVo.getOrderId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);

            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }
        // 返回模板参数
        return reqDTO;
    }


    /**
     * 新会员加入通知 new_member_join
     * @param templateCode
     * @param msgVo 【orderId：订单id,tenantId:分销商id,orderNo:运单号,userId:用户id，orderNo:运单号，userName:客户名称,createDate:注册时间】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleNewMemberJoinParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {
        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getOrderId());

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            //TODO  占未配置
        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getNewMemberJoinParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/1_order_detail/2_order_detail?orderId="+msgVo.getOrderId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }
        // 返回模板参数
        return reqDTO;
    }


    /**
     * 新订单佣金通知 new_order_commission
     * @param templateCode
     * @param msgVo 【orderId：订单id,tenantId:分销商id,orderNo:运单号,userId:用户id，orderNo:运单号，userName:客户名称,createDate:注册时间】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleNewOrderCommissionParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {
        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getOrderId());

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            //TODO  占未配置
        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            //TODO  占未配置，还需要讨论
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }
        // 返回模板参数
        return reqDTO;
    }


    /**
     * 工单回复通知 work_order_reply
     * @param templateCode
     * @param msgVo 【orderId：订单id,tenantId:分销商id,orderNo:运单号,userId:用户id，orderNo:运单号，workId:工单编号,workStatus:工单状态，remarks:备注】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleWorkOrderReplyParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {
        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getOrderId());

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(1);

            WxMaSubscribeMessage wxMpTemplateMessage = wxMiniNotifyParamService.
                    getWorkOrderReplyParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/fpage/4_over_weight/4_over_weight");
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;

                reqDTO.setMaSubscribeMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getWorkOrderReplyParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/pagesCenter/3_workorder_detail/3_workorder_detail?id="+msgVo.getWorkId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }
        // 返回模板参数
        return reqDTO;
    }



    /**
     * 投诉回复通知 complaint_reply
     * @param templateCode
     * @param msgVo 【orderId：订单id,tenantId:分销商id,orderNo:运单号,userId:用户id，orderNo:运单号，complainId:投诉反馈id，complainStatus:投诉状态,createDate:处理时间
     *              ,questionContent:问题内容,answerContent:问题内容，feedbackDate:投诉反馈时间】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleComplaintReplyParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {
        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getComplainId()+"");
        List<Object> messageSendList = new ArrayList<>();

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(1);

            WxMaSubscribeMessage wxMpTemplateMessage = wxMiniNotifyParamService.
                    getComplaintReplyParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "/pagesCenter/3_complain/3_complain_detail?id="+msgVo.getComplainId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMaSubscribeMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getComplaintReplyParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , "pagesCenter/3_complain/3_complain_detail?id="+msgVo.getComplainId());
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }
        // 返回模板参数
        return reqDTO;
    }


    /**
     * 账户余额到账通知 account_balance_added
     * @param templateCode
     * @param msgVo 【tenantId:分销商id,userId:用户id，rechargeAmount:充值金额，accountAmount:账户总额,createDate:充值时间】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleAccountBalanceAddedParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {
        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getUserId()+"");

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            //TODO  占未配置
        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getAccountBalanceAddedParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , null);
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }
        // 返回模板参数
        return reqDTO;
    }

    /**
     * 账户余额扣除成功通知 account_balance_deducted
     * @param templateCode
     * @param msgVo 【tenantId:分销商id,userId:用户id，deductAmount:扣除金额，accountAmount:账户总额, balanceAmount:账户余额,createDate:扣除时间】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleAccountBalancDeductedParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {
        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getUserId()+"");
        List<Object> messageSendList = new ArrayList<>();

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if(noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            //TODO  占未配置
        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(2);

            WxMpTemplateMessage wxMpTemplateMessage = mpNotifyParamService.
                    getAccountBalancDeductedParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId , null);
            if(ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMpTemplateMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if (!isSend) {
            return null;
        }
        // 返回模板参数
        return reqDTO;
    }


    /**
     * 优惠券发放通知 COUPON_ISSUANCE
     *
     * @param templateCode
     * @param msgVo             【tenantId:分销商id,userId:用户id，couponValue:优惠券面值，couponName:优惠券名称, endDate:有效期,usageScenarios:使用用场景,remark:温馨提示,couponId:优惠券id】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleCouponIssuanceParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {
        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getUserId() + "");
        List<Object> messageSendList = new ArrayList<>();

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if (noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(1);

            WxMaSubscribeMessage wxMpTemplateMessage = wxMiniNotifyParamService.
                    getCouponIssuanceParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId, "/fpage/6_coupon/coupon-detail?id=" + msgVo.getCouponId());
            if (ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMaSubscribeMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            //TODO  占未配置
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if (!isSend) {
            return null;
        }
        // 返回模板参数
        return reqDTO;
    }

    /**
     * 优惠券即将到期通知 COUPON_EXPIRATION
     *
     * @param templateCode
     * @param msgVo             【tenantId:分销商id,userId:用户id，couponValue:优惠券面值，couponName:优惠券名称, endDate:有效期,remark:温馨提示,couponId:优惠券id】
     * @param noticeTemplateMap
     * @param wxAppId
     * @return
     */
    public BatchMessageSendReqDTO assembleCouponExpirationParams(String templateCode, NotifyMsgVo msgVo, Map<Integer, NoticeTemplateRespVO> noticeTemplateMap, String wxAppId) {
        BatchMessageSendReqDTO reqDTO = new BatchMessageSendReqDTO();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTenantId(msgVo.getTenantId());
        reqDTO.setBusinessNo(msgVo.getUserId() + "");
        List<Object> messageSendList = new ArrayList<>();

        // 是否发送消息
        Boolean isSend = false;

        // 1.小程序，2.公众号 3.短信
        if (noticeTemplateMap.containsKey(1)) {
            // 小程序模板参数
            NoticeTemplateRespVO noticeTemplateRespVO = noticeTemplateMap.get(1);

            WxMaSubscribeMessage wxMpTemplateMessage = wxMiniNotifyParamService.
                    getCouponExpirationParams(msgVo, noticeTemplateRespVO.getCode(), wxAppId, "/fpage/6_coupon/coupon-detail?id=" + msgVo.getCouponId());
            if (ObjectUtil.isNotEmpty(wxMpTemplateMessage)) {
                isSend = true;
                reqDTO.setMaSubscribeMessage(wxMpTemplateMessage);
            }
        }

        if (noticeTemplateMap.containsKey(2)) {
            // 公众号模板参数
            //TODO  占未配置
        }

        if (noticeTemplateMap.containsKey(3)) {
            // 短信模板参数
            //TODO  占未配置

        }

        if(!isSend){
            return null;
        }
        // 返回模板参数
        return reqDTO;
    }


}
