package com.hnyiti.kuaidi.module.pgsql.service.notification.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知发送结果
 *
 * <AUTHOR>
 */
@Data
public class NotificationResult {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 响应数据
     */
    private Map<String, Object> responseData;



    /**
     * 发送时间
     */
    private LocalDateTime sendTime;

    /**
     * 接收人
     */
    private String recipient;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 创建成功结果
     */
    public static NotificationResult success(String recipient, String content) {
        NotificationResult result = new NotificationResult();
        result.setSuccess(true);
        result.setRecipient(recipient);
        result.setContent(content);
        result.setSendTime(LocalDateTime.now());
        return result;
    }



    /**
     * 创建成功结果（带响应数据）
     */
    public static NotificationResult success(String recipient, String content, Map<String, Object> responseData) {
        NotificationResult result = success(recipient, content);
        result.setResponseData(responseData);
        return result;
    }

    /**
     * 设置响应数据（支持链式调用）
     */
    public NotificationResult setResponseData(Map<String, Object> responseData) {
        this.responseData = responseData;
        return this;
    }

    /**
     * 创建失败结果
     */
    public static NotificationResult failure(String errorMessage) {
        NotificationResult result = new NotificationResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        result.setSendTime(LocalDateTime.now());
        return result;
    }

    /**
     * 创建失败结果（带响应数据）
     */
    public static NotificationResult failure(String errorMessage, Map<String, Object> responseData) {
        NotificationResult result = failure(errorMessage);
        result.setResponseData(responseData);
        return result;
    }
}
