package com.hnyiti.kuaidi.module.pgsql.api.notification;

import com.hnyiti.kuaidi.framework.common.pojo.CommonResult;
import com.hnyiti.kuaidi.module.pgsql.api.ApiConstants;
import com.hnyiti.kuaidi.module.pgsql.api.notification.vo.CreateNotificationTaskApiReqVO;
import com.hnyiti.kuaidi.module.pgsql.api.notification.vo.NotificationTaskApiVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 通知任务 API 接口
 * 提供给其他模块调用的通知任务服务
 *
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
public interface NotificationTaskApi {

    String PREFIX = ApiConstants.PREFIX + "/notification/task";

    /**
     * 创建通知任务
     *
     * @param reqVO 创建请求参数
     * @return 任务ID
     */
    @PostMapping(PREFIX + "/create")
    CommonResult<Long> createNotificationTask(@Valid @RequestBody CreateNotificationTaskApiReqVO reqVO);

    /**
     * 根据ID查询通知任务
     *
     * @param id 任务ID
     * @return 通知任务详情
     */
    @GetMapping(PREFIX + "/{id}")
    CommonResult<NotificationTaskApiVO> getNotificationTask(@PathVariable("id") Long id);

    /**
     * 更新任务状态
     *
     * @param id         任务ID
     * @param taskStatus 任务状态
     * @return 是否成功
     */
    @PutMapping(PREFIX + "/{id}/status")
    CommonResult<Boolean> updateTaskStatus(@PathVariable("id") Long id,
                                           @RequestParam("taskStatus") String taskStatus);

    /**
     * 根据订单ID查询通知任务列表
     *
     * @param orderId  订单ID
     * @param tenantId 租户ID
     * @return 通知任务列表
     */
    @GetMapping(PREFIX + "/list/order")
    CommonResult<List<NotificationTaskApiVO>> getNotificationTasksByOrderId(
            @RequestParam("orderId") String orderId,
            @RequestParam("tenantId") Long tenantId);

    /**
     * 创建短信通知任务（便捷方法）
     *
     * @param orderId       订单ID
     * @param tenantId      租户ID
     * @param mobile        手机号
     * @param content       短信内容
     * @param scheduledTime 计划执行时间（可选）
     * @return 任务ID
     */
    @PostMapping(PREFIX + "/create/sms")
    CommonResult<Long> createSmsNotificationTask(
            @RequestParam("orderId") String orderId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("mobile") String mobile,
            @RequestParam("content") String content,
            @RequestParam(value = "scheduledTime", required = false) LocalDateTime scheduledTime);

    /**
     * 创建小程序通知任务（便捷方法）
     *
     * @param orderId       订单ID
     * @param tenantId      租户ID
     * @param openid        用户openid
     * @param templateId    模板ID
     * @param templateData  模板数据
     * @param scheduledTime 计划执行时间（可选）
     * @return 任务ID
     */
    @PostMapping(PREFIX + "/create/miniprogram")
    CommonResult<Long> createMiniProgramNotificationTask(
            @RequestParam("orderId") String orderId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("openid") String openid,
            @RequestParam("templateId") String templateId,
            @RequestBody Map<String, Object> templateData,
            @RequestParam(value = "scheduledTime", required = false) LocalDateTime scheduledTime);

    /**
     * 创建公众号通知任务（便捷方法）
     *
     * @param orderId       订单ID
     * @param tenantId      租户ID
     * @param openid        用户openid
     * @param templateId    模板ID
     * @param templateData  模板数据
     * @param scheduledTime 计划执行时间（可选）
     * @return 任务ID
     */
    @PostMapping(PREFIX + "/create/wechatmp")
    CommonResult<Long> createWechatMpNotificationTask(
            @RequestParam("orderId") String orderId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("openid") String openid,
            @RequestParam("templateId") String templateId,
            @RequestBody Map<String, Object> templateData,
            @RequestParam(value = "scheduledTime", required = false) LocalDateTime scheduledTime);

    /**
     * 创建电话通知任务（便捷方法）
     *
     * @param orderId       订单ID
     * @param tenantId      租户ID
     * @param mobile        手机号
     * @param content       通话内容
     * @param scheduledTime 计划执行时间（可选）
     * @return 任务ID
     */
    @PostMapping(PREFIX + "/create/phonecall")
    CommonResult<Long> createPhoneCallNotificationTask(
            @RequestParam("orderId") String orderId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("mobile") String mobile,
            @RequestParam("content") String content,
            @RequestParam(value = "scheduledTime", required = false) LocalDateTime scheduledTime);

    /**
     * 批量创建通知任务
     *
     * @param reqVOList 创建请求参数列表
     * @return 任务ID列表
     */
    @PostMapping(PREFIX + "/batch/create")
    CommonResult<List<Long>> batchCreateNotificationTasks(
            @Valid @RequestBody List<CreateNotificationTaskApiReqVO> reqVOList);

    /**
     * 取消订单的所有待执行任务
     *
     * @param orderId  订单ID
     * @param tenantId 租户ID
     * @return 取消的任务数量
     */
    @PutMapping(PREFIX + "/cancel/order")
    CommonResult<Integer> cancelOrderNotificationTasks(
            @RequestParam("orderId") String orderId,
            @RequestParam("tenantId") Long tenantId);
}
