package com.hnyiti.kuaidi.module.pgsql.service.notification.checker;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单状态检查器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OrderStatusChecker {

    /**
     * 检查订单是否已完成补差
     *
     * @param orderId 订单ID
     * @return 是否已完成补差
     */
    public boolean isOrderCompensated(String orderId) {
        try {
            // TODO: 调用订单服务API查询最新的补差记录
            // 这里需要集成现有的订单补差查询接口

            log.debug("[isOrderCompensated] 检查订单补差状态，订单ID: {}", orderId);

            // 临时实现：返回false继续执行通知流程
            // 实际实现需要调用 OrderCompensateService.getLatestByOrderId(orderId)
            // 并检查 compensatingResult 是否为 "3"（已完成）

            return false;

        } catch (Exception e) {
            log.error("[isOrderCompensated] 检查订单补差状态异常，订单ID: {}", orderId, e);
            // 异常情况下返回false，继续执行通知
            return false;
        }
    }
}
