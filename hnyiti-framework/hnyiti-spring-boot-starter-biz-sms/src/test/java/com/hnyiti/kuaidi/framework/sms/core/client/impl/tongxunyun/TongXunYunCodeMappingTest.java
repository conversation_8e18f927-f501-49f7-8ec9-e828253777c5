package com.hnyiti.kuaidi.framework.sms.core.client.impl.tongxunyun;

import com.hnyiti.kuaidi.framework.common.exception.ErrorCode;
import com.hnyiti.kuaidi.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.hnyiti.kuaidi.framework.sms.core.enums.SmsFrameworkErrorCodeConstants;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * {@link TongXunYunCodeMapping} 的单元测试
 */
public class TongXunYunCodeMappingTest {

    private final TongXunYunCodeMapping codeMapping = new TongXunYunCodeMapping();

    @Test
    public void testSuccessCode() {
        ErrorCode result = codeMapping.apply("0");
        assertEquals(GlobalErrorCodeConstants.SUCCESS, result);
    }

    @Test
    public void testAccountInvalidCodes() {
        // 账号或密码错误
        ErrorCode result1 = codeMapping.apply("1");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_ACCOUNT_INVALID, result1);

        // 用户被禁发或禁用
        ErrorCode result19 = codeMapping.apply("19");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_ACCOUNT_INVALID, result19);

        // ip鉴权失败
        ErrorCode result20 = codeMapping.apply("20");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_ACCOUNT_INVALID, result20);
    }

    @Test
    public void testMobileInvalidCodes() {
        // 错误号码/限制运营商号码
        ErrorCode result4 = codeMapping.apply("4");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_MOBILE_INVALID, result4);

        // 手机号码为空
        ErrorCode result14 = codeMapping.apply("14");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_MOBILE_INVALID, result14);
    }

    @Test
    public void testTemplateInvalidCodes() {
        // msgid太长，不得超过64位
        ErrorCode result3 = codeMapping.apply("3");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID, result3);

        // 短信内容超过最大限制
        ErrorCode result6 = codeMapping.apply("6");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID, result6);

        // 扩展子号码无效
        ErrorCode result7 = codeMapping.apply("7");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID, result7);

        // 定时时间格式错误
        ErrorCode result8 = codeMapping.apply("8");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID, result8);

        // 短信内容为空
        ErrorCode result21 = codeMapping.apply("21");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID, result21);

        // 消息格式错误
        ErrorCode result99 = codeMapping.apply("99");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_TEMPLATE_INVALID, result99);
    }

    @Test
    public void testBusinessLimitControlCodes() {
        // 手机号码个数超过最大限制
        ErrorCode result5 = codeMapping.apply("5");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_SEND_BUSINESS_LIMIT_CONTROL, result5);

        // 无可用号码
        ErrorCode result24 = codeMapping.apply("24");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_SEND_BUSINESS_LIMIT_CONTROL, result24);

        // 批量提交短信数超过最大限制
        ErrorCode result25 = codeMapping.apply("25");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_SEND_BUSINESS_LIMIT_CONTROL, result25);
    }

    @Test
    public void testSystemBusyCode() {
        // 系统正忙
        ErrorCode result98 = codeMapping.apply("98");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_UNKNOWN, result98);
    }

    @Test
    public void testUnknownCode() {
        // 未知错误码
        ErrorCode result = codeMapping.apply("999");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_UNKNOWN, result);

        // 空字符串
        ErrorCode resultEmpty = codeMapping.apply("");
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_UNKNOWN, resultEmpty);

        // null
        ErrorCode resultNull = codeMapping.apply(null);
        assertEquals(SmsFrameworkErrorCodeConstants.SMS_UNKNOWN, resultNull);
    }

    /**
     * 测试所有已知的错误码映射
     */
    @Test
    public void testAllKnownCodes() {
        // 验证所有已知错误码都有对应的映射
        String[] knownCodes = {"0", "1", "3", "4", "5", "6", "7", "8", "14", "19", "20", "21", "24", "25", "98", "99"};

        for (String code : knownCodes) {
            ErrorCode result = codeMapping.apply(code);
            System.out.println("错误码 " + code + " 映射到: " + result.getMsg());
            // 确保没有映射到 null
            assert result != null;
        }
    }
}
