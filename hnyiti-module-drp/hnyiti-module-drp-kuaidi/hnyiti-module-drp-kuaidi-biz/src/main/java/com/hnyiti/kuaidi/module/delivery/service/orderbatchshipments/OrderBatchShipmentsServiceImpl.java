package com.hnyiti.kuaidi.module.delivery.service.orderbatchshipments;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hnyiti.kuaidi.framework.common.enums.CommonStatusEnum;
import com.hnyiti.kuaidi.framework.common.exception.ErrorCode;
import com.hnyiti.kuaidi.framework.common.exception.ServiceException;
import com.hnyiti.kuaidi.framework.common.pojo.CommonResult;
import com.hnyiti.kuaidi.framework.common.pojo.PageResult;
import com.hnyiti.kuaidi.framework.pay.core.enums.channel.PayChannelEnum;
import com.hnyiti.kuaidi.framework.pay.core.enums.order.PayOrderDisplayModeEnum;
import com.hnyiti.kuaidi.framework.security.core.util.SecurityFrameworkUtils;
import com.hnyiti.kuaidi.framework.tenant.core.context.TenantContextHolder;
import com.hnyiti.kuaidi.module.PayLogConstants;
import com.hnyiti.kuaidi.module.delivery.controller.admin.order.OrderBigVo;
import com.hnyiti.kuaidi.module.delivery.controller.admin.orderbatchshipments.vo.*;
import com.hnyiti.kuaidi.module.delivery.controller.app.vo.MiniOrderPayReqVO;
import com.hnyiti.kuaidi.module.delivery.convert.orderbatchshipments.OrderBatchShipmentsConvert;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.feeuser.FeeUserDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.order.OrderDO;
import com.hnyiti.kuaidi.module.delivery.dal.dataobject.orderbatchshipments.OrderBatchShipmentsDO;
import com.hnyiti.kuaidi.module.delivery.dal.mysql.orderbatchshipments.OrderBatchShipmentsMapper;
import com.hnyiti.kuaidi.module.delivery.enums.OrderConstants;
import com.hnyiti.kuaidi.module.delivery.service.address.AddressService;
import com.hnyiti.kuaidi.module.delivery.service.feeuser.FeeUserService;
import com.hnyiti.kuaidi.module.delivery.service.notify.NotifyParamService;
import com.hnyiti.kuaidi.module.delivery.service.order.OrderService;
import com.hnyiti.kuaidi.module.delivery.service.order.OrderSubmitService;
import com.hnyiti.kuaidi.module.delivery.service.order.pay.PayStrategy;
import com.hnyiti.kuaidi.module.delivery.service.order.pay.PayStrategyFactory;
import com.hnyiti.kuaidi.module.delivery.service.order.pay.vo.PayRequest;
import com.hnyiti.kuaidi.module.member.api.balance.vo.api.BalanceApi;
import com.hnyiti.kuaidi.module.member.api.user.MemberUserApi;
import com.hnyiti.kuaidi.module.member.controller.admin.wechatpaylog.vo.WechatPayLogCreateReqVO;
import com.hnyiti.kuaidi.module.member.enums.FeeConstants;
import com.hnyiti.kuaidi.module.member.service.wechatpaylog.WechatPayLogService;
import com.hnyiti.kuaidi.module.pay.api.order.dto.PayOrderCreateReqDTO;
import com.hnyiti.kuaidi.module.pay.controller.admin.order.vo.PayOrderSubmitReqVO;
import com.hnyiti.kuaidi.module.pay.controller.admin.order.vo.PayOrderSubmitRespVO;
import com.hnyiti.kuaidi.module.pay.dal.dataobject.app.PayAppDO;
import com.hnyiti.kuaidi.module.pay.service.app.PayAppService;
import com.hnyiti.kuaidi.module.pay.service.order.PayOrderService;
import com.hnyiti.kuaidi.module.system.api.param.ParamApi;
import com.hnyiti.kuaidi.module.system.api.social.SocialUserApi;
import com.hnyiti.kuaidi.module.system.api.tenant.TenantApi;
import com.hnyiti.kuaidi.module.system.api.tenant.dto.TenantRespVO;
import com.hnyiti.kuaidi.module.system.enums.social.SocialTypeEnum;
import com.sankuai.inf.leaf.service.SegmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.hnyiti.kuaidi.framework.common.util.date.LocalDateTimeUtils.addTime;
import static com.hnyiti.kuaidi.framework.common.util.servlet.ServletUtils.getClientIP;

/**
 * 批量寄件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class OrderBatchShipmentsServiceImpl implements OrderBatchShipmentsService {

    @Resource
    private OrderBatchShipmentsMapper orderBatchShipmentsMapper;
    @Lazy
    @Resource
    private OrderService orderService;
    @Lazy
    @Resource
    private AddressService addressService;
    @Lazy
    @Resource
    private TenantApi tenantApi;
    @Lazy
    @Resource
    private BalanceApi balanceApi;
    @Lazy
    @Resource
    private RedisTemplate redisTemplate;
    @Lazy
    @Resource
    private SocialUserApi socialUserApi;
    @Lazy
    @Resource
    private PayOrderService payOrderService;
    @Lazy
    @Resource
    private ParamApi paramApi;
    @Lazy
    @Resource
    private PayAppService payAppService;
    @Lazy
    @Resource
    private WechatPayLogService wechatPayLogService;
    @Lazy
    @Resource
    private SegmentService segmentService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Lazy
    @Resource
    private OrderSubmitService orderSubmitService;
    @Resource
    private FeeUserService feeUserService;
    @Resource
    private MemberUserApi memberUserApi;
    @Lazy
    @Resource
    private NotifyParamService notifyParamService;


    @Override
    public Long createOrderBatchShipments(OrderBatchShipmentsCreateReqVO createReqVO) {
        OrderBatchShipmentsDO orderBatchShipments = OrderBatchShipmentsConvert.INSTANCE.convert(createReqVO);
        orderBatchShipmentsMapper.insert(orderBatchShipments);
        return orderBatchShipments.getId();
    }

    @Override
    public void updateOrderBatchShipments(OrderBatchShipmentsUpdateReqVO updateReqVO) {
        OrderBatchShipmentsDO updateObj = OrderBatchShipmentsConvert.INSTANCE.convert(updateReqVO);
        orderBatchShipmentsMapper.updateById(updateObj);
    }

    @Override
    public void deleteOrderBatchShipments(Long id) {
        orderBatchShipmentsMapper.deleteById(id);
    }

    @Override
    public OrderBatchShipmentsDO getOrderBatchShipments(Long id) {
        return orderBatchShipmentsMapper.selectById(id);
    }

    @Override
    public List<OrderBatchShipmentsDO> getOrderBatchShipmentsList(Collection<Long> ids) {
        return orderBatchShipmentsMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<OrderBatchShipmentsRespVO> getOrderBatchShipmentsPage(OrderBatchShipmentsPageReqVO pageReqVO) {
        PageResult<OrderBatchShipmentsRespVO> orderBatchInfoPage = orderBatchShipmentsMapper.getOrderBatchInfoPage(pageReqVO);

        if (!CollectionUtils.isEmpty(orderBatchInfoPage.getList())) {
            orderBatchInfoPage.getList().stream().forEach(batchShipment -> {
                // 批量订单ID
                Long batchId = batchShipment.getId();

                List<OrderDO> orderBatch = orderService.getOrderBatch(batchShipment.getUserId(), batchId);
                batchShipment.setOrderInfo(orderBatch);

                // 批量订单：总金额（应付金额）
                BigDecimal countAmount = BigDecimal.ZERO;
                // 批量订单：实付金额
                BigDecimal actualAmount = BigDecimal.ZERO;
                // 优惠金额
                BigDecimal discountAmount = BigDecimal.ZERO;

                // 计算费用明细信息
                if (!CollectionUtils.isEmpty(orderBatch)) {
                    for (OrderDO orderDO : orderBatch) {
                        FeeUserDO feeUser = feeUserService.getFeeUserByOrderId(orderDO.getId());
                        countAmount = countAmount.add(
                                (feeUser.getLastPayAmount() != null && feeUser.getLastPayAmount().compareTo(BigDecimal.ZERO) > 0)
                                        ? feeUser.getLastPayAmount()
                                        : feeUser.getLastPrice()
                        );

                        actualAmount = actualAmount.add(
                                (feeUser.getPayAmount() != null && feeUser.getPayAmount().compareTo(BigDecimal.ZERO) > 0)
                                        ? feeUser.getPayAmount() : BigDecimal.ZERO);

                        // 计算优惠金额
                        if (ObjectUtil.isNotEmpty(feeUser.getCouponId())) {
                            discountAmount = discountAmount.add(feeUser.getReliefAmount());
                        }
                    }

                    // 支付方式  由于批量订单表中暂未记录支付方式，选择第一笔订单的支付方式用做整个批量订单支付方式的展示
                    batchShipment.setPayType(orderBatch.get(0).getPayType());
                }

                batchShipment.setCountAmount(countAmount);
                batchShipment.setActualAmount(actualAmount);
                batchShipment.setDiscountAmount(discountAmount);
            });
        }
        return orderBatchInfoPage;
    }

    @Override
    public List<OrderBatchShipmentsDO> getOrderBatchShipmentsList(OrderBatchShipmentsExportReqVO exportReqVO) {
        return orderBatchShipmentsMapper.selectList(exportReqVO);
    }

    @Override
    public void insertBatch(List<OrderBatchShipmentsDO> batch) {
        orderBatchShipmentsMapper.insertBatch(batch, batch.size());
    }

    @Override
    public void updateBatchById(List<OrderBatchShipmentsDO> batch) {
        orderBatchShipmentsMapper.updateBatch(batch, batch.size());
    }

    @Override
    public PageResult<OrderBatchShipmentsRespVO> getOrderBatchInfoPage(OrderBatchShipmentsPageReqVO pageVO) {
        PageResult<OrderBatchShipmentsRespVO> result = orderBatchShipmentsMapper.getOrderBatchInfoPage(pageVO);
        if (CollUtil.isNotEmpty(result.getList())) {
            // 遍历获取用户Id查询相应的批量订单
            for (OrderBatchShipmentsRespVO respVO : result.getList()) {
                Long userId = respVO.getUserId();
                Long batchId = respVO.getId();
                if (ObjectUtil.isEmpty(userId)) {
                    continue;
                }
                // 根据batchId、userId查询订单表
                List<OrderDO> orderInfo = orderService.getOrderBatch(userId, batchId);
                if (CollUtil.isNotEmpty(orderInfo)) {
                    respVO.setOrderInfo(orderInfo);
                }
            }
        }
        return result;
    }


    @Override
    @DSTransactional
    public Map batchSubmitPayV2(MiniOrderPayReqVO params) {
//        不用 orderSource 区分小程序、后台下单 使用orderType 1:小程序、2：后台
        Map coreMap = new HashMap();
        Long userId = null;
        Long tenantId = TenantContextHolder.getTenantId();
        if (ObjectUtil.isNotEmpty(params.getOrderType()) && params.getOrderType().equals(OrderConstants.ORDER_TYPE_USER)) {
            userId = SecurityFrameworkUtils.getLoginUserId();
        }

        String batchOrderId = params.getBatchOrderId();
        String payType = params.getPayType();

        // 查询批量寄件订单
        OrderBatchShipmentsDO orderBatchShipmentsDO = orderBatchShipmentsMapper.selectById(batchOrderId);
        //批量寄件校验
        validateBatchSubmitParams(orderBatchShipmentsDO, tenantId);

        // 实付金额
        BigDecimal lastPrice = BigDecimal.ZERO;
        if (ObjectUtil.isNotEmpty(orderBatchShipmentsDO.getOrderAmount())) {
            lastPrice = orderBatchShipmentsDO.getOrderAmount();
        }

        // 余额支付
        if (OrderConstants.PAY_TYPE_BALANCE.equals(payType)) {
            // 正常下单_小程序下单
            if (OrderConstants.ORDER_TYPE_USER.equals(params.getOrderType())) {
                // 用户批量寄件订单支付--扣款
                CommonResult commonResult = tenantApi.deductUserAmount(userId, null, Long.parseLong(batchOrderId), lastPrice.negate(), FeeConstants.PAY_TYPE_BATCH_ORDER, String.format("批量寄件扣款-%s", batchOrderId));
                if (!commonResult.isSuccess()) {
                    throw new ServiceException(commonResult.getCode(), commonResult.getMsg());
                }
            }
            // 批量寄件下单
            OrderBatchShipmentsDO orderDO = paymentCallbackV2(batchOrderId, "balance", null);
            if (null == orderDO) {
                throw new ServiceException(new ErrorCode(20503, "批量下单失败"));
            }
        } else if (OrderConstants.PAY_TYPE_WECHAT.equals(payType)) {
            // 用户微信支付返回支付参数
            return getWechatPayParams(params, orderBatchShipmentsDO);
        } else if (OrderConstants.PAY_TYPE_ALIPAY.equals(payType)) {
            Map<String, String> channelExtras = new HashMap<>();
            channelExtras.put("batchId", orderBatchShipmentsDO.getId() + "");
//            Integer price = orderBatchShipmentsDO.getOrderAmount().multiply(BigDecimal.valueOf(100)).intValue();
            channelExtras.put("type", "batchDeliveryV2");
            PayStrategy payHandler = PayStrategyFactory.createPayHandler(PayLogConstants.PAY_LOG_PAY_TYPE_ALIPAY);
            Map returnMap = payHandler.createPayOrder(new PayRequest().setDisplayMode(params.getDisplayMode()).setPaymentChannel(params.getPaymentChannel()).setCode(params.getCode())
                    .setBody("\"快递批量寄件\"").setSubject("快递寄件")
                    .setChannelExtras(channelExtras).setAmount(orderBatchShipmentsDO.getOrderAmount())
                    .setBizId(orderBatchShipmentsDO.getId() + "").setBizType(FeeConstants.WECHAT_PAY_BIZ_BATCHORDERPAY));
            return returnMap;
            // 用户支付宝支付返回支付参数
//                return getWechatPayParams(params, order);
        }

        return coreMap;
    }

    /**
     * 微信支付逻辑
     *
     * @param params
     * @param orderBatchShipmentsDO
     */
    private Map getWechatPayParams(MiniOrderPayReqVO params, OrderBatchShipmentsDO orderBatchShipmentsDO) {
//         获取到用户订阅消息模板
        Map coreMap = new HashMap();
//        1. 获取消息订阅模板
        coreMap.put("tempIds", orderService.getOrderTmplIds());
//        2. 校验微信渠道支付
        Assert.isTrue(paramApi.isOpenWechatPay(TenantContextHolder.getTenantId()), "微信支付渠道已关闭");
//        3. 校验系统支付参数
        List<PayAppDO> appList = payAppService.getAppList();
        Assert.isTrue(CollUtil.isNotEmpty(appList), "系统支付参数不正确");
//        4. 组装信息调用微信支付
        Long payment = buildWeChatPayment(params, orderBatchShipmentsDO, coreMap, appList);
//        5. 创建微信支付日志
        createWechatPayLog(payment, orderBatchShipmentsDO);
        return coreMap;

    }


    private void createWechatPayLog(Long payment, OrderBatchShipmentsDO orderBatchShipmentsDO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        WechatPayLogCreateReqVO createReqVO = new WechatPayLogCreateReqVO();
        createReqVO.setPayOrderId(payment);
        createReqVO.setBizType(FeeConstants.WECHAT_PAY_BIZ_BATCHORDERPAY);
        createReqVO.setMbrId(userId);
        createReqVO.setPayStatus(FeeConstants.PAY_STATUS_UNPAY);
        createReqVO.setPayAmount(orderBatchShipmentsDO.getOrderAmount());
        createReqVO.setBatchId(orderBatchShipmentsDO.getId());
        wechatPayLogService.createWechatPayLog(createReqVO);
    }

    /**
     * 调用微信支付接口
     *
     * @param params
     * @param orderBatchShipmentsDO
     * @param coreMap
     * @param appList
     * @return
     */
    private Long buildWeChatPayment(MiniOrderPayReqVO params, OrderBatchShipmentsDO orderBatchShipmentsDO, Map coreMap, List<PayAppDO> appList) {
        if (ObjectUtil.isEmpty(params.getPaymentChannel())) {
            params.setPaymentChannel(PayChannelEnum.WX_LITE.getCode());
        }
        if (ObjectUtil.isEmpty(params.getDisplayMode())) {
            params.setDisplayMode(PayOrderDisplayModeEnum.APP.getMode());
        }


        String subject = "快递批量寄件";
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Map<String, String> channelExtras = new HashMap<>();
        if (params.getPaymentChannel().equals(PayChannelEnum.WX_LITE.getCode())) {
            String openId = socialUserApi.getUserOpenId(userId, params.getCode(), SocialTypeEnum.WECHAT_MINI_APP.getType()).getData();
            // 如果openId为空，删除token让用户重新登录
            if (ObjectUtil.isEmpty(openId)) {
                // 删除用户和token
                memberUserApi.removeAccessTokenByUserId(userId);
                throw new ServiceException(new ErrorCode(401111, "当前登录已失效，请重新登录。"));
            }
            channelExtras.put("openid", openId);
        }


        channelExtras.put("batchId", orderBatchShipmentsDO.getId() + "");
//        order.getLastPrice().multiply(BigDecimal.valueOf(100)).intValue()
        Integer price = orderBatchShipmentsDO.getOrderAmount().multiply(BigDecimal.valueOf(100)).intValue();
//        Integer price = 2;
        channelExtras.put("type", "batchDeliveryV2");
        Long payOrderId = payOrderService.createOrder(new PayOrderCreateReqDTO().setAppId(appList.get(0).getId()).setUserIp(getClientIP()) // 支付应用
                .setMerchantOrderId(IdWorker.getIdStr()) // 业务的订单编号
                .setSubject(subject).setBody("快递批量寄件").setPrice(price) // 价格信息
                .setExpireTime(addTime(Duration.ofHours(2L))).setUserId(userId));

        PayOrderSubmitReqVO reqVO = new PayOrderSubmitReqVO();
        reqVO.setChannelCode(PayChannelEnum.WX_LITE.getCode());
        reqVO.setId(payOrderId);
        reqVO.setChannelExtras(channelExtras);
        reqVO.setDisplayMode(PayOrderDisplayModeEnum.APP.getMode());

        PayOrderSubmitRespVO payOrderSubmitRespVO = payOrderService.submitOrder(reqVO, getClientIP());
        String coreJson = payOrderSubmitRespVO.getDisplayContent();
//        coreMap = JSONUtil.toBean(coreJson, Map.class);
//        coreMap.put("package", coreMap.get("packageValue"));

        Map<String, Object> tempMap = JSONUtil.toBean(coreJson, Map.class);
        tempMap.put("package", tempMap.get("packageValue"));

        coreMap.putAll(tempMap);

        return payOrderId;
    }


    @Override
//    @DSTransactional
    public OrderBatchShipmentsDO paymentCallbackV2(String batchOrderId, String payType, Long bizId) {
        // 查询批量寄件订单
        OrderBatchShipmentsDO orderBatchShipmentsDO = orderBatchShipmentsMapper.selectById(batchOrderId);
        // 修改批量支付状态为已支付
        orderBatchShipmentsDO.setPayStatus("1");
        OrderBigVo orderBigVo = new OrderBigVo();
        List<OrderDO> orderDOList = orderService.getOrderBatch(null, Long.parseLong(batchOrderId));

        for (OrderDO bean : orderDOList) {
            // 构建扣费对象、构建订单对象
            orderBigVo = orderService.assembleBigVo(bean.getId());
            orderBigVo.setOrderId(bean.getId());
            if (ObjectUtil.isEmpty(bean.getOrderType()) || OrderConstants.ORDER_TYPE_USER.equals(bean.getOrderType())) {
                orderSubmitService.toThirdPartyOrder(bean, orderBigVo, payType, bizId);
            } else {
                // 后台批量下单
                orderSubmitService.toThirdPartyOrder(bean, orderBigVo, payType, null);
//                    }
            }
        }

        // 记录成功修改批量订单表的状态
        orderBatchShipmentsMapper.updateById(orderBatchShipmentsDO);

        return orderBatchShipmentsDO;
    }

    /**
     * 校验代理商
     */
    private void validateAgent() {
        Long tenantId = TenantContextHolder.getTenantId();
        CommonResult<TenantRespVO> tenantResult = tenantApi.getTenantInfo(tenantId);
        Assert.isTrue(tenantResult.isSuccess(), "查询加盟商信息失败");

        TenantRespVO tenantRespVO = tenantResult.getData();
        // 加盟商状态 冻结不能下单
        Assert.isTrue(CommonStatusEnum.ENABLE.getStatus().equals(tenantRespVO.getStatus()), "加盟商状态异常");

        // 余额
        BigDecimal balanceAmount = tenantRespVO.getBalanceAmount();
        // 余额限制阈值
        BigDecimal safetyDeposit = tenantRespVO.getSafetyDeposit();
        if (balanceAmount == null) {
            balanceAmount = BigDecimal.ZERO;
        }
        if (safetyDeposit == null) {
            safetyDeposit = BigDecimal.ZERO;
        }
        Assert.isTrue(balanceAmount.compareTo(safetyDeposit) > 0, "加盟商余额小于阈值");

        // 保证金
        BigDecimal marginAmount = tenantRespVO.getMarginAmount();
        if (marginAmount == null) {
            marginAmount = BigDecimal.ZERO;
        }
        Assert.isTrue(marginAmount.compareTo(new BigDecimal(100)) > 0, "加盟商保证金小于阈值");

    }

    /**
     * 后台批量寄件提交校验
     *
     * @param orderBatchShipmentsDO
     * @param tenantId
     */
    private void validateBatchSubmitParams(OrderBatchShipmentsDO orderBatchShipmentsDO, Long tenantId) {

        if (!"0".equals(orderBatchShipmentsDO.getPayStatus())) {
            throw new ServiceException(new ErrorCode(590112, "当前批量寄件已支付"));
        }
        long batchOrderId = orderBatchShipmentsDO.getId();

        String redisLockKey = ObjectUtil.isNotEmpty(tenantId) ? (tenantId + "_" + batchOrderId) : batchOrderId + "";
        if (!stringRedisTemplate.opsForValue().setIfAbsent("batchorder::lock::" + redisLockKey, "", 10, TimeUnit.SECONDS)) {
            throw new ServiceException(new ErrorCode(590111, "请不要重复提交"));
        }
    }

}
