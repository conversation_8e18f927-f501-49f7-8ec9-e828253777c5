# 快递订单补差明细表 (delivery_order_compensate) 业务分析

## 📋 表结构概述

`delivery_order_compensate` 表是快递云系统中用于记录订单补差明细的核心业务表，主要处理快递订单在重量变化时产生的费用补差和退款业务。

### 表结构定义

```sql
CREATE TABLE delivery_order_compensate (
    tenant_id           BIGINT,                    -- 租户号
    deleted             BOOLEAN        DEFAULT FALSE, -- 逻辑删除
    creator             VARCHAR(32),               -- 创建人
    create_time         TIMESTAMP,                 -- 创建时间
    updater             VARCHAR(32),               -- 更新人
    update_time         TIMESTAMP,                 -- 更新时间
    id                  BIGSERIAL PRIMARY KEY,     -- 主键ID
    order_id            VARCHAR(64),               -- 订单ID
    compensating_state  VARCHAR(6)     DEFAULT '1', -- 补差状态
    compensating_result VARCHAR(6)     DEFAULT '1', -- 补差结果
    compensation_amount DECIMAL(24, 6),            -- 补差金额
    compensation_type   VARCHAR(32),               -- 补差方式
    compensation_paid   DECIMAL(24, 6) DEFAULT 0,  -- 已补差金额
    compensation_time   TIMESTAMP,                 -- 补差时间
    remark              VARCHAR(512)               -- 补差备注
);
```

## 🔍 核心业务场景

### 1. 超重补差场景

当快递公司称重后发现实际重量超过下单时的重量，需要用户补缴差价：

```mermaid
graph TD
    A[用户下单] --> B[快递公司称重]
    B --> C{实际重量 > 预估重量?}
    C -->|是| D[计算补差金额]
    C -->|否| E[正常流程]
    D --> F[创建补差记录]
    F --> G[推送补差通知]
    G --> H[用户支付补差]
    H --> I[更新补差状态]
    I --> J[完成补差]
```

### 2. 超轻退款场景

当实际重量小于预估重量时，系统自动退还多收的费用：

```mermaid
graph TD
    A[快递公司称重] --> B{实际重量 < 预估重量?}
    B -->|是| C[计算退款金额]
    B -->|否| D[正常流程]
    C --> E[创建退款记录]
    E --> F[自动退款处理]
    F --> G[更新退款状态]
```

## 📊 状态枚举定义

### 补差状态 (compensating_state)

| 状态码 | 状态名称 | 描述 |
|--------|----------|------|
| 1 | 正常 | 无需补差或退款 |
| 2 | 超重补差 | 需要用户补缴差价 |
| 3 | 超轻退款 | 需要退还多收费用 |

### 补差结果 (compensating_result)

| 状态码 | 状态名称 | 描述 |
|--------|----------|------|
| 1 | 无需补差 | 重量正常，无需处理 |
| 2 | 未完成 | 补差或退款处理中 |
| 3 | 已完成 | 补差或退款已完成 |
| 4 | 重推 | 重新推送补差信息 |
| 5 | 已取消 | 补差已取消 |

### 补差方式 (compensation_type)

| 类型码 | 支付方式 | 描述 |
|--------|----------|------|
| 1 | 微信 | 微信支付补差 |
| 2 | 余额 | 账户余额支付 |
| 3 | 后台 | 后台人工处理 |

## 🏗️ 核心业务逻辑

### 1. 补差记录创建

#### 触发条件
- 快递公司回调重量变更信息
- 实际重量与预估重量存在差异
- 差异金额超过设定阈值

#### 创建逻辑
```java
public void updateOrderCompensateState(String orderId) {
    OrderDO orderDO = orderService.getOrder(orderId);
    
    // 查询是否已存在补差记录
    OrderCompensateDO existingRecord = orderCompensateMapper.selectOne(
        new LambdaQueryWrapperX<OrderCompensateDO>()
            .eq(OrderCompensateDO::getOrderId, orderId)
            .orderByDesc(OrderCompensateDO::getCreateTime)
            .last("limit 1")
    );
    
    // 根据业务规则决定新增或更新记录
    if (existingRecord == null) {
        // 新增补差记录
        createNewCompensateRecord(orderDO);
    } else {
        // 更新现有记录或创建新记录
        handleExistingRecord(existingRecord, orderDO);
    }
}
```

### 2. 补差完成处理

#### 超重补差完成
```java
public void compensationFinish(String orderId, BigDecimal amount, String payType) {
    // 查询补差记录（包括历史记录）
    OrderCompensateDO compensateRecord = findCompensateRecord(orderId);
    
    if (compensateRecord != null) {
        // 更新补差状态
        compensateRecord.setCompensatingResult("3"); // 已完成
        compensateRecord.setCompensationPaid(amount);
        compensateRecord.setCompensationType(payType);
        compensateRecord.setCompensationTime(LocalDateTime.now());
        
        // 保存更新
        updateCompensateRecord(compensateRecord);
        
        // 处理相关业务
        handleCompensationComplete(orderId, amount);
    }
}
```

#### 超轻退款处理
```java
public void refundAmount(String orderId, String payWay, BigDecimal refundAmount, Boolean isLessWeight) {
    // 创建退款记录
    OrderCompensateDO refundRecord = new OrderCompensateDO();
    refundRecord.setOrderId(orderId);
    refundRecord.setCompensatingState("3"); // 超轻退款
    refundRecord.setCompensatingResult("3"); // 已完成
    refundRecord.setCompensationAmount(refundAmount);
    refundRecord.setCompensationType(payWay);
    refundRecord.setCompensationTime(LocalDateTime.now());
    
    // 保存退款记录
    orderCompensateMapper.insert(refundRecord);
    
    // 处理退款业务逻辑
    processRefund(orderId, refundAmount, payWay);
}
```

### 3. 订单取消处理

当订单被取消时，需要处理未完成的补差记录：

```java
public void orderCancel(OrderDO orderDO) {
    // 查询最新的补差记录
    OrderCompensateDO latestRecord = orderCompensateMapper.selectOne(
        new LambdaQueryWrapperX<OrderCompensateDO>()
            .eq(OrderCompensateDO::getOrderId, orderDO.getId())
            .eq(OrderCompensateDO::getCompensatingState, "2") // 超重补差
            .eq(OrderCompensateDO::getCompensatingResult, "2") // 未完成
            .orderByDesc(OrderCompensateDO::getCreateTime)
            .last("limit 1")
    );
    
    if (latestRecord != null) {
        // 标记为已取消
        latestRecord.setCompensatingResult("5");
        orderCompensateMapper.updateById(latestRecord);
    }
}
```

## 🔄 业务流程详解

### 1. 重量变更回调处理流程

```java
// OrderCallbackServiceImpl.updateOrderCallback()
public void updateOrderCallback(OrderUpdateCallbackVo updateCallbackVo) {
    String orderId = updateCallbackVo.getOrderInfo().getId();
    
    // 更新订单基本信息
    updateOrderInfo(updateCallbackVo);
    
    // 处理补差逻辑
    // 规则：
    // 1. 无补差记录时，直接新增
    // 2. 状态金额一致时，不操作
    // 3. 状态一致金额不同时，修改操作
    // 4. 已完成状态不修改，新增记录
    // 5. 未完成状态变更时，标记重推并新增
    orderCompensateService.updateOrderCompensateState(orderId);
}
```

### 2. 补差金额计算逻辑

```java
private BigDecimal calculateCompensationAmount(BigDecimal oldCost, BigDecimal newCost, BigDecimal reliefAmount) {
    if (ObjectUtil.isEmpty(oldCost) || ObjectUtil.isEmpty(newCost)) {
        return BigDecimal.ZERO;
    }
    
    // 如果有减免金额，需要加上减免金额
    if (ObjectUtil.isNotEmpty(reliefAmount)) {
        oldCost = oldCost.add(reliefAmount);
    }
    
    // 计算补差金额
    // 负数表示需要补差，正数表示需要退款
    BigDecimal compensationAmount = newCost.subtract(oldCost);
    
    return compensationAmount;
}
```

### 3. 补差通知推送

```java
public String compensationPushNow(String orderId, String type) {
    OrderDO orderDO = orderMapper.selectOne(OrderDO::getId, orderId);
    
    // 验证订单状态
    if (!isValidForCompensation(orderDO)) {
        throw new ServiceException("当前订单状态不支持推送补差信息");
    }
    
    // 根据推送类型处理
    switch (type) {
        case "wechat":
            // 推送微信补差通知
            pushWechatCompensation(orderDO);
            break;
        case "sms":
            // 推送短信通知
            pushSmsCompensation(orderDO);
            break;
        default:
            throw new ServiceException("不支持的推送方式");
    }
    
    return "推送成功";
}
```

## 📈 数据查询与统计

### 1. 分页查询补差记录

```java
public PageResult<OrderCompensateDO> getOrderCompensatePage(OrderCompensatePageReqVO pageReqVO) {
    return orderCompensateMapper.selectPage(pageReqVO, 
        new LambdaQueryWrapperX<OrderCompensateDO>()
            .betweenIfPresent(OrderCompensateDO::getCreateTime, pageReqVO.getCreateTime())
            .eqIfPresent(OrderCompensateDO::getOrderId, pageReqVO.getOrderId())
            .eqIfPresent(OrderCompensateDO::getCompensatingState, pageReqVO.getCompensatingState())
            .eqIfPresent(OrderCompensateDO::getCompensatingResult, pageReqVO.getCompensatingResult())
            .orderByDesc(OrderCompensateDO::getCreateTime)
    );
}
```

### 2. 批量查询（包含历史数据）

```java
public List<OrderCompensateDO> getCompensateAllByOrderIds(List<String> orderIds) {
    // 使用 UNION ALL 查询当前表和历史表
    return orderCompensateMapper.getCompensateAllByOrderIds(orderIds);
}
```

对应的 SQL：
```xml
<select id="getCompensateAllByOrderIds" resultType="OrderCompensateDO">
    SELECT * FROM (
        SELECT * FROM delivery_order_compensate t1
        WHERE t1.order_id IN 
        <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        UNION ALL
        SELECT * FROM delivery_order_compensate_history t1
        WHERE t1.order_id IN 
        <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    ) t
    ORDER BY t.compensating_result DESC
</select>
```

## 🔧 技术实现特点

### 1. 多数据源支持

- **MySQL 数据源**: 主要业务数据存储
- **PostgreSQL 数据源**: 数据同步和备份

### 2. 历史数据管理

- 当前表：`delivery_order_compensate`
- 历史表：`delivery_order_compensate_history`
- 支持跨表查询和数据迁移

### 3. 分布式ID生成

```java
// 使用分段ID生成器
Long compensateId = segmentService.getId("delivery_order_compensate").getId();
orderCompensate.setId(compensateId);
```

### 4. 租户隔离

所有操作都基于 `tenant_id` 进行数据隔离，确保多租户环境下的数据安全。

## 🚨 异常处理与监控

### 1. 补差异常处理

- 支付失败自动重试
- 异常订单人工介入
- 补差超时提醒

### 2. 数据一致性保证

- 事务控制确保数据一致性
- 补偿机制处理异常情况
- 定时任务检查数据完整性

### 3. 业务监控指标

- 补差成功率
- 平均补差金额
- 补差处理时长
- 异常补差订单数量

## 📝 业务规则总结

1. **补差触发条件**: 实际重量与预估重量差异超过阈值
2. **状态流转**: 未完成 → 已完成/已取消
3. **金额计算**: 基于重量差异和费率计算补差金额
4. **支付方式**: 支持微信、余额、后台多种支付方式
5. **历史记录**: 保留完整的补差历史轨迹
6. **异常处理**: 完善的异常处理和补偿机制

这个补差系统是快递云业务的核心组件，确保了订单费用的准确性和用户体验的一致性。
