package com.hnyiti.kuaidi.module.pgsql.mq.message;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 通知消息
 *
 * <AUTHOR>
 */
@Data
@Builder
public class NotificationMessage {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 消息动作
     */
    private Action action;

    /**
     * 延时时间（毫秒）
     */
    private Long delayTime;

    /**
     * 任务数据
     */
    private Map<String, Object> taskData;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 消息动作枚举
     */
    public enum Action {
        SEND("SEND", "发送"),
        RESCHEDULE("RESCHEDULE", "重新调度"),
        CANCEL("CANCEL", "取消");

        private final String code;
        private final String name;

        Action(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
