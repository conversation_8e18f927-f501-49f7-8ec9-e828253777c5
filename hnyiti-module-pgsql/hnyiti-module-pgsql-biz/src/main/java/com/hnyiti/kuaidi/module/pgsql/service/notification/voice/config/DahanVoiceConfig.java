package com.hnyiti.kuaidi.module.pgsql.service.notification.voice.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

/**
 * 大汉三通语音配置
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "dahan.voice")
public class DahanVoiceConfig {

    /**
     * 用户账号
     */
    private String account = "dh8528";

    /**
     * 账号密码（原始密码，会自动进行MD5加密）
     */
    private String rawPassword = "your_password";

    /**
     * 语音提交接口地址
     */
    private String submitUrl = "http://voice.3tong.net/json/voiceSms/SubmitVoc";

    /**
     * 状态报告获取接口地址
     */
    private String reportUrl = "http://voice.3tong.net/json/voiceSms/GetReport";

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 60000;

    /**
     * 是否启用语音通知
     */
    private boolean enabled = true;

    /**
     * 默认发音人
     * B0=汉小美, B1=汉小宇, B3=汉逍遥, B4=汉丫丫
     */
    private String defaultSpeaker = "B0";

    /**
     * 默认播放次数
     */
    private int defaultPlayTimes = 1;

    /**
     * 最大重试次数
     */
    private int maxRetryTimes = 3;

    /**
     * 获取MD5加密后的密码
     *
     * @return MD5加密后的密码（32位小写）
     */
    public String getPassword() {
        if (rawPassword == null || rawPassword.isEmpty()) {
            throw new IllegalStateException("大汉三通语音密码未配置");
        }

        // 使用Spring的DigestUtils进行MD5加密，返回32位小写
        return DigestUtils.md5DigestAsHex(rawPassword.getBytes()).toLowerCase();
    }

    /**
     * 验证配置是否完整
     *
     * @return 是否配置完整
     */
    public boolean isConfigValid() {
        return account != null && !account.isEmpty() &&
                rawPassword != null && !rawPassword.isEmpty() &&
                submitUrl != null && !submitUrl.isEmpty() &&
                reportUrl != null && !reportUrl.isEmpty();
    }

    /**
     * 获取配置摘要信息（用于日志，不包含敏感信息）
     *
     * @return 配置摘要
     */
    public String getConfigSummary() {
        return String.format("DahanVoiceConfig{account='%s', submitUrl='%s', reportUrl='%s', enabled=%s, speaker='%s'}",
                account, submitUrl, reportUrl, enabled, defaultSpeaker);
    }
}
