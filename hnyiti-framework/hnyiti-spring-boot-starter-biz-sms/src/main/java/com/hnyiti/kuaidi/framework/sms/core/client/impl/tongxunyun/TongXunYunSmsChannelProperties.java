package com.hnyiti.kuaidi.framework.sms.core.client.impl.tongxunyun;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.hnyiti.kuaidi.framework.sms.core.property.SmsChannelProperties;
import lombok.Data;

/**
 * 通讯云短信配置实现类
 *
 * <AUTHOR>
 */
@Data
public class TongXunYunSmsChannelProperties extends SmsChannelProperties {

    /**
     * 账号
     */
    private String account;

    /**
     * 密码（MD5加密后的32位小写）
     */
    private String password;

    /**
     * 构建通讯云短信配置
     *
     * @param properties 基础配置
     * @return 通讯云短信配置
     */
    public static TongXunYunSmsChannelProperties build(SmsChannelProperties properties) {
        if (properties instanceof TongXunYunSmsChannelProperties) {
            return (TongXunYunSmsChannelProperties) properties;
        }

        TongXunYunSmsChannelProperties result = BeanUtil.toBean(properties, TongXunYunSmsChannelProperties.class);

        // 设置账号和密码
        result.setAccount(properties.getApiKey());
        result.setPassword(properties.getApiSecret());

        Assert.notEmpty(result.getAccount(), "通讯云短信账号不能为空");
        Assert.notEmpty(result.getPassword(), "通讯云短信密码不能为空");

        return result;
    }
}
