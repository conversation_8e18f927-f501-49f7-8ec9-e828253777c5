# 多阶段通知配置
notification:
  compensation:
    # 延时配置
    delays:
      sms: 2h        # 短信延时2小时
      phone: 6h      # 电话在短信后6小时
    
    # 工作时间配置
    work-time:
      start: "09:00"
      end: "23:59"
    
    # 重试配置
    retry:
      max-attempts: 3
      interval: 30m
    
    # 渠道开关
    channels:
      mini-program: true
      wechat-mp: true
      sms: true
      phone-call: true
    
    # 模板配置
    templates:
      mini-program:
        compensation: "compensation_mini_template"
      wechat-mp:
        compensation: "compensation_mp_template"
      sms:
        compensation: "SMS_COMPENSATION_TEMPLATE"
      phone-call:
        compensation: "VOICE_COMPENSATION_TEMPLATE"

# Spring Cloud Stream 配置
spring:
  cloud:
    stream:
      bindings:
        # 立即通知
        compensation-immediate-notify:
          destination: compensation-immediate-notify
          content-type: application/json
          group: pgsql-notification-group
        
        # 延时通知
        compensation-delayed-notify:
          destination: compensation-delayed-notify
          content-type: application/json
          group: pgsql-notification-group
        
        # 重新调度通知
        compensation-reschedule-notify:
          destination: compensation-reschedule-notify
          content-type: application/json
          group: pgsql-notification-group
      
      # RocketMQ 配置
      rocketmq:
        binder:
          name-server: ${rocketmq.name-server:127.0.0.1:9876}
        bindings:
          compensation-immediate-notify:
            consumer:
              orderly: true
              delay-level-when-next-consume: 1
          compensation-delayed-notify:
            consumer:
              orderly: true
              delay-level-when-next-consume: 1
          compensation-reschedule-notify:
            consumer:
              orderly: true
              delay-level-when-next-consume: 1

# 日志配置
logging:
  level:
    com.hnyiti.kuaidi.module.pgsql.service.notification: INFO
    com.hnyiti.kuaidi.module.pgsql.mq: INFO
