# PostgreSQL 模块超重补差消息推送逻辑分析

## 📋 概述

`hnyiti-module-pgsql-biz` 模块主要负责将订单数据同步到 PostgreSQL 数据库，并在同步过程中处理超重补差相关的消息推送逻辑。该模块通过消息队列机制实现异步处理和企业微信通知。

## 🏗️ 核心架构

### 消息流转架构

```mermaid
graph TD
    A[MySQL订单数据变更] --> B[发送MQ消息]
    B --> C[PostgreSQL消费者接收]
    C --> D[订单数据同步]
    D --> E{同步成功?}
    E -->|是| F[保存补差明细]
    E -->|否| G[发送异常通知]
    F --> H[企业微信通知]
    G --> I[企业微信异常通知]
```

## 🔄 消息队列配置

### 输入通道定义

**文件**: `KuaiDiInput.java`

```java
public interface KuaiDiInput {
    String ORDER_SYNC_PGSQL_INPUT = "order-sync-pgsql-input";
    String MEMBER_SYNC_PGSQL_INPUT = "member-sync-pgsql-input";
    
    @Input(ORDER_SYNC_PGSQL_INPUT)
    SubscribableChannel orderSyncPgsqlInput();
    
    @Input(MEMBER_SYNC_PGSQL_INPUT)
    SubscribableChannel memberSyncPgsqlInput();
}
```

### 输出通道定义

**文件**: `PgsqlOutput.java`

```java
public interface PgsqlOutput {
    String WECOM_NOTIFY_OUTPUT = "wecom-notify-output";
    
    @Output(WECOM_NOTIFY_OUTPUT)
    MessageChannel wecomNotifyOutput();
}
```

## 📨 核心消息处理逻辑

### 1. 订单同步消息消费

**文件**: `OrderConsumer.java`  
**方法**: `orderSyncPgsql(Message<Map<String, String>> message)`

#### 处理流程

1. **消息接收**: 监听 `order-sync-pgsql-input` 通道
2. **参数提取**: 从消息中提取 `orderId`、`type`、`tenantId`
3. **分布式锁**: 使用 Redisson 实现订单级别的分布式锁
4. **数据同步**: 调用 `DeliveryOrderService` 进行数据同步
5. **异常处理**: 同步失败时发送企业微信异常通知

```java
@StreamListener(KuaiDiInput.ORDER_SYNC_PGSQL_INPUT)
public void orderSyncPgsql(Message<Map<String, String>> message) {
    log.info("开始同步订单到PostgreSQL");
    long begin = System.currentTimeMillis();
    try {
        toOrderSyncPgsql(message);
    } catch (Exception e) {
        log.error("处理消息时出错：{}", e.getMessage(), e);
    } finally {
        log.info("同步订单花费时间: {}", System.currentTimeMillis() - begin);
    }
}
```

### 2. 分布式锁机制

```java
// 构建锁的key
String lockKey = "order_sync_lock:" + orderId + ":" + tenantId;
// 获取RLock实例
RLock lock = redissonClient.getLock(lockKey);
// 尝试获取锁，等待0秒，持有锁3秒
boolean locked = lock.tryLock(0, 3, TimeUnit.SECONDS);
```

**锁的作用**:
- 防止同一订单的并发同步
- 确保数据一致性
- 避免重复处理

### 3. 订单数据同步

**文件**: `DeliveryOrderService.java`  
**方法**: `toSaveOrderCompensate(List<OrderCompensateDO> orderCompensateList, String orderId, Long tenantId)`

#### 补差明细同步逻辑

```java
private void toSaveOrderCompensate(List<OrderCompensateDO> orderCompensateList, String orderId, Long tenantId) {
    log.info("开始保存订单补差明细，订单ID：{}，数量：{}", orderId, 
             orderCompensateList != null ? orderCompensateList.size() : 0);

    if (ObjectUtil.isEmpty(orderCompensateList) || orderCompensateList.size() <= 0) {
        log.info("订单补差明细为空，无需保存，订单ID：{}", orderId);
        return;
    }

    // 数据转换和保存
    List<DeliveryOrderCompensate> compensateList = new ArrayList<>();
    for (OrderCompensateDO compensate : orderCompensateList) {
        DeliveryOrderCompensate deliveryOrderCompensate = new DeliveryOrderCompensate();
        // 设置补差信息
        deliveryOrderCompensate.setOrderId(compensate.getOrderId());
        deliveryOrderCompensate.setCompensatingState(compensate.getCompensatingState());
        deliveryOrderCompensate.setCompensatingResult(compensate.getCompensatingResult());
        deliveryOrderCompensate.setCompensationAmount(compensate.getCompensationAmount());
        deliveryOrderCompensate.setCompensationType(compensate.getCompensationType());
        deliveryOrderCompensate.setCompensationPaid(compensate.getCompensationPaid());
        deliveryOrderCompensate.setCompensationTime(compensate.getCompensationTime());
        deliveryOrderCompensate.setRemark(compensate.getRemark());
        deliveryOrderCompensate.setTenantId(tenantId);
        
        compensateList.add(deliveryOrderCompensate);
    }
    
    // 先删后增策略
    compensateService.deleteByOrderIdAndTenantId(orderId, tenantId);
    compensateService.saveCompensateBatch(compensateList);
}
```

## 🚨 异常处理与通知

### 1. 异常消息发送

**文件**: `OrderConsumer.java`  
**方法**: `sendAnomalyMessage(String bizId, Long tenantId, Exception e)`

```java
public boolean sendAnomalyMessage(String bizId, Long tenantId, Exception e) {
    WeChatGroupNotifyReq weChatGroupNotifyReq = new WeChatGroupNotifyReq();
    weChatGroupNotifyReq.setExecuteMethod(21);
    weChatGroupNotifyReq.setBizType("mq_pgsql_error");
    weChatGroupNotifyReq.setBizId(bizId);
    weChatGroupNotifyReq.setTenantId(tenantId);
    weChatGroupNotifyReq.setPushType(21);
    
    String errorMsg = "";
    try {
        // 获取详细的堆栈信息
        errorMsg = getDetailedExceptionInfo(e);
    } catch (Exception ex) {
        log.error("获取错误堆栈信息失败：", e.getMessage());
    }
    weChatGroupNotifyReq.setExceptionMessage(errorMsg);
    log.info("pgsql入库异常消息：" + weChatGroupNotifyReq);
    return pgsqlProducer.wecomNotifyOutput(weChatGroupNotifyReq);
}
```

### 2. 企业微信通知生产者

**文件**: `PgsqlProducer.java`  
**方法**: `wecomNotifyOutput(WeChatGroupNotifyReq weChatGroupNotifyReq)`

```java
public boolean wecomNotifyOutput(WeChatGroupNotifyReq weChatGroupNotifyReq) {
    log.info("pgsql 保存信息入库异常消息：" + weChatGroupNotifyReq);
    // 延迟5秒
    int delayTimeLevel = 1;
    return logOuput.wecomNotifyOutput().send(MessageBuilder.withPayload(weChatGroupNotifyReq)
            .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, delayTimeLevel)
            .build());
}
```

## 📊 补差明细数据模型

### PostgreSQL 补差明细表结构

**实体类**: `DeliveryOrderCompensate`

```java
public class DeliveryOrderCompensate {
    private Long id;                    // 主键ID
    private String orderId;             // 订单ID
    private String compensatingState;   // 补差状态(1:正常，2：超重补差,3:超轻退款)
    private String compensatingResult;  // 补差结果（1：无需补差，2：未完成，3：已完成，4：重推，5：已取消）
    private BigDecimal compensationAmount; // 补差金额、退款金额
    private String compensationType;    // 补差方式（1：微信，2：余额，3：后台）
    private BigDecimal compensationPaid; // 已补差金额
    private LocalDateTime compensationTime; // 补差时间
    private String remark;              // 补差备注
    private Long tenantId;              // 租户ID
    private LocalDateTime createTime;   // 创建时间
    private LocalDateTime updateTime;   // 更新时间
}
```

### 补差状态枚举

| 状态码 | 状态名称 | 描述 |
|--------|----------|------|
| 1 | 正常 | 无需补差或退款 |
| 2 | 超重补差 | 需要用户补缴差价 |
| 3 | 超轻退款 | 需要退还多收费用 |

### 补差结果枚举

| 状态码 | 状态名称 | 描述 |
|--------|----------|------|
| 1 | 无需补差 | 重量正常，无需处理 |
| 2 | 未完成 | 补差或退款处理中 |
| 3 | 已完成 | 补差或退款已完成 |
| 4 | 重推 | 重新推送补差信息 |
| 5 | 已取消 | 补差已取消 |

## 🔄 业务流程详解

### 1. 超重补差数据同步流程

```mermaid
sequenceDiagram
    participant MySQL as MySQL数据库
    participant MQ as 消息队列
    participant PGSQL as PostgreSQL模块
    participant WeChat as 企业微信
    
    MySQL->>MQ: 发送订单同步消息
    MQ->>PGSQL: 消费订单同步消息
    PGSQL->>PGSQL: 获取分布式锁
    PGSQL->>MySQL: 调用API获取订单数据
    PGSQL->>PGSQL: 同步订单基础信息
    PGSQL->>PGSQL: 同步补差明细信息
    alt 同步成功
        PGSQL->>PGSQL: 释放锁
    else 同步失败
        PGSQL->>MQ: 发送异常通知消息
        MQ->>WeChat: 推送异常通知
        PGSQL->>PGSQL: 释放锁
    end
```

### 2. 补差明细处理策略

#### 先删后增策略
```java
// 先删除原有补差明细
compensateService.deleteByOrderIdAndTenantId(orderId, tenantId);
// 再批量保存新的补差明细
compensateService.saveCompensateBatch(compensateList);
```

**优点**:
- 确保数据一致性
- 避免重复数据
- 简化更新逻辑

**缺点**:
- 可能存在短暂的数据空窗期
- 对数据库性能有一定影响

## 🛡️ 容错机制

### 1. 分布式锁超时机制
- 锁等待时间: 0秒（立即获取）
- 锁持有时间: 3秒
- 避免死锁和长时间阻塞

### 2. 异常重试机制
- 消息队列自带重试机制
- 异常时发送企业微信通知
- 详细的错误日志记录

### 3. 数据一致性保证
- 事务控制确保原子性
- 分布式锁防止并发问题
- 先删后增保证数据准确性

## 📈 监控与日志

### 关键日志记录点

1. **消息接收**: 记录消息内容和处理时间
2. **锁获取**: 记录锁的获取和释放状态
3. **数据同步**: 记录同步的数据量和结果
4. **异常处理**: 详细记录异常信息和堆栈
5. **企业微信通知**: 记录通知发送状态

### 性能监控指标

- 消息处理耗时
- 数据同步成功率
- 异常发生频率
- 锁竞争情况

## 🔧 配置要点

### 消息队列配置
- 消费者组配置
- 重试机制配置
- 延迟消息配置

### 数据库配置
- PostgreSQL 连接池配置
- 事务隔离级别配置
- 批量操作优化配置

这个 PostgreSQL 模块的超重补差消息推送逻辑设计完善，具备良好的容错性和监控能力，确保了数据同步的可靠性和业务流程的完整性。
