package com.hnyiti.kuaidi.module.delivery.controller.app.h5;

import cn.hutool.core.util.ObjectUtil;
import com.aliyun.tea.utils.StringUtils;
import com.hnyiti.kuaidi.framework.common.exception.ErrorCode;
import com.hnyiti.kuaidi.framework.common.exception.ServiceException;
import com.hnyiti.kuaidi.framework.common.pojo.CommonResult;
import com.hnyiti.kuaidi.framework.tenant.core.context.TenantContextHolder;
import com.hnyiti.kuaidi.module.member.api.user.MemberUserApi;
import com.hnyiti.kuaidi.module.member.api.user.dto.MemberUserRespDTO;
import com.hnyiti.kuaidi.module.member.enums.ErrorCodeConstants;
import com.hnyiti.kuaidi.module.member.service.inviteurl.GenerateQrCodeService;
import com.hnyiti.kuaidi.module.member.service.inviteurl.InviteUrlService;
import com.hnyiti.kuaidi.module.system.api.sms.SmsCodeApi;
import com.hnyiti.kuaidi.module.system.api.sms.dto.code.SmsCodeSendReqDTO;
import com.hnyiti.kuaidi.module.system.api.social.SocialUserApi;
import com.hnyiti.kuaidi.module.system.api.social.dto.*;
import com.hnyiti.kuaidi.module.system.enums.sms.SmsSceneEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import static com.hnyiti.kuaidi.framework.common.util.servlet.ServletUtils.getClientIP;


/**
 * 系统模块接口
 */
@Slf4j
@RestController
@RequestMapping("/mini/v2/web/sys")
@EnableAsync // 启用异步支持
public class WebSystemController {
    @Resource
    private SocialUserApi socialUserApi;
    @Resource
    private InviteUrlService inviteUrlService;
    @Resource
    private GenerateQrCodeService generateQrCodeService;
    @Resource // 保证 aj-captcha 的 SPI 创建时的注入
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MemberUserApi memberUserApi;
    @Resource
    private SmsCodeApi smsCodeApi;
    @Resource
    private PasswordEncoder passwordEncoder;

    /**
     * 手机号,密码登录
     *
     * @param appAuthLoginReqVO
     * @return
     */
    @PermitAll
    @PostMapping("/mobileLogin")
//    @AutoLog(value = "web手机号登陆", businessType = LogBusinessType.KUAIDI)
    public CommonResult mobileLogin(@RequestBody AppAuthLoginReqVO appAuthLoginReqVO) {
        log.info("mobileLogin, {}", appAuthLoginReqVO.getMobile());

        if(ObjectUtil.isEmpty(appAuthLoginReqVO) && ObjectUtil.isEmpty(appAuthLoginReqVO.getMobile())){
            throw new ServiceException(new ErrorCode(20510, "手机号不能为空。"));
        }

        String key = "";
        try {
            Long tenantId = TenantContextHolder.getTenantId();
            if (ObjectUtil.isNotEmpty(tenantId)) {
                key = "lock::mobileLogin::" + tenantId + "_" + appAuthLoginReqVO.getMobile();
            }
            // 防止并发-加锁
            ValueOperations<String, String> valueOperations = stringRedisTemplate.opsForValue();
            if (!valueOperations.setIfAbsent(key, "LOCKED", Duration.ofSeconds(60))) {
                log.info("当前用户正在登录中{}", key);
                throw new ServiceException(new ErrorCode(20510, "正在登录中，请稍等。"));
            }

//            AppAuthLoginReqVO appAuthLoginReqVO = new AppAuthLoginReqVO();
//            appAuthLoginReqVO.setMobile(mobile);
//            appAuthLoginReqVO.setPassword(password);

            CommonResult<AppAuthLoginRespVO> commonResult = socialUserApi.mobileLogin(appAuthLoginReqVO);
            if (commonResult.isError()) {
//                socialUserApi.miniCodeLoginV2("1654165","15646546","12213");
                return commonResult;
            }

            AppAuthLoginRespVO data = commonResult.getData();
            Long userId = data.getUserId();
            String accessToken = data.getAccessToken();

            Map dataMap = new HashMap();
            dataMap.put("userId", userId);
            dataMap.put("token", accessToken);

            // 获取openId
//            String openId = socialUserApi.getUserOpenId(userId, null).getData();
//            dataMap.put("openId", openId);

            // generateQrCodeService.generate(userId);

            if (!inviteUrlService.hasInviteUrl(userId)) {
                // 异步生成二维码，不阻塞当前请求
                generateQrCodeAsync(userId);
            }

            return CommonResult.success(dataMap);
        } catch (Exception exception) {
            log.error("登录失败, mobileLogin, {}", exception);
            return CommonResult.error(ErrorCodeConstants.AUTH_WEIXIN_MINI_APP_PHONE_CODE_ERROR);
        } finally {
            if (ObjectUtil.isNotEmpty(key)) {
                stringRedisTemplate.delete(key);
            }
        }
    }

    /**
     * 标记异步发送短信
     * @param sendReqDTO
     */
    @Async
    public void sendSmsCodeAsync(SmsCodeSendReqDTO sendReqDTO) {
        smsCodeApi.sendSmsCode(sendReqDTO);
    }

    /**
     * 用户注册获取短信验证码
     * @param reqVO
     * @return
     */
    @PermitAll
    @PostMapping("/userSmsRegister")
    public CommonResult userSmsRegister(@RequestBody H5GetSmsCodeReqVO reqVO) {
        String mobile = reqVO.getMobile();
        // 发送短信
        SmsCodeSendReqDTO sendReqDTO = new SmsCodeSendReqDTO().setCreateIp(getClientIP()).setMobile(mobile).setScene(4);
        Map<String, Object> smsParam = new HashMap<>();

        // 验证码
        String templateCode = "9999";
        String operate = SmsSceneEnum.getCodeByScene(4).getDescription();
        smsParam.put("code", templateCode);
        smsParam.put("time",5);
        smsParam.put("operate", operate);
        sendReqDTO.setData(smsParam);

        // 异步发送短信验证码
        sendSmsCodeAsync(sendReqDTO);
        return CommonResult.success(sendReqDTO);
    }

    /**
     * 用户注册
     * @param reqVO
     * @return
     */
    @PermitAll
    @PostMapping("/userRegister")
    public CommonResult userRegister(@Valid @RequestBody AppAuthRegisterReqVO reqVO) {
        String mobile = reqVO.getMobile();
        // 校验手机号是否被注册
        MemberUserRespDTO user = memberUserApi.getUserByMobile(mobile);
        if (null != user) {
            return CommonResult.error(ErrorCodeConstants.USER_EXISTS);
        }

        // 密码加密
//        byte[] decodedBytes = Base64.getDecoder().decode(reqVO.getPassword());
//        String password = new String(decodedBytes);
        reqVO.setPassword(reqVO.getPassword());

        // 校验验证码是否正确
        CommonResult<AppAuthLoginRespVO> commonResult = socialUserApi.userH5Register(reqVO);
        if (ObjectUtil.isNotEmpty(commonResult.getData())) {
            // 验证成功，返回登录对象
            AppAuthLoginRespVO data = commonResult.getData();
            Long userId = data.getUserId();
            String accessToken = data.getAccessToken();

            Map dataMap = new HashMap();
            dataMap.put("userId", userId);
            dataMap.put("token", accessToken);
            if (!inviteUrlService.hasInviteUrl(userId)) {
                generateQrCodeService.generate(userId);
            }
            return CommonResult.success(dataMap);
        }else {
            return CommonResult.error(new ErrorCode(1004103009, commonResult.getMsg()));
        }
    }


    /**
     * 获取短信快捷登录验证码
     * @param reqVO
     * @return
     */
    @PermitAll
    @PostMapping("/userSmsLogin")
    public CommonResult userLoginSms(@RequestBody H5GetSmsCodeReqVO reqVO) {
        /**
         * 【牛马寄件】您正在进行{operate}，验证码{code}，在{time}分钟内有效。若非本人操作，请忽略本短信。
         */
        String mobile = reqVO.getMobile();
        MemberUserRespDTO user = memberUserApi.getUserByMobile(mobile);
        if (null == user || ObjectUtil.isEmpty(user)) {
            return CommonResult.error(ErrorCodeConstants.USER_NOT_EXISTS);
        }

        // 发送短信
        SmsCodeSendReqDTO sendReqDTO = new SmsCodeSendReqDTO().setCreateIp(getClientIP()).setMobile(mobile).setScene(1);
        Map<String, Object> smsParam = new HashMap<>();

        // 验证码
        String templateCode = "9999";
        String operate = SmsSceneEnum.getCodeByScene(1).getDescription();
        smsParam.put("code", templateCode);
        smsParam.put("time",5);
        smsParam.put("operate", operate);
        sendReqDTO.setData(smsParam);

        // 异步发送短信验证码
        sendSmsCodeAsync(sendReqDTO);
        return CommonResult.success(sendReqDTO);
    }


    /**
     * 快捷登录-获取验证码后校验验证码登录
     * @param reqVO
     * @return
     */
    @PermitAll
    @PostMapping("/quickLogin")
    public CommonResult userQuickLogin(@RequestBody H5GetSmsCodeReqVO reqVO) {
        if (StringUtils.isEmpty(reqVO.getVerificationCode())) {
            return CommonResult.error(ErrorCodeConstants.VCODE_NOT_EXISTS);
        }

        CommonResult<AppAuthLoginRespVO> commonResult = socialUserApi.smsLogin(new AppAuthLoginReqVO()
                .setCode(reqVO.getVerificationCode()).setMobile(reqVO.getMobile()));
        if (ObjectUtil.isNotEmpty(commonResult.getData())) {
            // 验证成功，返回登录对象
            AppAuthLoginRespVO data = commonResult.getData();
            Long userId = data.getUserId();
            String accessToken = data.getAccessToken();

            Map dataMap = new HashMap();
            dataMap.put("userId", userId);
            dataMap.put("token", accessToken);
            if (!inviteUrlService.hasInviteUrl(userId)) {
                generateQrCodeService.generate(userId);
            }
            return CommonResult.success(dataMap);
        }else {
            return CommonResult.error(new ErrorCode(1004103009, commonResult.getMsg()));
        }
    }


    /**
     * 用户忘记密码获取短信验证码
     *
     * @param reqVO
     * @return
     */
    @PermitAll
    @PostMapping("/userSmsForget")
    public CommonResult userSmsForget(@RequestBody H5GetSmsCodeReqVO reqVO) {
        String mobile = reqVO.getMobile();
        // 发送短信
        SmsCodeSendReqDTO sendReqDTO = new SmsCodeSendReqDTO().setCreateIp(getClientIP()).setMobile(mobile).setScene(3);
        Map<String, Object> smsParam = new HashMap<>();

        // 验证码
        String templateCode = "9999";
        String operate = SmsSceneEnum.getCodeByScene(4).getDescription();
        smsParam.put("code", templateCode);
        smsParam.put("time", 5);
        smsParam.put("operate", operate);
        sendReqDTO.setData(smsParam);

        // 异步发送短信验证码
        sendSmsCodeAsync(sendReqDTO);
        return CommonResult.success(sendReqDTO);
    }

    /**
     * 用户修改密码
     *
     * @param reqVO
     * @return
     */
    @PermitAll
    @PostMapping("/resetPassword")
    public CommonResult resetPassword(@RequestBody ResetPasswordReqVO reqVO) {

        if (ObjectUtil.isEmpty(reqVO.getCode())) {
            return CommonResult.error(ErrorCodeConstants.VCODE_NOT_NULL);
        }

        if (ObjectUtil.isEmpty(reqVO.getPassword())) {
            return CommonResult.error(ErrorCodeConstants.USER_PWD_NOT_NULL);
        }


        // 根据验证码修改密码
        socialUserApi.resetPassword(reqVO);
        return CommonResult.success(1);
    }

    /**
     * 异步生成二维码
     *
     * @param userId 用户ID
     */
    @Async
    public void generateQrCodeAsync(Long userId) {
        try {
            log.info("开始异步生成用户二维码，userId: {}", userId);
            generateQrCodeService.generate(userId);
            log.info("异步生成用户二维码完成，userId: {}", userId);
        } catch (Exception e) {
            log.error("异步生成用户二维码失败，userId: {}", userId, e);
        }
    }
}
