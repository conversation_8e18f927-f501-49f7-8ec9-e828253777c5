package com.hnyiti.kuaidi.module.pgsql.api.notification;

import com.hnyiti.kuaidi.framework.common.pojo.CommonResult;
import com.hnyiti.kuaidi.module.pgsql.api.notification.vo.CreateNotificationTaskApiReqVO;
import com.hnyiti.kuaidi.module.pgsql.api.notification.vo.NotificationTaskApiVO;
import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.dal.mapper.notification.NotificationTaskMapper;
import com.hnyiti.kuaidi.module.pgsql.service.notification.NotificationTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.hnyiti.kuaidi.framework.common.pojo.CommonResult.success;

/**
 * 通知任务 API 接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class NotificationTaskApiImpl implements NotificationTaskApi {

    @Autowired
    private NotificationTaskService notificationTaskService;

    @Autowired
    private NotificationTaskMapper notificationTaskMapper;

    @Override
    public CommonResult<Long> createNotificationTask(CreateNotificationTaskApiReqVO reqVO) {
        log.info("[createNotificationTask] API调用创建通知任务，订单ID: {}, 任务类型: {}",
                reqVO.getOrderId(), reqVO.getTaskType());

        try {
            Long taskId = notificationTaskService.createNotificationTask(
                    reqVO.getOrderId(),
                    reqVO.getTenantId(),
                    reqVO.getTaskType(),
                    reqVO.getScheduledTime(),
                    reqVO.getTaskData()
            );

            log.info("[createNotificationTask] API创建通知任务成功，任务ID: {}", taskId);
            return success(taskId);

        } catch (Exception e) {
            log.error("[createNotificationTask] API创建通知任务失败", e);
            return CommonResult.error(500, "创建通知任务失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<NotificationTaskApiVO> getNotificationTask(Long id) {
        log.info("[getNotificationTask] API查询通知任务，任务ID: {}", id);

        try {
            NotificationTask task = notificationTaskService.getNotificationTaskById(id);
            NotificationTaskApiVO vo = convertToApiVO(task);

            log.info("[getNotificationTask] API查询通知任务成功，任务ID: {}", id);
            return success(vo);

        } catch (Exception e) {
            log.error("[getNotificationTask] API查询通知任务失败，任务ID: {}", id, e);
            return CommonResult.error(500, "查询通知任务失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Boolean> updateTaskStatus(Long id, String taskStatus) {
        log.info("[updateTaskStatus] API更新任务状态，任务ID: {}, 状态: {}", id, taskStatus);

        try {
            notificationTaskService.updateTaskStatus(id, taskStatus);

            log.info("[updateTaskStatus] API更新任务状态成功，任务ID: {}", id);
            return success(true);

        } catch (Exception e) {
            log.error("[updateTaskStatus] API更新任务状态失败，任务ID: {}", id, e);
            return CommonResult.error(500, "更新任务状态失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<NotificationTaskApiVO>> getNotificationTasksByOrderId(String orderId, Long tenantId) {
        log.info("[getNotificationTasksByOrderId] API查询订单通知任务，订单ID: {}, 租户ID: {}", orderId, tenantId);

        try {
            List<NotificationTask> tasks = notificationTaskMapper.selectByOrderId(orderId, tenantId);
            List<NotificationTaskApiVO> voList = tasks.stream()
                    .map(this::convertToApiVO)
                    .collect(Collectors.toList());

            log.info("[getNotificationTasksByOrderId] API查询订单通知任务成功，任务数量: {}", voList.size());
            return success(voList);

        } catch (Exception e) {
            log.error("[getNotificationTasksByOrderId] API查询订单通知任务失败，订单ID: {}", orderId, e);
            return CommonResult.error(500, "查询订单通知任务失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Long> createSmsNotificationTask(String orderId, Long tenantId, String mobile,
                                                        String content, LocalDateTime scheduledTime) {
        log.info("[createSmsNotificationTask] API创建短信通知任务，订单ID: {}, 手机号: {}", orderId, mobile);

        try {
            Map<String, Object> taskData = new HashMap<>();
            taskData.put("mobile", mobile);
            taskData.put("content", content);
            taskData.put("templateId", "sms_template");

            Long taskId = notificationTaskService.createNotificationTask(
                    orderId, tenantId, NotificationTask.TaskType.SMS.getCode(), scheduledTime, taskData);

            log.info("[createSmsNotificationTask] API创建短信通知任务成功，任务ID: {}", taskId);
            return success(taskId);

        } catch (Exception e) {
            log.error("[createSmsNotificationTask] API创建短信通知任务失败", e);
            return CommonResult.error(500, "创建短信通知任务失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Long> createMiniProgramNotificationTask(String orderId, Long tenantId, String openid,
                                                                String templateId, Map<String, Object> templateData,
                                                                LocalDateTime scheduledTime) {
        log.info("[createMiniProgramNotificationTask] API创建小程序通知任务，订单ID: {}, openid: {}", orderId, openid);

        try {
            Map<String, Object> taskData = new HashMap<>();
            taskData.put("openid", openid);
            taskData.put("templateId", templateId);
            taskData.put("data", templateData);

            Long taskId = notificationTaskService.createNotificationTask(
                    orderId, tenantId, NotificationTask.TaskType.MINI_PROGRAM.getCode(), scheduledTime, taskData);

            log.info("[createMiniProgramNotificationTask] API创建小程序通知任务成功，任务ID: {}", taskId);
            return success(taskId);

        } catch (Exception e) {
            log.error("[createMiniProgramNotificationTask] API创建小程序通知任务失败", e);
            return CommonResult.error(500, "创建小程序通知任务失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Long> createWechatMpNotificationTask(String orderId, Long tenantId, String openid,
                                                             String templateId, Map<String, Object> templateData,
                                                             LocalDateTime scheduledTime) {
        log.info("[createWechatMpNotificationTask] API创建公众号通知任务，订单ID: {}, openid: {}", orderId, openid);

        try {
            Map<String, Object> taskData = new HashMap<>();
            taskData.put("openid", openid);
            taskData.put("templateId", templateId);
            taskData.put("data", templateData);

            Long taskId = notificationTaskService.createNotificationTask(
                    orderId, tenantId, NotificationTask.TaskType.WECHAT_MP.getCode(), scheduledTime, taskData);

            log.info("[createWechatMpNotificationTask] API创建公众号通知任务成功，任务ID: {}", taskId);
            return success(taskId);

        } catch (Exception e) {
            log.error("[createWechatMpNotificationTask] API创建公众号通知任务失败", e);
            return CommonResult.error(500, "创建公众号通知任务失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Long> createPhoneCallNotificationTask(String orderId, Long tenantId, String mobile,
                                                              String content, LocalDateTime scheduledTime) {
        log.info("[createPhoneCallNotificationTask] API创建电话通知任务，订单ID: {}, 手机号: {}", orderId, mobile);

        try {
            Map<String, Object> taskData = new HashMap<>();
            taskData.put("mobile", mobile);
            taskData.put("content", content);
            taskData.put("templateId", "phone_template");

            Long taskId = notificationTaskService.createNotificationTask(
                    orderId, tenantId, NotificationTask.TaskType.PHONE_CALL.getCode(), scheduledTime, taskData);

            log.info("[createPhoneCallNotificationTask] API创建电话通知任务成功，任务ID: {}", taskId);
            return success(taskId);

        } catch (Exception e) {
            log.error("[createPhoneCallNotificationTask] API创建电话通知任务失败", e);
            return CommonResult.error(500, "创建电话通知任务失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<Long>> batchCreateNotificationTasks(List<CreateNotificationTaskApiReqVO> reqVOList) {
        log.info("[batchCreateNotificationTasks] API批量创建通知任务，任务数量: {}", reqVOList.size());

        try {
            List<Long> taskIds = new ArrayList<>();
            for (CreateNotificationTaskApiReqVO reqVO : reqVOList) {
                Long taskId = notificationTaskService.createNotificationTask(
                        reqVO.getOrderId(),
                        reqVO.getTenantId(),
                        reqVO.getTaskType(),
                        reqVO.getScheduledTime(),
                        reqVO.getTaskData()
                );
                taskIds.add(taskId);
            }

            log.info("[batchCreateNotificationTasks] API批量创建通知任务成功，任务数量: {}", taskIds.size());
            return success(taskIds);

        } catch (Exception e) {
            log.error("[batchCreateNotificationTasks] API批量创建通知任务失败", e);
            return CommonResult.error(500, "批量创建通知任务失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Integer> cancelOrderNotificationTasks(String orderId, Long tenantId) {
        log.info("[cancelOrderNotificationTasks] API取消订单通知任务，订单ID: {}, 租户ID: {}", orderId, tenantId);

        try {
            int cancelledCount = notificationTaskMapper.cancelSubsequentTasks(orderId, tenantId, LocalDateTime.now());

            log.info("[cancelOrderNotificationTasks] API取消订单通知任务成功，取消数量: {}", cancelledCount);
            return success(cancelledCount);

        } catch (Exception e) {
            log.error("[cancelOrderNotificationTasks] API取消订单通知任务失败，订单ID: {}", orderId, e);
            return CommonResult.error(500, "取消订单通知任务失败: " + e.getMessage());
        }
    }

    /**
     * 转换为 API VO
     */
    private NotificationTaskApiVO convertToApiVO(NotificationTask task) {
        NotificationTaskApiVO vo = new NotificationTaskApiVO();
        BeanUtils.copyProperties(task, vo);
        return vo;
    }
}
