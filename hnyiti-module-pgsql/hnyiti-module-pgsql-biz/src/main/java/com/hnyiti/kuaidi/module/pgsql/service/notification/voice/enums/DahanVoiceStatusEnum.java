package com.hnyiti.kuaidi.module.pgsql.service.notification.voice.enums;

/**
 * 大汉三通语音状态码枚举
 *
 * <AUTHOR>
 */
public enum DahanVoiceStatusEnum {

    // ==================== 提交响应码 ====================
    SUCCESS("DH:0000", "成功"),
    ACCOUNT_INVALID("DH:1001", "账号无效"),
    PASSWORD_ERROR("DH:1002", "密码错误"),
    MSGID_TOO_LONG("DH:1003", "msgid太长"),
    INVALID_MOBILE("DH:1004", "存在无效号码/限制运营商号码"),
    MOBILE_COUNT_EXCEED("DH:1005", "号码个数超过最大限制"),
    CONTENT_TOO_LONG("DH:1006", "消息内容超过最大限制"),
    INSUFFICIENT_BALANCE("DH:1011", "账户余额不足"),
    CONTENT_SENSITIVE("DH:1010", "内容包含敏感词"),
    PLAYMODE_INVALID("DH:1023", "放音模式无效"),
    CALLTYPE_INVALID("DH:1024", "外呼类型无效"),
    MSGID_EMPTY("DH:1025", "msgid为空"),
    SYSTEM_BUSY("DH:1099", "系统繁忙"),

    // ==================== 呼叫状态码 ====================
    CALL_SUCCESS("DH:0000", "呼叫成功"),
    CALLEE_BUSY("DH:0001", "被叫忙【占线或繁忙】"),
    CALLEE_INVALID("DH:0002", "被叫空号"),
    CALL_TIMEOUT("DH:0003", "呼叫超时【无振铃】"),
    NO_ANSWER("DH:0004", "无人应答【有振铃，无人接听】"),
    PLAY_FAILED("DH:0005", "放音失败【放音文件不存在或格式不正确】"),
    NO_KEY_PRESSED("DH:0006", "没有接收到按键"),
    CALL_CONNECTED("DH:0007", "呼叫接通【正在放音，被叫挂机】"),
    CALL_CANCELLED("DH:0008", "取消呼叫【被叫还未接时，主叫挂机】"),
    CONTENT_SENSITIVE_CANCEL("DH:0009", "取消呼叫【放音文本含有敏感词】"),
    BLACKLIST_CANCEL("DH:0010", "取消呼叫【号码为黑名单】"),
    CALLEE_REJECT("DH:0017", "被叫拒接"),
    CALLEE_POWER_OFF("DH:0018", "被叫关机"),
    CALLEE_OUT_OF_SERVICE("DH:0019", "被叫停机"),
    CALLEE_NOT_IN_SERVICE("DH:0020", "被叫不在服务区"),
    CALL_FAILED("DH:9999", "呼叫失败");

    private final String code;
    private final String description;

    DahanVoiceStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static DahanVoiceStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (DahanVoiceStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        
        return null;
    }

    /**
     * 判断是否为成功状态
     *
     * @param code 状态码
     * @return 是否成功
     */
    public static boolean isSuccess(String code) {
        return SUCCESS.getCode().equals(code) || CALL_SUCCESS.getCode().equals(code);
    }

    /**
     * 判断是否为临时失败（可重试）
     *
     * @param code 状态码
     * @return 是否为临时失败
     */
    public static boolean isTemporaryFailure(String code) {
        return CALLEE_BUSY.getCode().equals(code) ||
               CALL_TIMEOUT.getCode().equals(code) ||
               NO_ANSWER.getCode().equals(code) ||
               CALLEE_POWER_OFF.getCode().equals(code) ||
               CALLEE_NOT_IN_SERVICE.getCode().equals(code) ||
               SYSTEM_BUSY.getCode().equals(code);
    }

    /**
     * 判断是否为永久失败（不可重试）
     *
     * @param code 状态码
     * @return 是否为永久失败
     */
    public static boolean isPermanentFailure(String code) {
        return CALLEE_INVALID.getCode().equals(code) ||
               INVALID_MOBILE.getCode().equals(code) ||
               BLACKLIST_CANCEL.getCode().equals(code) ||
               CONTENT_SENSITIVE_CANCEL.getCode().equals(code) ||
               ACCOUNT_INVALID.getCode().equals(code) ||
               PASSWORD_ERROR.getCode().equals(code);
    }

    @Override
    public String toString() {
        return String.format("%s: %s", code, description);
    }
}
