package com.hnyiti.kuaidi.module.pgsql.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalTime;
import java.util.Map;

/**
 * 通知配置
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "notification.compensation")
public class NotificationConfig {

    /**
     * 延时配置
     */
    private Delays delays = new Delays();

    /**
     * 工作时间配置
     */
    private WorkTime workTime = new WorkTime();

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    /**
     * 渠道开关配置
     */
    private Channels channels = new Channels();

    /**
     * 模板配置
     */
    private Templates templates = new Templates();

    @Data
    public static class Delays {
        /**
         * 短信延时时间（默认2小时）
         */
        private Duration sms = Duration.ofHours(2);

        /**
         * 电话延时时间（短信后6小时）
         */
        private Duration phone = Duration.ofHours(6);
    }

    @Data
    public static class WorkTime {
        /**
         * 工作开始时间（默认9:00）
         */
        private LocalTime start = LocalTime.of(9, 0);

        /**
         * 工作结束时间（默认24:00）
         */
        private LocalTime end = LocalTime.of(23, 59, 59);
    }

    @Data
    public static class Retry {
        /**
         * 最大重试次数（默认3次）
         */
        private Integer maxAttempts = 3;

        /**
         * 重试间隔（默认30分钟）
         */
        private Duration interval = Duration.ofMinutes(30);
    }

    @Data
    public static class Channels {
        /**
         * 小程序通知开关
         */
        private Boolean miniProgram = true;

        /**
         * 公众号通知开关
         */
        private Boolean wechatMp = true;

        /**
         * 短信通知开关
         */
        private Boolean sms = true;

        /**
         * 电话通知开关
         */
        private Boolean phoneCall = true;
    }

    @Data
    public static class Templates {
        /**
         * 小程序模板配置
         */
        private Map<String, String> miniProgram;

        /**
         * 公众号模板配置
         */
        private Map<String, String> wechatMp;

        /**
         * 短信模板配置
         */
        private Map<String, String> sms;

        /**
         * 电话模板配置
         */
        private Map<String, String> phoneCall;
    }

    /**
     * 检查是否在工作时间内
     */
    public boolean isWorkTime() {
        LocalTime now = LocalTime.now();
        return !now.isBefore(workTime.getStart()) && !now.isAfter(workTime.getEnd());
    }

    /**
     * 计算下一个工作时间
     */
    public LocalTime getNextWorkTime() {
        LocalTime now = LocalTime.now();
        
        if (now.isBefore(workTime.getStart())) {
            // 当前时间早于工作开始时间，返回今天的工作开始时间
            return workTime.getStart();
        } else {
            // 当前时间晚于工作结束时间，返回明天的工作开始时间
            return workTime.getStart();
        }
    }
}
