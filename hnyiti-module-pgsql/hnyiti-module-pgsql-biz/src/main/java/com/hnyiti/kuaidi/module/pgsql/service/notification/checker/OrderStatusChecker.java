package com.hnyiti.kuaidi.module.pgsql.service.notification.checker;

import com.hnyiti.kuaidi.module.pgsql.api.order.OrderCompensateApi;
import com.hnyiti.kuaidi.module.pgsql.api.order.dto.OrderCompensateDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单状态检查器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OrderStatusChecker {

    @Autowired
    private OrderCompensateApi orderCompensateApi;

    /**
     * 检查订单是否已完成补差
     *
     * @param orderId 订单ID
     * @return 是否已完成补差
     */
    public boolean isOrderCompensated(String orderId) {
        try {
            // 调用API查询最新的补差记录
            OrderCompensateDTO compensate = orderCompensateApi.getLatestByOrderId(orderId);
            
            if (compensate == null) {
                log.debug("[isOrderCompensated] 订单无补差记录，订单ID: {}", orderId);
                return false;
            }
            
            // 检查补差结果是否为已完成（状态码：3）
            boolean isCompleted = "3".equals(compensate.getCompensatingResult());
            
            log.debug("[isOrderCompensated] 订单补差状态检查，订单ID: {}, 补差结果: {}, 是否已完成: {}", 
                    orderId, compensate.getCompensatingResult(), isCompleted);
            
            return isCompleted;
            
        } catch (Exception e) {
            log.error("[isOrderCompensated] 检查订单补差状态异常，订单ID: {}", orderId, e);
            // 异常情况下返回false，继续执行通知
            return false;
        }
    }

    /**
     * 获取订单补差信息
     *
     * @param orderId 订单ID
     * @return 补差信息
     */
    public OrderCompensateDTO getOrderCompensateInfo(String orderId) {
        try {
            return orderCompensateApi.getLatestByOrderId(orderId);
        } catch (Exception e) {
            log.error("[getOrderCompensateInfo] 获取订单补差信息异常，订单ID: {}", orderId, e);
            return null;
        }
    }
}
