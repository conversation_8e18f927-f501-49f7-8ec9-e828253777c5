package com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知记录表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "notification_record", autoResultMap = true)
public class NotificationRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 通知类型：MINI_PROGRAM, WECHAT_MP, SMS, PHONE_CALL
     */
    private String notificationType;

    /**
     * 接收人（openid、手机号等）
     */
    private String recipient;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 发送状态：SUCCESS, FAILED, PENDING
     */
    private String sendStatus;

    /**
     * 发送时间
     */
    private LocalDateTime sendTime;

    /**
     * 第三方响应数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> responseData;

    /**
     * 错误信息
     */
    private String errorMessage;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 发送状态枚举
     */
    public enum SendStatus {
        SUCCESS("SUCCESS", "发送成功"),
        FAILED("FAILED", "发送失败"),
        PENDING("PENDING", "发送中");

        private final String code;
        private final String name;

        SendStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 通知类型枚举
     */
    public enum NotificationType {
        MINI_PROGRAM("MINI_PROGRAM", "小程序通知"),
        WECHAT_MP("WECHAT_MP", "公众号通知"),
        SMS("SMS", "短信通知"),
        PHONE_CALL("PHONE_CALL", "电话通知");

        private final String code;
        private final String name;

        NotificationType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
