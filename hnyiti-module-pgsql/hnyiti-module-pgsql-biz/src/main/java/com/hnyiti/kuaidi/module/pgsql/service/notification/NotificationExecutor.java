package com.hnyiti.kuaidi.module.pgsql.service.notification;

import com.hnyiti.kuaidi.module.pgsql.config.NotificationConfig;
import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.dal.mapper.notification.NotificationTaskMapper;
import com.hnyiti.kuaidi.module.pgsql.mq.producer.NotificationProducer;
import com.hnyiti.kuaidi.module.pgsql.service.notification.checker.OrderStatusChecker;
import com.hnyiti.kuaidi.module.pgsql.service.notification.checker.PreviousNotificationChecker;
import com.hnyiti.kuaidi.module.pgsql.service.notification.checker.WorkTimeChecker;
import com.hnyiti.kuaidi.module.pgsql.service.notification.channel.NotificationChannelAdapter;
import com.hnyiti.kuaidi.module.pgsql.service.notification.model.NotificationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 通知执行器
 * 负责执行具体的通知任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NotificationExecutor {

    @Autowired
    private NotificationTaskMapper notificationTaskMapper;

    @Autowired
    private NotificationChannelAdapter channelAdapter;

    @Autowired
    private NotificationRecordService notificationRecordService;

    @Autowired
    private OrderStatusChecker orderStatusChecker;

    @Autowired
    private WorkTimeChecker workTimeChecker;

    @Autowired
    private PreviousNotificationChecker previousNotificationChecker;

    @Autowired
    private NotificationProducer notificationProducer;

    @Autowired
    private NotificationConfig notificationConfig;

    /**
     * 执行通知任务
     *
     * @param task 通知任务
     */
    public void executeNotification(NotificationTask task) {
        log.info("[executeNotification] 开始执行通知任务，任务ID: {}, 订单ID: {}, 类型: {}", 
                task.getId(), task.getOrderId(), task.getTaskType());

        try {
            // 更新任务状态为执行中
            updateTaskStatus(task.getId(), NotificationTask.TaskStatus.EXECUTING.getCode(), LocalDateTime.now(), null);

            // 1. 检查订单状态
            if (orderStatusChecker.isOrderCompensated(task.getOrderId())) {
                log.info("[executeNotification] 订单已完成补差，取消后续任务，订单ID: {}", task.getOrderId());
                cancelSubsequentTasks(task.getOrderId(), task.getTenantId());
                updateTaskStatus(task.getId(), NotificationTask.TaskStatus.CANCELLED.getCode(), LocalDateTime.now(), 
                        createResultData("订单已完成补差", false));
                return;
            }

            // 2. 检查工作时间（短信和电话）
            if (needWorkTimeCheck(task.getTaskType()) && !workTimeChecker.isWorkTime()) {
                log.info("[executeNotification] 当前不在工作时间内，重新安排任务，任务ID: {}", task.getId());
                rescheduleToNextWorkTime(task);
                return;
            }

            // 3. 检查前序通知（电话）
            if (NotificationTask.TaskType.PHONE_CALL.getCode().equals(task.getTaskType()) && 
                !previousNotificationChecker.isPreviousNotificationsCompleted(task.getOrderId(), task.getTenantId())) {
                log.info("[executeNotification] 前序通知未完成，延后执行电话任务，任务ID: {}", task.getId());
                rescheduleTask(task, notificationConfig.getRetry().getInterval().toMinutes());
                return;
            }

            // 4. 执行通知
            NotificationResult result = channelAdapter.sendNotification(task);

            // 5. 保存通知记录
            notificationRecordService.saveRecord(task, result);

            // 6. 更新任务状态
            if (result.isSuccess()) {
                updateTaskStatus(task.getId(), NotificationTask.TaskStatus.COMPLETED.getCode(), 
                        LocalDateTime.now(), createResultData("发送成功", true));
                log.info("[executeNotification] 通知发送成功，任务ID: {}", task.getId());
            } else {
                handleNotificationFailure(task, result);
            }

        } catch (Exception e) {
            log.error("[executeNotification] 执行通知任务异常，任务ID: {}", task.getId(), e);
            handleNotificationException(task, e);
        }
    }

    /**
     * 处理通知发送失败
     */
    private void handleNotificationFailure(NotificationTask task, NotificationResult result) {
        log.warn("[handleNotificationFailure] 通知发送失败，任务ID: {}, 错误: {}", task.getId(), result.getErrorMessage());

        // 检查是否需要重试
        if (task.getRetryCount() < task.getMaxRetryCount()) {
            // 安排重试
            scheduleRetry(task, result.getErrorMessage());
        } else {
            // 重试次数已达上限，标记为失败
            updateTaskStatus(task.getId(), NotificationTask.TaskStatus.FAILED.getCode(), 
                    LocalDateTime.now(), createResultData("重试次数已达上限: " + result.getErrorMessage(), false));
        }
    }

    /**
     * 处理通知执行异常
     */
    private void handleNotificationException(NotificationTask task, Exception e) {
        log.error("[handleNotificationException] 通知执行异常，任务ID: {}", task.getId(), e);

        // 检查是否需要重试
        if (task.getRetryCount() < task.getMaxRetryCount()) {
            // 安排重试
            scheduleRetry(task, "执行异常: " + e.getMessage());
        } else {
            // 重试次数已达上限，标记为失败
            updateTaskStatus(task.getId(), NotificationTask.TaskStatus.FAILED.getCode(), 
                    LocalDateTime.now(), createResultData("执行异常，重试次数已达上限: " + e.getMessage(), false));
        }
    }

    /**
     * 安排重试
     */
    private void scheduleRetry(NotificationTask task, String errorMessage) {
        int newRetryCount = task.getRetryCount() + 1;
        LocalDateTime nextRetryTime = LocalDateTime.now().plus(notificationConfig.getRetry().getInterval());

        // 更新重试信息
        notificationTaskMapper.updateRetryInfo(task.getId(), newRetryCount, nextRetryTime);

        // 发送延时重试消息
        task.setRetryCount(newRetryCount);
        task.setNextRetryTime(nextRetryTime);
        notificationProducer.sendDelayedNotification(task, notificationConfig.getRetry().getInterval().toMillis());

        log.info("[scheduleRetry] 安排重试，任务ID: {}, 重试次数: {}, 下次重试时间: {}, 错误: {}", 
                task.getId(), newRetryCount, nextRetryTime, errorMessage);
    }

    /**
     * 重新安排到下个工作时间
     */
    private void rescheduleToNextWorkTime(NotificationTask task) {
        LocalDateTime nextWorkTime = workTimeChecker.getNextWorkTime();
        
        // 更新计划执行时间
        task.setScheduledTime(nextWorkTime);
        notificationTaskMapper.updateById(task);

        // 计算延时时间并发送延时消息
        long delayMillis = java.time.Duration.between(LocalDateTime.now(), nextWorkTime).toMillis();
        notificationProducer.sendDelayedNotification(task, delayMillis);

        log.info("[rescheduleToNextWorkTime] 重新安排到工作时间，任务ID: {}, 新的执行时间: {}", 
                task.getId(), nextWorkTime);
    }

    /**
     * 重新安排任务
     */
    private void rescheduleTask(NotificationTask task, long delayMinutes) {
        LocalDateTime newScheduledTime = LocalDateTime.now().plusMinutes(delayMinutes);
        
        // 更新计划执行时间
        task.setScheduledTime(newScheduledTime);
        notificationTaskMapper.updateById(task);

        // 发送延时消息
        notificationProducer.sendDelayedNotification(task, delayMinutes * 60 * 1000);

        log.info("[rescheduleTask] 重新安排任务，任务ID: {}, 延时: {} 分钟, 新的执行时间: {}", 
                task.getId(), delayMinutes, newScheduledTime);
    }

    /**
     * 检查是否需要工作时间检查
     */
    private boolean needWorkTimeCheck(String taskType) {
        return NotificationTask.TaskType.SMS.getCode().equals(taskType) || 
               NotificationTask.TaskType.PHONE_CALL.getCode().equals(taskType);
    }

    /**
     * 取消后续任务
     */
    private void cancelSubsequentTasks(String orderId, Long tenantId) {
        notificationTaskMapper.cancelSubsequentTasks(orderId, tenantId, LocalDateTime.now());
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Long taskId, String status, LocalDateTime executeTime, Map<String, Object> resultData) {
        String resultDataJson = resultData != null ? com.alibaba.fastjson.JSON.toJSONString(resultData) : null;
        notificationTaskMapper.updateTaskStatus(taskId, status, executeTime, resultDataJson);
    }

    /**
     * 创建结果数据
     */
    private Map<String, Object> createResultData(String message, boolean success) {
        Map<String, Object> resultData = new HashMap<>();
        resultData.put("message", message);
        resultData.put("success", success);
        resultData.put("timestamp", LocalDateTime.now().toString());
        return resultData;
    }
}
