<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnyiti.kuaidi.module.pgsql.dal.mapper.notification.NotificationTaskMapper">

    <!-- 查询待执行的任务 -->
    <select id="selectPendingTasks" resultType="com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask">
        SELECT *
        FROM notification_task
        WHERE task_status = 'PENDING'
          AND scheduled_time &lt;= #{currentTime}
        ORDER BY scheduled_time ASC
        LIMIT #{limit}
    </select>

    <!-- 查询需要重试的任务 -->
    <select id="selectRetryTasks" resultType="com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask">
        SELECT *
        FROM notification_task
        WHERE task_status = 'FAILED'
          AND retry_count &lt; max_retry_count
          AND next_retry_time &lt;= #{currentTime}
        ORDER BY next_retry_time ASC
        LIMIT #{limit}
    </select>

    <!-- 根据订单ID查询任务 -->
    <select id="selectByOrderId" resultType="com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask">
        SELECT *
        FROM notification_task
        WHERE order_id = #{orderId}
          AND tenant_id = #{tenantId}
        ORDER BY create_time ASC
    </select>

    <!-- 取消订单的后续任务 -->
    <update id="cancelSubsequentTasks">
        UPDATE notification_task
        SET task_status = 'CANCELLED',
            update_time = #{currentTime}
        WHERE order_id = #{orderId}
          AND tenant_id = #{tenantId}
          AND task_status IN ('PENDING', 'FAILED')
    </update>

    <!-- 更新任务状态 -->
    <update id="updateTaskStatus">
        UPDATE notification_task
        SET task_status = #{status},
            actual_execute_time = #{executeTime},
            result_data = #{resultData},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新重试信息 -->
    <update id="updateRetryInfo">
        UPDATE notification_task
        SET retry_count = #{retryCount},
            next_retry_time = #{nextRetryTime},
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
