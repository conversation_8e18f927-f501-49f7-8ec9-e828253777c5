-- 多阶段通知系统数据库表结构

-- 通知任务表
CREATE TABLE notification_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    order_id VARCHAR(64) NOT NULL COMMENT '订单ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    task_type VARCHAR(32) NOT NULL COMMENT '任务类型：MINI_PROGRAM, WECHAT_MP, SMS, PHONE_CALL',
    task_status VARCHAR(32) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态：PENDING, EXECUTING, COMPLETED, CANCELLED, FAILED',
    scheduled_time DATETIME NOT NULL COMMENT '计划执行时间',
    actual_execute_time DATETIME NULL COMMENT '实际执行时间',
    retry_count INT NOT NULL DEFAULT 0 COMMENT '重试次数',
    max_retry_count INT NOT NULL DEFAULT 3 COMMENT '最大重试次数',
    next_retry_time DATETIME NULL COMMENT '下次重试时间',
    task_data JSON NULL COMMENT '任务数据（收件人、内容等）',
    result_data JSON NULL COMMENT '执行结果数据',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_order_tenant (order_id, tenant_id),
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_status_type (task_status, task_type),
    INDEX idx_retry_time (next_retry_time),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知任务表';

-- 通知记录表
CREATE TABLE notification_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    order_id VARCHAR(64) NOT NULL COMMENT '订单ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    notification_type VARCHAR(32) NOT NULL COMMENT '通知类型：MINI_PROGRAM, WECHAT_MP, SMS, PHONE_CALL',
    recipient VARCHAR(128) NOT NULL COMMENT '接收人（openid、手机号等）',
    content TEXT NULL COMMENT '通知内容',
    send_status VARCHAR(32) NOT NULL COMMENT '发送状态：SUCCESS, FAILED, PENDING',
    send_time DATETIME NULL COMMENT '发送时间',
    response_data JSON NULL COMMENT '第三方响应数据',
    error_message VARCHAR(512) NULL COMMENT '错误信息',
    cost_amount DECIMAL(10,4) NULL COMMENT '费用（短信、电话）',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_order_tenant (order_id, tenant_id),
    INDEX idx_type_status (notification_type, send_status),
    INDEX idx_send_time (send_time),
    INDEX idx_recipient (recipient),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知记录表';

-- 插入初始化数据（可选）
INSERT INTO notification_task (order_id, tenant_id, task_type, task_status, scheduled_time, task_data) VALUES
('TEST_ORDER_001', 1, 'MINI_PROGRAM', 'PENDING', NOW(), '{"openid": "test_openid", "templateId": "test_template"}');

-- 创建视图：通知统计视图
CREATE VIEW v_notification_statistics AS
SELECT 
    order_id,
    tenant_id,
    COUNT(*) as total_notifications,
    SUM(CASE WHEN send_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN send_status = 'FAILED' THEN 1 ELSE 0 END) as failed_count,
    SUM(CASE WHEN notification_type = 'MINI_PROGRAM' AND send_status = 'SUCCESS' THEN 1 ELSE 0 END) as mini_program_success,
    SUM(CASE WHEN notification_type = 'WECHAT_MP' AND send_status = 'SUCCESS' THEN 1 ELSE 0 END) as wechat_mp_success,
    SUM(CASE WHEN notification_type = 'SMS' AND send_status = 'SUCCESS' THEN 1 ELSE 0 END) as sms_success,
    SUM(CASE WHEN notification_type = 'PHONE_CALL' AND send_status = 'SUCCESS' THEN 1 ELSE 0 END) as phone_call_success,
    COALESCE(SUM(cost_amount), 0) as total_cost,
    MIN(create_time) as first_notification_time,
    MAX(create_time) as last_notification_time
FROM notification_record 
GROUP BY order_id, tenant_id;

-- 创建存储过程：清理过期数据
DELIMITER //
CREATE PROCEDURE CleanExpiredNotificationData(IN days_to_keep INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 删除过期的通知记录（保留指定天数）
    DELETE FROM notification_record 
    WHERE create_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- 删除过期的已完成任务（保留指定天数）
    DELETE FROM notification_task 
    WHERE create_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY)
    AND task_status IN ('COMPLETED', 'CANCELLED', 'FAILED');
    
    COMMIT;
    
    SELECT ROW_COUNT() as affected_rows;
END //
DELIMITER ;

-- 创建定时清理事件（可选，需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT IF NOT EXISTS evt_clean_notification_data
-- ON SCHEDULE EVERY 1 DAY
-- STARTS TIMESTAMP(CURRENT_DATE, '02:00:00')
-- DO
--   CALL CleanExpiredNotificationData(30); -- 保留30天数据
