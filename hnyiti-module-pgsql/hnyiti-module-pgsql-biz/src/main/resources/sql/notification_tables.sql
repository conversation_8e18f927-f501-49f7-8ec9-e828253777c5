-- 多阶段通知系统数据库表结构 (PostgreSQL)

-- 通知任务表
CREATE TABLE notification_task
(
    id                  BIGSERIAL PRIMARY KEY,
    order_id            VARCHAR(64) NOT NULL,
    tenant_id           BIGINT      NOT NULL,
    task_type           VARCHAR(32) NOT NULL,                           -- 任务类型：MINI_PROGRAM, WECHAT_MP, SMS, PHONE_CALL
    task_status         VARCHAR(32) NOT NULL DEFAULT 'PENDING',         -- 任务状态：PENDING, EXECUTING, COMPLETED, CANCELLED, FAILED
    scheduled_time      TIMESTAMP   NOT NULL,                           -- 计划执行时间
    actual_execute_time TIMESTAMP NULL,                                 -- 实际执行时间
    retry_count         INTEGER     NOT NULL DEFAULT 0,                 -- 重试次数
    max_retry_count     INTEGER     NOT NULL DEFAULT 3,                 -- 最大重试次数
    next_retry_time     TIMESTAMP NULL,                                 -- 下次重试时间
    task_data           JSONB NULL,                                     -- 任务数据（收件人、内容等）
    result_data         JSONB NULL,                                     -- 执行结果数据
    create_time         TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_time         TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 添加表注释
COMMENT
ON TABLE notification_task IS '通知任务表';
COMMENT
ON COLUMN notification_task.id IS '主键ID';
COMMENT
ON COLUMN notification_task.order_id IS '订单ID';
COMMENT
ON COLUMN notification_task.tenant_id IS '租户ID';
COMMENT
ON COLUMN notification_task.task_type IS '任务类型：MINI_PROGRAM, WECHAT_MP, SMS, PHONE_CALL';
COMMENT
ON COLUMN notification_task.task_status IS '任务状态：PENDING, EXECUTING, COMPLETED, CANCELLED, FAILED';
COMMENT
ON COLUMN notification_task.scheduled_time IS '计划执行时间';
COMMENT
ON COLUMN notification_task.actual_execute_time IS '实际执行时间';
COMMENT
ON COLUMN notification_task.retry_count IS '重试次数';
COMMENT
ON COLUMN notification_task.max_retry_count IS '最大重试次数';
COMMENT
ON COLUMN notification_task.next_retry_time IS '下次重试时间';
COMMENT
ON COLUMN notification_task.task_data IS '任务数据（收件人、内容等）';
COMMENT
ON COLUMN notification_task.result_data IS '执行结果数据';
COMMENT
ON COLUMN notification_task.create_time IS '创建时间';
COMMENT
ON COLUMN notification_task.update_time IS '更新时间';

-- 创建索引
CREATE INDEX idx_notification_task_order_tenant ON notification_task (order_id, tenant_id);
CREATE INDEX idx_notification_task_scheduled_time ON notification_task (scheduled_time);
CREATE INDEX idx_notification_task_status_type ON notification_task (task_status, task_type);
CREATE INDEX idx_notification_task_retry_time ON notification_task (next_retry_time);
CREATE INDEX idx_notification_task_create_time ON notification_task (create_time);

-- 通知记录表
CREATE TABLE notification_record
(
    id                BIGSERIAL PRIMARY KEY,
    order_id          VARCHAR(64)  NOT NULL,
    tenant_id         BIGINT       NOT NULL,
    notification_type VARCHAR(32)  NOT NULL,                          -- 通知类型：MINI_PROGRAM, WECHAT_MP, SMS, PHONE_CALL
    recipient         VARCHAR(128) NOT NULL,                          -- 接收人（openid、手机号等）
    content           TEXT NULL,                                      -- 通知内容
    send_status       VARCHAR(32)  NOT NULL,                          -- 发送状态：SUCCESS, FAILED, PENDING
    send_time         TIMESTAMP NULL,                                 -- 发送时间
    response_data     JSONB NULL,                                     -- 第三方响应数据
    error_message     VARCHAR(512) NULL,                              -- 错误信息

    create_time       TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 添加表注释
COMMENT
ON TABLE notification_record IS '通知记录表';
COMMENT
ON COLUMN notification_record.id IS '主键ID';
COMMENT
ON COLUMN notification_record.order_id IS '订单ID';
COMMENT
ON COLUMN notification_record.tenant_id IS '租户ID';
COMMENT
ON COLUMN notification_record.notification_type IS '通知类型：MINI_PROGRAM, WECHAT_MP, SMS, PHONE_CALL';
COMMENT
ON COLUMN notification_record.recipient IS '接收人（openid、手机号等）';
COMMENT
ON COLUMN notification_record.content IS '通知内容';
COMMENT
ON COLUMN notification_record.send_status IS '发送状态：SUCCESS, FAILED, PENDING';
COMMENT
ON COLUMN notification_record.send_time IS '发送时间';
COMMENT
ON COLUMN notification_record.response_data IS '第三方响应数据';
COMMENT
ON COLUMN notification_record.error_message IS '错误信息';

COMMENT
ON COLUMN notification_record.create_time IS '创建时间';

-- 创建索引
CREATE INDEX idx_notification_record_order_tenant ON notification_record (order_id, tenant_id);
CREATE INDEX idx_notification_record_type_status ON notification_record (notification_type, send_status);
CREATE INDEX idx_notification_record_send_time ON notification_record (send_time);
CREATE INDEX idx_notification_record_recipient ON notification_record (recipient);
CREATE INDEX idx_notification_record_create_time ON notification_record (create_time);

-- 插入初始化数据（可选）
INSERT INTO notification_task (order_id, tenant_id, task_type, task_status, scheduled_time, task_data)
VALUES ('TEST_ORDER_001', 1, 'MINI_PROGRAM', 'PENDING', NOW(), '{
  "openid": "test_openid",
  "templateId": "test_template"
}'::jsonb);

-- 创建视图：通知统计视图
CREATE VIEW v_notification_statistics AS
SELECT order_id,
       tenant_id,
       COUNT(*)                                                                               as total_notifications,
       SUM(CASE WHEN send_status = 'SUCCESS' THEN 1 ELSE 0 END)                               as success_count,
       SUM(CASE WHEN send_status = 'FAILED' THEN 1 ELSE 0 END)                                as failed_count,
       SUM(CASE
               WHEN notification_type = 'MINI_PROGRAM' AND send_status = 'SUCCESS' THEN 1
               ELSE 0 END)                                                                    as mini_program_success,
       SUM(CASE
               WHEN notification_type = 'WECHAT_MP' AND send_status = 'SUCCESS' THEN 1
               ELSE 0 END)                                                                    as wechat_mp_success,
       SUM(CASE WHEN notification_type = 'SMS' AND send_status = 'SUCCESS' THEN 1 ELSE 0 END) as sms_success,
       SUM(CASE
               WHEN notification_type = 'PHONE_CALL' AND send_status = 'SUCCESS' THEN 1
               ELSE 0 END)                                                                    as phone_call_success,

       MIN(create_time)                                                                       as first_notification_time,
       MAX(create_time)                                                                       as last_notification_time
FROM notification_record
GROUP BY order_id, tenant_id;

-- 创建函数：清理过期数据
CREATE
OR REPLACE FUNCTION clean_expired_notification_data(days_to_keep INTEGER)
RETURNS TABLE(affected_records INTEGER, affected_tasks INTEGER) AS $$
DECLARE
record_count INTEGER;
    task_count
INTEGER;
BEGIN
    -- 删除过期的通知记录（保留指定天数）
DELETE
FROM notification_record
WHERE create_time < NOW() - INTERVAL '1 day' * days_to_keep;

GET DIAGNOSTICS record_count = ROW_COUNT;

-- 删除过期的已完成任务（保留指定天数）
DELETE
FROM notification_task
WHERE create_time < NOW() - INTERVAL '1 day' * days_to_keep
  AND task_status IN ('COMPLETED'
    , 'CANCELLED'
    , 'FAILED');

GET DIAGNOSTICS task_count = ROW_COUNT;

-- 返回影响的行数
RETURN QUERY SELECT record_count, task_count;
END;
$$
LANGUAGE plpgsql;

-- 创建更新时间触发器函数
CREATE
OR REPLACE FUNCTION update_notification_task_updated_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time
= CURRENT_TIMESTAMP;
RETURN NEW;
END;
$$
LANGUAGE plpgsql;

-- 为 notification_task 表创建更新时间触发器
CREATE TRIGGER trigger_update_notification_task_time
    BEFORE UPDATE
    ON notification_task
    FOR EACH ROW
    EXECUTE FUNCTION update_notification_task_updated_time();

-- 创建定时清理任务的示例（需要使用 pg_cron 扩展）
-- 注意：需要先安装和启用 pg_cron 扩展
-- CREATE EXTENSION IF NOT EXISTS pg_cron;
--
-- -- 每天凌晨2点清理30天前的数据
-- SELECT cron.schedule('clean-notification-data', '0 2 * * *', 'SELECT clean_expired_notification_data(30);');

-- 手动清理数据的示例用法：
-- SELECT * FROM clean_expired_notification_data(30); -- 清理30天前的数据
