package com.hnyiti.kuaidi.module.system.service.auth;

import cn.hutool.core.util.StrUtil;
import com.hnyiti.kuaidi.framework.ip.core.Area;
import com.hnyiti.kuaidi.framework.ip.core.utils.AreaUtils;
import com.hnyiti.kuaidi.framework.ip.core.utils.IPUtils;
import com.hnyiti.kuaidi.module.system.enums.logger.LoginLogTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录通知服务
 * 负责异步发送登录通知到企业微信
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LoginNotificationService {

    @Resource
    private RestTemplate restTemplate;

    /**
     * 企业微信机器人 Webhook URL
     */
    private static final String WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5c8432d4-d951-42f8-8806-fddfc5171a61";

    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 异步发送登录通知到企业微信
     *
     * @param username 用户名/手机号
     * @param loginType 登录方式
     * @param userIp 用户IP
     */
    @Async
    public void sendLoginNotification(String username, LoginLogTypeEnum loginType, String userIp) {
        try {
            // 构建登录通知消息
            String message = buildLoginMessage(username, loginType, userIp);

            // 发送到企业微信
            sendToWeChat(message);

            log.info("[sendLoginNotification][登录通知发送成功] username:{} loginType:{} ip:{}",
                    username, loginType.name(), userIp);

        } catch (Exception e) {
            // 异常不影响登录流程，只记录日志
            log.error("[sendLoginNotification][登录通知发送失败] username:{} loginType:{} ip:{}",
                    username, loginType.name(), userIp, e);
        }
    }

    /**
     * 构建登录消息内容
     *
     * @param username 用户名
     * @param loginType 登录类型
     * @param userIp 用户IP
     * @return 消息内容
     */
    private String buildLoginMessage(String username, LoginLogTypeEnum loginType, String userIp) {
        // 获取IP对应的地区信息
        String location = getLocationByIp(userIp);

        // 获取登录方式描述
        String loginMethod = getLoginMethodDescription(loginType);

        // 获取当前时间
        String loginTime = LocalDateTime.now().format(FORMATTER);

        // 构建消息内容
        return String.format(
                "🔐 用户登录通知\n" +
                "账号：%s\n" +
                "IP地址：%s\n" +
                "地区：%s\n" +
                "登录方式：%s\n" +
                "登录时间：%s",
                username, userIp, location, loginMethod, loginTime
        );
    }

    /**
     * 根据IP获取地区信息
     *
     * @param ip IP地址
     * @return 地区信息
     */
    private String getLocationByIp(String ip) {
        try {
            if (StrUtil.isBlank(ip)) {
                return "未知";
            }

            // 使用框架提供的IP工具类获取地区
            Area area = IPUtils.getArea(ip);
            if (area == null) {
                return "未知";
            }

            // 格式化地区信息
            return AreaUtils.format(area.getId());

        } catch (Exception e) {
            log.warn("[getLocationByIp][IP地址解析失败] ip:{}", ip, e);
            return "未知";
        }
    }

    /**
     * 获取登录方式描述
     *
     * @param loginType 登录类型
     * @return 登录方式描述
     */
    private String getLoginMethodDescription(LoginLogTypeEnum loginType) {
        switch (loginType) {
            case LOGIN_USERNAME:
                return "账号密码登录";
            case LOGIN_MOBILE:
                return "手机号登录";
            case LOGIN_SMS:
                return "短信验证码登录";
            case LOGIN_SOCIAL:
                return "社交登录";
            default:
                return "其他方式登录";
        }
    }

    /**
     * 发送消息到企业微信
     *
     * @param message 消息内容
     */
    private void sendToWeChat(String message) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("msgtype", "text");

            Map<String, String> textContent = new HashMap<>();
            textContent.put("content", message);
            requestBody.put("text", textContent);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String response = restTemplate.postForObject(WEBHOOK_URL, requestEntity, String.class);

            log.debug("[sendToWeChat][企业微信响应] response:{}", response);

        } catch (Exception e) {
            log.error("[sendToWeChat][发送企业微信消息失败] message:{}", message, e);
            throw e;
        }
    }
}
