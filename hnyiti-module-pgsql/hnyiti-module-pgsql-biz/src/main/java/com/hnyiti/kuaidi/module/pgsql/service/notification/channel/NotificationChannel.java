package com.hnyiti.kuaidi.module.pgsql.service.notification.channel;

import com.hnyiti.kuaidi.module.pgsql.dal.dataobject.notification.NotificationTask;
import com.hnyiti.kuaidi.module.pgsql.service.notification.model.NotificationResult;

/**
 * 通知渠道接口
 *
 * <AUTHOR>
 */
public interface NotificationChannel {

    /**
     * 发送通知
     *
     * @param task 通知任务
     * @return 发送结果
     */
    NotificationResult send(NotificationTask task);

    /**
     * 检查渠道是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();

    /**
     * 获取渠道类型
     *
     * @return 渠道类型
     */
    String getChannelType();
}
